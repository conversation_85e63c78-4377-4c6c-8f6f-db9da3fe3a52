/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/[slug]/page";
exports.ids = ["app/[slug]/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Bslug%5D%2Fpage&page=%2F%5Bslug%5D%2Fpage&appPaths=%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2F%5Bslug%5D%2Fpage.js&appDir=D%3A%5CLaptop%20data%5CDRIPLY-CHAT%5Cchat-next%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CLaptop%20data%5CDRIPLY-CHAT%5Cchat-next&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Bslug%5D%2Fpage&page=%2F%5Bslug%5D%2Fpage&appPaths=%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2F%5Bslug%5D%2Fpage.js&appDir=D%3A%5CLaptop%20data%5CDRIPLY-CHAT%5Cchat-next%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CLaptop%20data%5CDRIPLY-CHAT%5Cchat-next&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.js */ \"(rsc)/./src/app/layout.js\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[slug]/page.js */ \"(rsc)/./src/app/[slug]/page.js\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '[slug]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\app\\\\[slug]\\\\page.js\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\app\\\\layout.js\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\app\\\\[slug]\\\\page.js\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/[slug]/page\",\n        pathname: \"/[slug]\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Bslug%5D%2Fpage&page=%2F%5Bslug%5D%2Fpage&appPaths=%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2F%5Bslug%5D%2Fpage.js&appDir=D%3A%5CLaptop%20data%5CDRIPLY-CHAT%5Cchat-next%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CLaptop%20data%5CDRIPLY-CHAT%5Cchat-next&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Wix_Madefor_Text%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-wix-madefor-text%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22wixMadeforText%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CClientWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Wix_Madefor_Text%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-wix-madefor-text%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22wixMadeforText%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CClientWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ClientWrapper.jsx */ \"(rsc)/./src/components/ClientWrapper.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Wix_Madefor_Text%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-wix-madefor-text%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22wixMadeforText%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CClientWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CChatInterfaceWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CChatInterfaceWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ChatInterfaceWrapper.jsx */ \"(rsc)/./src/components/ChatInterfaceWrapper.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNMYXB0b3AlMjBkYXRhJTVDJTVDRFJJUExZLUNIQVQlNUMlNUNjaGF0LW5leHQlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDQ2hhdEludGVyZmFjZVdyYXBwZXIuanN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOExBQW9KIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiRDpcXFxcTGFwdG9wIGRhdGFcXFxcRFJJUExZLUNIQVRcXFxcY2hhdC1uZXh0XFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXENoYXRJbnRlcmZhY2VXcmFwcGVyLmpzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CChatInterfaceWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxMYXB0b3AgZGF0YVxcRFJJUExZLUNIQVRcXGNoYXQtbmV4dFxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/[slug]/page.js":
/*!********************************!*\
  !*** ./src/app/[slug]/page.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   generateMetadata: () => (/* binding */ generateMetadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ChatInterfaceWrapper__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/ChatInterfaceWrapper */ \"(rsc)/./src/components/ChatInterfaceWrapper.jsx\");\n\n\n\n// Dynamic metadata generation using your API response data\nasync function generateMetadata({ params }) {\n    const { slug } = await params;\n    // Default fallback metadata\n    let title = `Chat with ${slug} - Driply`;\n    let description = `Start a conversation with ${slug} on Driply platform`;\n    let keywords = [\n        'chat',\n        slug,\n        'driply',\n        'ai',\n        'conversation'\n    ];\n    let businessName = 'Driply';\n    let image = '/og-image.jpg';\n    try {\n        // Fetch the same data that your ChatContext uses\n        const response = await fetch(`${\"https://api-develop.driply.me/\" || 0}/api/chat/settings?customerName=${slug}`, {\n            method: 'GET',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            // Cache for better performance\n            next: {\n                revalidate: 300\n            }\n        });\n        if (response.ok) {\n            const data = await response.json();\n            console.log('[Metadata] API Response:', data);\n            if (data && data.customerName) {\n                // Extract data from your API response structure\n                businessName = data.businessName || 'Driply';\n                const metaData = data.metaData || {};\n                // Use the metadata from your API response\n                title = metaData.title || `${businessName} Chat - ${slug}`;\n                description = metaData.description || `Chat with ${businessName} - Powered by Driply`;\n                // Handle keywords - convert string to array if needed\n                if (metaData.keywords) {\n                    keywords = typeof metaData.keywords === 'string' ? metaData.keywords.split(',').map((k)=>k.trim()) : Array.isArray(metaData.keywords) ? metaData.keywords : [\n                        metaData.keywords\n                    ];\n                } else {\n                    keywords = [\n                        'chat',\n                        slug,\n                        businessName.toLowerCase(),\n                        'driply',\n                        'ai',\n                        'conversation'\n                    ];\n                }\n                // Use custom image if provided\n                if (metaData.image && metaData.image.trim()) {\n                    image = metaData.image;\n                }\n                console.log('[Metadata] Using dynamic metadata:', {\n                    title,\n                    description,\n                    keywords,\n                    businessName,\n                    image\n                });\n            }\n        } else {\n            console.warn('[Metadata] API request failed, using fallback metadata');\n        }\n    } catch (error) {\n        console.warn('[Metadata] Error fetching dynamic metadata:', error.message);\n    // Will use fallback metadata\n    }\n    // Return the complete metadata object\n    return {\n        title: title,\n        description: description,\n        keywords: keywords,\n        openGraph: {\n            title: title,\n            description: description,\n            type: 'website',\n            url: `https://yourdomain.com/${slug}`,\n            siteName: businessName,\n            images: [\n                {\n                    url: image,\n                    width: 1200,\n                    height: 630,\n                    alt: `${businessName} Chat - ${slug}`\n                }\n            ],\n            locale: 'en_US'\n        },\n        twitter: {\n            card: 'summary_large_image',\n            title: title,\n            description: description,\n            images: [\n                image\n            ]\n        },\n        robots: {\n            index: true,\n            follow: true\n        },\n        authors: [\n            {\n                name: businessName\n            }\n        ],\n        creator: businessName,\n        publisher: 'Driply'\n    };\n}\n// Server component - no \"use client\" directive\nconst page = async ({ params, searchParams })=>{\n    const { slug } = await params;\n    const queryParams = await searchParams;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatInterfaceWrapper__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            slug: slug,\n            query: queryParams\n        }, void 0, false, {\n            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\app\\\\[slug]\\\\page.js\",\n            lineNumber: 110,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\app\\\\[slug]\\\\page.js\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (page);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/[slug]/page.js\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"f4658303ac8d\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcTGFwdG9wIGRhdGFcXERSSVBMWS1DSEFUXFxjaGF0LW5leHRcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImY0NjU4MzAzYWM4ZFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.js":
/*!***************************!*\
  !*** ./src/app/layout.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.js\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.js\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_js_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.js\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.js\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_js_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Wix_Madefor_Text_arguments_variable_font_wix_madefor_text_subsets_latin_variableName_wixMadeforText___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.js\",\"import\":\"Wix_Madefor_Text\",\"arguments\":[{\"variable\":\"--font-wix-madefor-text\",\"subsets\":[\"latin\"]}],\"variableName\":\"wixMadeforText\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.js\\\",\\\"import\\\":\\\"Wix_Madefor_Text\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-wix-madefor-text\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"wixMadeforText\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Wix_Madefor_Text_arguments_variable_font_wix_madefor_text_subsets_latin_variableName_wixMadeforText___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_js_import_Wix_Madefor_Text_arguments_variable_font_wix_madefor_text_subsets_latin_variableName_wixMadeforText___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_ClientWrapper__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/ClientWrapper */ \"(rsc)/./src/components/ClientWrapper.jsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: {\n        default: \"Driply - AI Chat Platform\",\n        template: \"%s | Driply\"\n    },\n    description: \"Driply is an advanced AI chat platform that provides intelligent conversations and seamless user experience.\",\n    keywords: [\n        \"AI\",\n        \"chat\",\n        \"conversation\",\n        \"driply\",\n        \"artificial intelligence\"\n    ],\n    authors: [\n        {\n            name: \"Driply Team\"\n        }\n    ],\n    creator: \"Driply\",\n    publisher: \"Driply\",\n    robots: {\n        index: true,\n        follow: true\n    },\n    openGraph: {\n        type: \"website\",\n        locale: \"en_US\",\n        url: \"https://yourdomain.com\",\n        siteName: \"Driply\",\n        title: \"Driply - AI Chat Platform\",\n        description: \"Driply is an advanced AI chat platform that provides intelligent conversations and seamless user experience.\",\n        images: [\n            {\n                url: \"/og-image.jpg\",\n                width: 1200,\n                height: 630,\n                alt: \"Driply AI Chat Platform\"\n            }\n        ]\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"Driply - AI Chat Platform\",\n        description: \"Driply is an advanced AI chat platform that provides intelligent conversations and seamless user experience.\",\n        images: [\n            \"/og-image.jpg\"\n        ]\n    },\n    viewport: \"width=device-width, initial-scale=1\",\n    themeColor: \"#000000\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_js_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default().variable)} ${(next_font_google_target_css_path_src_app_layout_js_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default().variable)} ${(next_font_google_target_css_path_src_app_layout_js_import_Wix_Madefor_Text_arguments_variable_font_wix_madefor_text_subsets_latin_variableName_wixMadeforText___WEBPACK_IMPORTED_MODULE_5___default().variable)} antialiased`,\n            suppressHydrationWarning: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientWrapper__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\app\\\\layout.js\",\n                lineNumber: 71,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\app\\\\layout.js\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\app\\\\layout.js\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBSU1BO0FBS0FDO0FBS0FDO0FBYmlCO0FBQ2lDO0FBaUJqRCxNQUFNRSxXQUFXO0lBQ3RCQyxPQUFPO1FBQ0xDLFNBQVM7UUFDVEMsVUFBVTtJQUNaO0lBQ0FDLGFBQ0U7SUFDRkMsVUFBVTtRQUFDO1FBQU07UUFBUTtRQUFnQjtRQUFVO0tBQTBCO0lBQzdFQyxTQUFTO1FBQUM7WUFBRUMsTUFBTTtRQUFjO0tBQUU7SUFDbENDLFNBQVM7SUFDVEMsV0FBVztJQUNYQyxRQUFRO1FBQ05DLE9BQU87UUFDUEMsUUFBUTtJQUNWO0lBQ0FDLFdBQVc7UUFDVEMsTUFBTTtRQUNOQyxRQUFRO1FBQ1JDLEtBQUs7UUFDTEMsVUFBVTtRQUNWaEIsT0FBTztRQUNQRyxhQUNFO1FBQ0ZjLFFBQVE7WUFDTjtnQkFDRUYsS0FBSztnQkFDTEcsT0FBTztnQkFDUEMsUUFBUTtnQkFDUkMsS0FBSztZQUNQO1NBQ0Q7SUFDSDtJQUNBQyxTQUFTO1FBQ1BDLE1BQU07UUFDTnRCLE9BQU87UUFDUEcsYUFDRTtRQUNGYyxRQUFRO1lBQUM7U0FBZ0I7SUFDM0I7SUFDQU0sVUFBVTtJQUNWQyxZQUFZO0FBQ2QsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFBRUMsUUFBUSxFQUFFO0lBQzdDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUNDQyxXQUFXLEdBQUduQywwTEFBa0IsQ0FBQyxDQUFDLEVBQUVDLCtMQUFrQixDQUFDLENBQUMsRUFBRUMsZ05BQXVCLENBQUMsWUFBWSxDQUFDO1lBQy9GbUMsMEJBQTBCO3NCQUcxQiw0RUFBQ2xDLGlFQUFhQTswQkFBRTRCOzs7Ozs7Ozs7Ozs7Ozs7O0FBSXhCIiwic291cmNlcyI6WyJEOlxcTGFwdG9wIGRhdGFcXERSSVBMWS1DSEFUXFxjaGF0LW5leHRcXHNyY1xcYXBwXFxsYXlvdXQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgR2Vpc3QsIEdlaXN0X01vbm8sIFdpeF9NYWRlZm9yX1RleHQgfSBmcm9tIFwibmV4dC9mb250L2dvb2dsZVwiO1xyXG5pbXBvcnQgXCIuL2dsb2JhbHMuY3NzXCI7XHJcbmltcG9ydCBDbGllbnRXcmFwcGVyIGZyb20gXCIuLi9jb21wb25lbnRzL0NsaWVudFdyYXBwZXJcIjtcclxuXHJcbmNvbnN0IGdlaXN0U2FucyA9IEdlaXN0KHtcclxuICB2YXJpYWJsZTogXCItLWZvbnQtZ2Vpc3Qtc2Fuc1wiLFxyXG4gIHN1YnNldHM6IFtcImxhdGluXCJdLFxyXG59KTtcclxuXHJcbmNvbnN0IGdlaXN0TW9ubyA9IEdlaXN0X01vbm8oe1xyXG4gIHZhcmlhYmxlOiBcIi0tZm9udC1nZWlzdC1tb25vXCIsXHJcbiAgc3Vic2V0czogW1wibGF0aW5cIl0sXHJcbn0pO1xyXG5cclxuY29uc3Qgd2l4TWFkZWZvclRleHQgPSBXaXhfTWFkZWZvcl9UZXh0KHtcclxuICB2YXJpYWJsZTogXCItLWZvbnQtd2l4LW1hZGVmb3ItdGV4dFwiLFxyXG4gIHN1YnNldHM6IFtcImxhdGluXCJdLFxyXG59KTtcclxuXHJcbmV4cG9ydCBjb25zdCBtZXRhZGF0YSA9IHtcclxuICB0aXRsZToge1xyXG4gICAgZGVmYXVsdDogXCJEcmlwbHkgLSBBSSBDaGF0IFBsYXRmb3JtXCIsXHJcbiAgICB0ZW1wbGF0ZTogXCIlcyB8IERyaXBseVwiLFxyXG4gIH0sXHJcbiAgZGVzY3JpcHRpb246XHJcbiAgICBcIkRyaXBseSBpcyBhbiBhZHZhbmNlZCBBSSBjaGF0IHBsYXRmb3JtIHRoYXQgcHJvdmlkZXMgaW50ZWxsaWdlbnQgY29udmVyc2F0aW9ucyBhbmQgc2VhbWxlc3MgdXNlciBleHBlcmllbmNlLlwiLFxyXG4gIGtleXdvcmRzOiBbXCJBSVwiLCBcImNoYXRcIiwgXCJjb252ZXJzYXRpb25cIiwgXCJkcmlwbHlcIiwgXCJhcnRpZmljaWFsIGludGVsbGlnZW5jZVwiXSxcclxuICBhdXRob3JzOiBbeyBuYW1lOiBcIkRyaXBseSBUZWFtXCIgfV0sXHJcbiAgY3JlYXRvcjogXCJEcmlwbHlcIixcclxuICBwdWJsaXNoZXI6IFwiRHJpcGx5XCIsXHJcbiAgcm9ib3RzOiB7XHJcbiAgICBpbmRleDogdHJ1ZSxcclxuICAgIGZvbGxvdzogdHJ1ZSxcclxuICB9LFxyXG4gIG9wZW5HcmFwaDoge1xyXG4gICAgdHlwZTogXCJ3ZWJzaXRlXCIsXHJcbiAgICBsb2NhbGU6IFwiZW5fVVNcIixcclxuICAgIHVybDogXCJodHRwczovL3lvdXJkb21haW4uY29tXCIsXHJcbiAgICBzaXRlTmFtZTogXCJEcmlwbHlcIixcclxuICAgIHRpdGxlOiBcIkRyaXBseSAtIEFJIENoYXQgUGxhdGZvcm1cIixcclxuICAgIGRlc2NyaXB0aW9uOlxyXG4gICAgICBcIkRyaXBseSBpcyBhbiBhZHZhbmNlZCBBSSBjaGF0IHBsYXRmb3JtIHRoYXQgcHJvdmlkZXMgaW50ZWxsaWdlbnQgY29udmVyc2F0aW9ucyBhbmQgc2VhbWxlc3MgdXNlciBleHBlcmllbmNlLlwiLFxyXG4gICAgaW1hZ2VzOiBbXHJcbiAgICAgIHtcclxuICAgICAgICB1cmw6IFwiL29nLWltYWdlLmpwZ1wiLCAvLyBNYWtlIHN1cmUgdG8gYWRkIHRoaXMgaW1hZ2UgdG8geW91ciBwdWJsaWMgZm9sZGVyXHJcbiAgICAgICAgd2lkdGg6IDEyMDAsXHJcbiAgICAgICAgaGVpZ2h0OiA2MzAsXHJcbiAgICAgICAgYWx0OiBcIkRyaXBseSBBSSBDaGF0IFBsYXRmb3JtXCIsXHJcbiAgICAgIH0sXHJcbiAgICBdLFxyXG4gIH0sXHJcbiAgdHdpdHRlcjoge1xyXG4gICAgY2FyZDogXCJzdW1tYXJ5X2xhcmdlX2ltYWdlXCIsXHJcbiAgICB0aXRsZTogXCJEcmlwbHkgLSBBSSBDaGF0IFBsYXRmb3JtXCIsXHJcbiAgICBkZXNjcmlwdGlvbjpcclxuICAgICAgXCJEcmlwbHkgaXMgYW4gYWR2YW5jZWQgQUkgY2hhdCBwbGF0Zm9ybSB0aGF0IHByb3ZpZGVzIGludGVsbGlnZW50IGNvbnZlcnNhdGlvbnMgYW5kIHNlYW1sZXNzIHVzZXIgZXhwZXJpZW5jZS5cIixcclxuICAgIGltYWdlczogW1wiL29nLWltYWdlLmpwZ1wiXSxcclxuICB9LFxyXG4gIHZpZXdwb3J0OiBcIndpZHRoPWRldmljZS13aWR0aCwgaW5pdGlhbC1zY2FsZT0xXCIsXHJcbiAgdGhlbWVDb2xvcjogXCIjMDAwMDAwXCIsXHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHsgY2hpbGRyZW4gfSkge1xyXG4gIHJldHVybiAoXHJcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cclxuICAgICAgPGJvZHlcclxuICAgICAgICBjbGFzc05hbWU9e2Ake2dlaXN0U2Fucy52YXJpYWJsZX0gJHtnZWlzdE1vbm8udmFyaWFibGV9ICR7d2l4TWFkZWZvclRleHQudmFyaWFibGV9IGFudGlhbGlhc2VkYH1cclxuICAgICAgICBzdXBwcmVzc0h5ZHJhdGlvbldhcm5pbmc9e3RydWV9XHJcbiAgICAgID5cclxuICAgICAgICB7LyogVXNlIENsaWVudFdyYXBwZXIgdG8gc2VwYXJhdGUgY2xpZW50IGNvbXBvbmVudHMgZnJvbSBzZXJ2ZXIgbGF5b3V0ICovfVxyXG4gICAgICAgIDxDbGllbnRXcmFwcGVyPntjaGlsZHJlbn08L0NsaWVudFdyYXBwZXI+XHJcbiAgICAgIDwvYm9keT5cclxuICAgIDwvaHRtbD5cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJnZWlzdFNhbnMiLCJnZWlzdE1vbm8iLCJ3aXhNYWRlZm9yVGV4dCIsIkNsaWVudFdyYXBwZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVmYXVsdCIsInRlbXBsYXRlIiwiZGVzY3JpcHRpb24iLCJrZXl3b3JkcyIsImF1dGhvcnMiLCJuYW1lIiwiY3JlYXRvciIsInB1Ymxpc2hlciIsInJvYm90cyIsImluZGV4IiwiZm9sbG93Iiwib3BlbkdyYXBoIiwidHlwZSIsImxvY2FsZSIsInVybCIsInNpdGVOYW1lIiwiaW1hZ2VzIiwid2lkdGgiLCJoZWlnaHQiLCJhbHQiLCJ0d2l0dGVyIiwiY2FyZCIsInZpZXdwb3J0IiwidGhlbWVDb2xvciIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSIsInZhcmlhYmxlIiwic3VwcHJlc3NIeWRyYXRpb25XYXJuaW5nIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.js\n");

/***/ }),

/***/ "(rsc)/./src/components/ChatInterfaceWrapper.jsx":
/*!*************************************************!*\
  !*** ./src/components/ChatInterfaceWrapper.jsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterfaceWrapper.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Laptop data\\DRIPLY-CHAT\\chat-next\\src\\components\\ChatInterfaceWrapper.jsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/ClientWrapper.jsx":
/*!******************************************!*\
  !*** ./src/components/ClientWrapper.jsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ClientWrapper.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Laptop data\\DRIPLY-CHAT\\chat-next\\src\\components\\ClientWrapper.jsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Wix_Madefor_Text%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-wix-madefor-text%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22wixMadeforText%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CClientWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Wix_Madefor_Text%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-wix-madefor-text%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22wixMadeforText%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CClientWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ClientWrapper.jsx */ \"(ssr)/./src/components/ClientWrapper.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Wix_Madefor_Text%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-wix-madefor-text%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22wixMadeforText%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CClientWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CChatInterfaceWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CChatInterfaceWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ChatInterfaceWrapper.jsx */ \"(ssr)/./src/components/ChatInterfaceWrapper.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNMYXB0b3AlMjBkYXRhJTVDJTVDRFJJUExZLUNIQVQlNUMlNUNjaGF0LW5leHQlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDQ2hhdEludGVyZmFjZVdyYXBwZXIuanN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOExBQW9KIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiRDpcXFxcTGFwdG9wIGRhdGFcXFxcRFJJUExZLUNIQVRcXFxcY2hhdC1uZXh0XFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXENoYXRJbnRlcmZhY2VXcmFwcGVyLmpzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CChatInterfaceWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/assets/images/logo.png":
/*!************************************!*\
  !*** ./src/assets/images/logo.png ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/logo.425de58c.png\",\"height\":188,\"width\":188,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Flogo.425de58c.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXNzZXRzL2ltYWdlcy9sb2dvLnBuZyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsQ0FBQyw0TEFBNEwiLCJzb3VyY2VzIjpbIkQ6XFxMYXB0b3AgZGF0YVxcRFJJUExZLUNIQVRcXGNoYXQtbmV4dFxcc3JjXFxhc3NldHNcXGltYWdlc1xcbG9nby5wbmciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2xvZ28uNDI1ZGU1OGMucG5nXCIsXCJoZWlnaHRcIjoxODgsXCJ3aWR0aFwiOjE4OCxcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZsb2dvLjQyNWRlNThjLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo4fTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/assets/images/logo.png\n");

/***/ }),

/***/ "(ssr)/./src/components/ChatInterface.jsx":
/*!******************************************!*\
  !*** ./src/components/ChatInterface.jsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(ssr)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/constants */ \"(ssr)/./src/utils/constants.js\");\n/* harmony import */ var _utils_config__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/config */ \"(ssr)/./src/utils/config.js\");\n/* harmony import */ var _barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FaArrowUp,FaChevronLeft,FaChevronRight!=!react-icons/fa6 */ \"(ssr)/./node_modules/react-icons/fa6/index.mjs\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../contexts/ChatContext */ \"(ssr)/./src/contexts/ChatContext.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nconst ChatInterface = ({ slug, query })=>{\n    // Context API\n    const { metadata, updateMetadata, setLoadingState, setErrorState, clearError, getCustomerName, getBusinessName, hasMetadata, loading: contextLoading, error: contextError } = (0,_contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__.useChatContext)();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [currentSlide, setCurrentSlide] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [touchStart, setTouchStart] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [touchEnd, setTouchEnd] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isTablet, setIsTablet] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showInitialUI, setShowInitialUI] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isBotTyping, setIsBotTyping] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isBotThinking, setIsBotThinking] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [sseConnection, setSseConnection] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const carouselRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const messagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const messageElementsRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(new Map()); // Store refs to individual messages\n    // Detect iOS for targeted fixes\n    const isIOS = ()=>{\n        if (false) {}\n        return false;\n    };\n    const username = slug;\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (username && \"undefined\" !== \"undefined\") {}\n        }\n    }[\"ChatInterface.useEffect\"], [\n        username\n    ]);\n    const fetchCustomer = async (customerName)=>{\n        try {\n            setLoadingState(true);\n            clearError();\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(`${_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_SETTINGS}?customerName=${customerName}`);\n            if (response.data && response.data.customerName) {\n                const metadataFromResponse = response.data.metaData;\n                console.log('Storing metadata in context:', metadataFromResponse);\n                updateMetadata(metadataFromResponse);\n                // Keep existing localStorage for backward compatibility\n                localStorage.setItem(\"customerName_userId\", response.data.customerName);\n                localStorage.setItem(\"BusinessName\", response.data.businessName);\n                window.dispatchEvent(new CustomEvent('businessNameLoaded', {\n                    detail: {\n                        businessName: response.data.businessName\n                    }\n                }));\n                const existingUserId = localStorage.getItem(\"userID\");\n                if (existingUserId) {\n                    await fetchExistingMessages(existingUserId);\n                } else {\n                    await initializeChatSession();\n                }\n            } else {\n                await initializeChatSession();\n            }\n        } catch (error) {\n            console.error('Error fetching customer data:', error);\n            setErrorState('Failed to load customer settings');\n            // Fallback metadata\n            updateMetadata({\n                customerName: customerName,\n                businessName: \"Driply\"\n            });\n            localStorage.setItem(\"BusinessName\", \"Driply\");\n            window.dispatchEvent(new CustomEvent('businessNameLoaded', {\n                detail: {\n                    businessName: \"Driply\"\n                }\n            }));\n            await initializeChatSession();\n        } finally{\n            setLoadingState(false);\n        }\n    };\n    const fetchExistingMessages = async (userId)=>{\n        console.log(\"metadata : \", metadata);\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(`${_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_MESSAGES}?userId=${userId}`);\n            if (response.data && response.data.length > 0) {\n                setMessages(response.data.map((msg)=>({\n                        text: msg.message,\n                        timestamp: new Date(msg.createdAt).getTime(),\n                        type: msg.type,\n                        source: msg.source\n                    })));\n            }\n            connectToSSE(userId);\n        } catch (error) {\n            await initializeChatSession();\n        }\n    };\n    const initializeChatSession = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_INIT, {\n                customerName: username\n            });\n            if (response.data && response.data.userId) {\n                localStorage.setItem(\"userId\", response.data.userId);\n                localStorage.setItem(\"userID\", response.data._id);\n                await fetchExistingMessages(response.data._id);\n            }\n        } catch (error) {\n            if (!localStorage.getItem(\"BusinessName\")) {\n                localStorage.setItem(\"BusinessName\", \"Driply\");\n                window.dispatchEvent(new CustomEvent('businessNameLoaded', {\n                    detail: {\n                        businessName: \"Driply\"\n                    }\n                }));\n            }\n        }\n    };\n    const connectToSSE = (userId)=>{\n        try {\n            if (sseConnection) {\n                sseConnection.close();\n            }\n            const sseUrl = (0,_utils_config__WEBPACK_IMPORTED_MODULE_4__.getSSEUrl)(userId);\n            const eventSource = new EventSource(sseUrl);\n            setSseConnection(eventSource);\n            eventSource.addEventListener(\"message\", ({ data })=>{\n                try {\n                    const contents = JSON.parse(data);\n                    handleSSEMessage(contents);\n                } catch (error) {\n                // Handle parsing error silently\n                }\n            });\n            eventSource.addEventListener(\"error\", ()=>{\n            // Handle connection error silently\n            });\n        } catch (error) {\n        // Handle SSE connection error silently\n        }\n    };\n    const handleSSEMessage = (data)=>{\n        const subtype = data.subType || data.subtype || data.type || \"UNKNOWN\";\n        const content = data.content || \"\";\n        const message = data.message || data.text || \"\";\n        if ([\n            \"TYPING\",\n            \"THINKING\",\n            \"BEHAVIOUR_MESSAGE\"\n        ].includes(subtype)) {\n            switch(subtype){\n                case \"TYPING\":\n                    setIsBotTyping(true);\n                    setIsBotThinking(false);\n                    break;\n                case \"THINKING\":\n                    setIsBotThinking(true);\n                    setIsBotTyping(false);\n                    break;\n                case \"BEHAVIOUR_MESSAGE\":\n                    setIsBotTyping(false);\n                    setIsBotThinking(false);\n                    break;\n            }\n            return;\n        }\n        const displayText = message || content;\n        if (!displayText) return;\n        const displayType = [\n            \"TEXT\",\n            \"MESSAGE\",\n            \"DATA_MESSAGE\"\n        ].includes(subtype) ? subtype : \"UNKNOWN\";\n        setMessages((prev)=>[\n                ...prev,\n                {\n                    text: displayText,\n                    timestamp: Date.now(),\n                    type: displayType,\n                    source: \"BOT\"\n                }\n            ]);\n        setIsBotTyping(false);\n        setIsBotThinking(false);\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (message.trim()) {\n            const userMessage = message.trim();\n            setMessages((prev)=>[\n                    ...prev,\n                    {\n                        text: userMessage,\n                        timestamp: Date.now(),\n                        type: \"TEXT\",\n                        source: \"USER\"\n                    }\n                ]);\n            setMessage(\"\");\n            setIsTyping(false);\n            // Immediate reset to \"first message\" view for user message\n            setTimeout(()=>resetToTopPosition(), 50);\n            // Follow up with full positioning after DOM update\n            setTimeout(()=>scrollToFirstMessageView(), 150);\n            // Show loading dots immediately after sending message\n            setIsBotThinking(true);\n            setIsBotTyping(false);\n            // Reset textarea\n            if (inputRef.current) {\n                inputRef.current.style.height = \"104px\";\n                inputRef.current.scrollTop = 0;\n                inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n                inputRef.current.classList.add(\"reset-height\");\n            }\n            try {\n                const userId = localStorage.getItem(\"userID\");\n                if (userId) {\n                    await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_SEND, {\n                        userId: userId,\n                        message: userMessage,\n                        \"typs\": \"TEXT\",\n                        \"source\": \"USER\",\n                        \"isTest\": query.isTest === '1' ? true : false\n                    });\n                }\n            } catch (error) {\n                // Hide loading dots on error\n                setIsBotThinking(false);\n                setIsBotTyping(false);\n                setMessages((prev)=>[\n                        ...prev,\n                        {\n                            text: \"Sorry, there was an error sending your message. Please try again.\",\n                            timestamp: Date.now(),\n                            type: \"TEXT\",\n                            source: \"BOT\"\n                        }\n                    ]);\n            }\n        }\n    };\n    const handleTextareaResize = (textarea)=>{\n        if (textarea) {\n            textarea.style.height = \"104px\";\n            const scrollHeight = textarea.scrollHeight;\n            if (isIOS()) {\n                if (scrollHeight > 48) {\n                    textarea.style.overflowY = \"auto\";\n                } else {\n                    textarea.style.overflowY = \"hidden\";\n                }\n            } else {\n                const newHeight = Math.min(250, Math.max(104, scrollHeight));\n                textarea.style.height = `${newHeight}px`;\n                textarea.style.overflowY = scrollHeight > newHeight ? \"auto\" : \"hidden\";\n            }\n        }\n    };\n    const handleInputChange = (e)=>{\n        const value = e.target.value;\n        setMessage(value);\n        handleTextareaResize(e.target);\n        if (messages.length === 0) {\n            setIsTyping(value.length > 0);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length > 0 && inputRef.current && !message) {\n                const timer = setTimeout({\n                    \"ChatInterface.useEffect.timer\": ()=>{\n                        if (inputRef.current) {\n                            inputRef.current.style.height = \"104px\";\n                            inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n                            inputRef.current.classList.add(\"reset-height\");\n                        }\n                    }\n                }[\"ChatInterface.useEffect.timer\"], 50);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                })[\"ChatInterface.useEffect\"];\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages,\n        message\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length > 0) {\n                // Trigger \"first message\" view for each new message\n                scrollToFirstMessageView();\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages,\n        isBotTyping,\n        isBotThinking\n    ]);\n    // Single-message focus scrolling system\n    const calculateMessageHeights = ()=>{\n        const messageHeights = [];\n        const messageElements = messagesContainerRef.current?.querySelectorAll('.message-item');\n        if (!messageElements) return [];\n        messageElements.forEach((element, index)=>{\n            const height = element.getBoundingClientRect().height;\n            messageHeights.push({\n                index,\n                height,\n                element\n            });\n        });\n        return messageHeights;\n    };\n    const calculateFirstMessagePosition = ()=>{\n        const container = messagesContainerRef.current;\n        if (!container) return 0;\n        const messageElements = container.querySelectorAll('.message-item');\n        if (messageElements.length === 0) return 0;\n        // SIMPLE APPROACH: Find the last message and position it at the top\n        const lastMessageElement = messageElements[messageElements.length - 1];\n        if (!lastMessageElement) return 0;\n        // Get the offset of the last message from the container's scroll area\n        const lastMessageOffsetTop = lastMessageElement.offsetTop;\n        // Position the last message at the top of the visible area\n        // Add small padding for better visual appearance\n        const topPadding = 20;\n        const targetScrollTop = lastMessageOffsetTop - topPadding;\n        console.log('🎯 POSITIONING LATEST MESSAGE AT TOP:', {\n            lastMessageOffsetTop,\n            targetScrollTop,\n            totalMessages: messageElements.length,\n            action: '⬆️ Moving latest message to TOP (first message style)'\n        });\n        return Math.max(0, targetScrollTop);\n    };\n    const getCurrentExchangeHeight = (messageHeights)=>{\n        if (messageHeights.length === 0) return 0;\n        // Determine what constitutes the \"current exchange\" based on message state\n        let exchangeHeight = 0;\n        const spacingBetweenMessages = 24; // matches space-y-6 (1.5rem)\n        // If bot is typing or thinking, include typing indicator\n        if (isBotTyping || isBotThinking) {\n            // Show: last user message + typing indicator\n            if (messageHeights.length >= 1) {\n                const lastUserMessageHeight = messageHeights[messageHeights.length - 1]?.height || 0;\n                const typingIndicatorHeight = 60; // estimated height for typing dots\n                exchangeHeight = lastUserMessageHeight + typingIndicatorHeight + spacingBetweenMessages;\n            }\n        } else {\n            // Show: current user-bot message pair\n            if (messageHeights.length >= 2) {\n                // Last two messages (user + bot response)\n                const lastBotHeight = messageHeights[messageHeights.length - 1]?.height || 0;\n                const lastUserHeight = messageHeights[messageHeights.length - 2]?.height || 0;\n                exchangeHeight = lastUserHeight + lastBotHeight + spacingBetweenMessages;\n            } else if (messageHeights.length === 1) {\n                // Only user message (bot hasn't responded yet)\n                exchangeHeight = messageHeights[0].height;\n            }\n        }\n        // Add some padding at the top for better visual appearance\n        const topPadding = 20;\n        return exchangeHeight + topPadding;\n    };\n    const scrollToFirstMessageView = ()=>{\n        setTimeout(()=>{\n            const container = messagesContainerRef.current;\n            if (!container) {\n                console.log('❌ Container not found');\n                return;\n            }\n            // SIMPLE TEST: Just scroll to show latest message at top\n            const messageElements = container.querySelectorAll('.message-item');\n            if (messageElements.length === 0) {\n                console.log('❌ No message elements found');\n                return;\n            }\n            const lastMessage = messageElements[messageElements.length - 1];\n            const containerRect = container.getBoundingClientRect();\n            const messageRect = lastMessage.getBoundingClientRect();\n            // Calculate scroll needed to bring message to top\n            const scrollOffset = container.scrollTop + (messageRect.top - containerRect.top) - 20; // 20px padding\n            console.log('🎯 SCROLL TO TOP TEST:', {\n                containerHeight: container.clientHeight,\n                containerScrollTop: container.scrollTop,\n                totalMessages: messageElements.length,\n                lastMessageTop: messageRect.top,\n                containerTop: containerRect.top,\n                calculatedOffset: scrollOffset,\n                action: '⬆️ TESTING: Moving latest message to TOP'\n            });\n            container.scrollTo({\n                top: Math.max(0, scrollOffset),\n                behavior: 'smooth'\n            });\n        }, 300); // Longer delay to ensure DOM is ready\n    };\n    // Alternative: Simple scroll to top approach\n    const scrollToTopLikeFirstMessage = ()=>{\n        const container = messagesContainerRef.current;\n        if (!container) return;\n        // SIMPLE TEST: Just scroll to top of container\n        // This will show the first messages, but let's see if it works\n        container.scrollTo({\n            top: 0,\n            behavior: 'smooth'\n        });\n        console.log('🧪 TESTING: Simple scroll to top (position 0)');\n    };\n    // Immediate positioning for \"first message\" appearance\n    const resetToTopPosition = ()=>{\n        const container = messagesContainerRef.current;\n        if (!container) return;\n        // Use the calculated position to move latest message to top\n        const targetScrollTop = calculateFirstMessagePosition();\n        console.log('⚡ POSITIONING LATEST MESSAGE AT TOP:', {\n            targetScrollTop,\n            totalMessages: messages.length,\n            action: '🚀 Moving latest message to top of visible area'\n        });\n        container.scrollTo({\n            top: targetScrollTop,\n            behavior: 'smooth'\n        });\n    };\n    const renderMessages = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: '24px'\n            },\n            children: [\n                messages.map((msg, index)=>{\n                    const messageId = `${msg.timestamp}-${index}`;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: (el)=>{\n                            if (el) {\n                                messageElementsRef.current.set(messageId, el);\n                            } else {\n                                messageElementsRef.current.delete(messageId);\n                            }\n                        },\n                        className: \"message-item\",\n                        \"data-message-index\": index,\n                        \"data-message-id\": messageId,\n                        style: {\n                            display: 'flex',\n                            justifyContent: msg.source === 'USER' ? 'flex-end' : 'flex-start',\n                            marginBottom: '24px'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: '16px',\n                                fontWeight: '400',\n                                borderRadius: isMobile ? '15px' : '24px',\n                                maxWidth: isMobile ? '90%' : '448px',\n                                wordBreak: 'break-word',\n                                overflowWrap: 'break-word',\n                                hyphens: 'auto',\n                                backgroundColor: msg.source === 'USER' ? '#f3f4f6' : '#ffffff',\n                                padding: isMobile ? '8px 12px' : '12px 16px',\n                                boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)'\n                            },\n                            children: msg.text\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 521,\n                            columnNumber: 15\n                        }, undefined)\n                    }, messageId, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 503,\n                        columnNumber: 13\n                    }, undefined);\n                }),\n                (isBotTyping || isBotThinking) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"message-item\",\n                    \"data-message-type\": \"loading\",\n                    style: {\n                        display: 'flex',\n                        justifyContent: 'flex-start',\n                        marginBottom: '24px'\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundColor: '#ffffff',\n                            padding: isMobile ? '8px 12px' : '12px 16px',\n                            borderRadius: isMobile ? '15px' : '24px',\n                            boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                gap: '4px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"loading-dot\",\n                                    style: {\n                                        width: '8px',\n                                        height: '8px',\n                                        backgroundColor: '#000000',\n                                        borderRadius: '50%'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 559,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"loading-dot\",\n                                    style: {\n                                        width: '8px',\n                                        height: '8px',\n                                        backgroundColor: '#000000',\n                                        borderRadius: '50%'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 560,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"loading-dot\",\n                                    style: {\n                                        width: '8px',\n                                        height: '8px',\n                                        backgroundColor: '#000000',\n                                        borderRadius: '50%'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 561,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 558,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 552,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                    lineNumber: 543,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n            lineNumber: 499,\n            columnNumber: 7\n        }, undefined);\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSubmit(e);\n        }\n    };\n    const handleSuggestionClick = async (cardTitle)=>{\n        setMessages((prev)=>[\n                ...prev,\n                {\n                    text: cardTitle,\n                    timestamp: Date.now(),\n                    type: \"TEXT\",\n                    source: \"USER\"\n                }\n            ]);\n        setMessage(\"\");\n        setIsTyping(false);\n        // Immediate reset to \"first message\" view for suggestion click\n        setTimeout(()=>resetToTopPosition(), 50);\n        // Follow up with full positioning\n        setTimeout(()=>scrollToFirstMessageView(), 150);\n        // Show loading dots immediately after sending message\n        setIsBotThinking(true);\n        setIsBotTyping(false);\n        if (inputRef.current) {\n            inputRef.current.style.height = \"104px\";\n            inputRef.current.scrollTop = 0;\n            inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n            inputRef.current.classList.add(\"reset-height\");\n        }\n        try {\n            const userId = localStorage.getItem(\"userID\");\n            if (userId) {\n                await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_SEND, {\n                    userId: userId,\n                    message: cardTitle,\n                    customerName: username\n                });\n            }\n        } catch (error) {\n            // Hide loading dots on error\n            setIsBotThinking(false);\n            setIsBotTyping(false);\n            setMessages((prev)=>[\n                    ...prev,\n                    {\n                        text: \"Sorry, there was an error sending your message. Please try again.\",\n                        timestamp: Date.now(),\n                        type: \"TEXT\",\n                        source: \"BOT\"\n                    }\n                ]);\n        }\n    };\n    const getMaxSlides = ()=>{\n        if (isMobile) return _utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.length;\n        return Math.ceil(_utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.length / 2);\n    };\n    const nextSlide = ()=>{\n        if (isMobile) {\n            setCurrentSlide((prev)=>prev < _utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.length - 1 ? prev + 1 : prev);\n        } else {\n            const maxSlides = getMaxSlides();\n            setCurrentSlide((prev)=>(prev + 1) % maxSlides);\n        }\n    };\n    const prevSlide = ()=>{\n        if (isMobile) {\n            setCurrentSlide((prev)=>prev > 0 ? prev - 1 : prev);\n        } else {\n            const maxSlides = getMaxSlides();\n            setCurrentSlide((prev)=>(prev - 1 + maxSlides) % maxSlides);\n        }\n    };\n    const handleNextSlide = ()=>{\n        nextSlide();\n    };\n    const handlePrevSlide = ()=>{\n        prevSlide();\n    };\n    const handleTouchStart = (e)=>{\n        setTouchStart(e.targetTouches[0].clientX);\n    };\n    const handleTouchMove = (e)=>{\n        setTouchEnd(e.targetTouches[0].clientX);\n    };\n    const handleTouchEnd = ()=>{\n        if (!touchStart || !touchEnd) return;\n        const swipeDistance = touchStart - touchEnd;\n        const minSwipeDistance = 50;\n        if (swipeDistance > minSwipeDistance) {\n            nextSlide();\n        }\n        if (swipeDistance < -minSwipeDistance) {\n            prevSlide();\n        }\n        setTouchStart(0);\n        setTouchEnd(0);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length === 0) {\n                setShowInitialUI(false);\n                const timer = setTimeout({\n                    \"ChatInterface.useEffect.timer\": ()=>setShowInitialUI(true)\n                }[\"ChatInterface.useEffect.timer\"], 60);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                })[\"ChatInterface.useEffect\"];\n            } else {\n                setShowInitialUI(false);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages.length\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const checkScreenSize = {\n                \"ChatInterface.useEffect.checkScreenSize\": ()=>{\n                    if (false) {}\n                }\n            }[\"ChatInterface.useEffect.checkScreenSize\"];\n            checkScreenSize();\n            const handleResize = {\n                \"ChatInterface.useEffect.handleResize\": ()=>{\n                    setCurrentSlide(0);\n                    checkScreenSize();\n                }\n            }[\"ChatInterface.useEffect.handleResize\"];\n            if (false) {}\n        }\n    }[\"ChatInterface.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (inputRef.current && messages.length === 0) {\n                const shouldAutoFocus = window.innerWidth >= 768;\n                if (shouldAutoFocus) {\n                    const timer = setTimeout({\n                        \"ChatInterface.useEffect.timer\": ()=>{\n                            inputRef.current?.focus();\n                        }\n                    }[\"ChatInterface.useEffect.timer\"], 100);\n                    return ({\n                        \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                    })[\"ChatInterface.useEffect\"];\n                }\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages.length\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            return ({\n                \"ChatInterface.useEffect\": ()=>{\n                    if (sseConnection) {\n                        sseConnection.close();\n                    }\n                }\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], [\n        sseConnection\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"bg-white flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"hidden lg:flex flex-1 flex-col px-4 \",\n                children: messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"flex flex-1 items-center justify-center min-h-[calc(100vh-64px)] mt-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"flex flex-col items-center w-[768px] justify-center mx-auto \",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    transitionDelay: showInitialUI ? \"40ms\" : \"0ms\"\n                                },\n                                className: \"jsx-4bbd75c2920167f7\" + \" \" + `text-4xl text-gray-900 mb-6 text-center transition-all duration-700 ${showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"}`,\n                                children: \"How can I help you?\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 747,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                style: {\n                                    transitionDelay: showInitialUI ? \"120ms\" : \"0ms\"\n                                },\n                                className: \"jsx-4bbd75c2920167f7\" + \" \" + `relative w-full mb-6 flex flex-col items-center transition-all duration-500 ${showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        ref: messages.length === 0 ? inputRef : null,\n                                        value: message,\n                                        onChange: handleInputChange,\n                                        onKeyDown: handleKeyPress,\n                                        placeholder: \"Ask anything\",\n                                        autoFocus: messages.length === 0,\n                                        rows: 1,\n                                        style: {\n                                            boxSizing: \"border-box\",\n                                            zIndex: 1001,\n                                            position: \"relative\"\n                                        },\n                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"pt-3 px-4 lg:p-4 pb-16 pr-12 w-full min-h-[104px] max-h-[250px] overflow-y-auto hide-scrollbar text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base shadow-md resize-none reset-height\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 766,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: message.trim().length === 0,\n                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + `absolute right-3 bottom-3 w-9 h-9 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation ${message.trim().length === 0 ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                            className: \"w-4 h-4 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 789,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 781,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 758,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    transitionDelay: showInitialUI ? \"200ms\" : \"0ms\",\n                                    zIndex: 10\n                                },\n                                className: \"jsx-4bbd75c2920167f7\" + \" \" + `relative w-full max-w-2xl transition-all duration-500 ease-in-out ${isTyping ? \"opacity-0 pointer-events-none\" : showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: carouselRef,\n                                            style: {\n                                                transform: `translateX(-${currentSlide * 100}%)`\n                                            },\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"flex transition-transform duration-300 ease-in-out\",\n                                            children: Array.from({\n                                                length: Math.ceil(_utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.length / 2)\n                                            }).map((_, slideIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"w-full flex-shrink-0 flex gap-2 justify-center\",\n                                                    children: _utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.slice(slideIndex * 2, slideIndex * 2 + 2).map((card, cardIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleSuggestionClick(card.title),\n                                                            style: {\n                                                                width: \"fit-content\"\n                                                            },\n                                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"py-2 px-3 rounded-[8px] bg-[#f6f6f6] text-left group transition-all duration-200 touch-manipulation\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-[16px] font-[600] text-black mb-0.5 leading-tight whitespace-nowrap\",\n                                                                    children: card.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                    lineNumber: 827,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-[16px] text-gray-500 leading-tight whitespace-nowrap\",\n                                                                    children: card.subtitle\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                    lineNumber: 830,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, slideIndex * 2 + cardIndex, true, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 821,\n                                                            columnNumber: 27\n                                                        }, undefined))\n                                                }, slideIndex, false, {\n                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                    lineNumber: 813,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 805,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 804,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handlePrevSlide,\n                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"absolute left-28 top-1/2 transform -translate-y-1/2 -translate-x-6 w-8 h-8 bg-white border border-gray-200 rounded-full flex items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronLeft, {\n                                            className: \"w-3 h-3 text-gray-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 843,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 839,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleNextSlide,\n                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"absolute right-28 top-1/2 transform -translate-y-1/2 translate-x-6 w-8 h-8 bg-white border border-gray-200 rounded-full flex items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronRight, {\n                                            className: \"w-3 h-3 text-gray-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 849,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 845,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 792,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 746,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                    lineNumber: 745,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"h-screen flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: messagesContainerRef,\n                            style: {\n                                height: \"calc(100vh - 60px - 180px)\",\n                                marginTop: \"60px\",\n                                overflowY: \"auto\",\n                                backgroundColor: \"#ffffff\",\n                                overscrollBehavior: \"contain\",\n                                scrollBehavior: \"smooth\"\n                            },\n                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"hide-scrollbar\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"w-full lg:w-[768px] mx-auto px-4 py-4\",\n                                children: [\n                                    renderMessages(),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: messagesEndRef,\n                                        className: \"jsx-4bbd75c2920167f7\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 871,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 869,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 857,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                height: \"180px\",\n                                position: \"relative\",\n                                zIndex: 1000,\n                                flexShrink: 0\n                            },\n                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"bg-white border-t border-gray-100\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSubmit,\n                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"relative w-full lg:w-[768px] mx-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                ref: messages.length > 0 ? inputRef : null,\n                                                value: message,\n                                                onChange: handleInputChange,\n                                                onKeyDown: handleKeyPress,\n                                                placeholder: \"Ask anything\",\n                                                rows: 1,\n                                                style: {\n                                                    boxSizing: \"border-box\",\n                                                    zIndex: 1001,\n                                                    position: \"relative\"\n                                                },\n                                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"pt-3 px-4 md:px-5 pb-10 pr-12 w-full min-h-[104px] max-h-[250px] overflow-y-auto text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base shadow-lg resize-none reset-height hide-scrollbar\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 890,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"submit\",\n                                                disabled: message.trim().length === 0,\n                                                className: \"jsx-4bbd75c2920167f7\" + \" \" + `absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation ${message.trim().length === 0 ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"}`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                                    className: \"w-4 h-4 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                    lineNumber: 912,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 904,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 886,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-center mt-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-xs text-gray-500\",\n                                            children: [\n                                                \"This chat is powered by\",\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-black\",\n                                                    children: \"Driply.me\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                    lineNumber: 918,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 916,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 915,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 885,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 876,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                    lineNumber: 855,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 743,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"lg:hidden h-screen flex flex-col\",\n                children: [\n                    messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"flex flex-col items-center pt-15 pb-4 px-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"hidden md:flex lg:hidden flex-col items-center\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 932,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 931,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    paddingBottom: \"260px\"\n                                },\n                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"flex flex-col items-center justify-center flex-1 px-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"40ms\" : \"0ms\"\n                                    },\n                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + `text-2xl md:text-4xl text-gray-900 text-center leading-relaxed transition-all duration-700 ${showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"}`,\n                                    children: \"How can I help you?\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 938,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 934,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"h-screen flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: messagesContainerRef,\n                                style: {\n                                    height: isMobile ? \"calc(100vh - 60px - 280px)\" // mobile: navbar(60px) + input(280px)\n                                     : \"calc(100vh - 60px - 200px)\",\n                                    marginTop: \"60px\",\n                                    overflowY: \"auto\",\n                                    backgroundColor: \"#ffffff\",\n                                    overscrollBehavior: \"contain\",\n                                    scrollBehavior: \"smooth\"\n                                },\n                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"hide-scrollbar\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"w-full max-w-[803px] mx-auto px-4 py-4\",\n                                    children: [\n                                        renderMessages(),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: messagesEndRef,\n                                            className: \"jsx-4bbd75c2920167f7\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 969,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 967,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 953,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    height: isMobile ? \"280px\" : \"200px\",\n                                    position: \"relative\",\n                                    zIndex: 1000,\n                                    flexShrink: 0,\n                                    paddingBottom: \"env(safe-area-inset-bottom, 0)\"\n                                },\n                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"bg-white border-t border-gray-100\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        transform: \"translateZ(0)\"\n                                    },\n                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"px-4 py-4 bg-white\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            style: {\n                                                zIndex: 1001\n                                            },\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"relative bg-white\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    transform: \"translateZ(0)\"\n                                                },\n                                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"relative w-full max-w-[890px] mx-auto bg-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        ref: messages.length > 0 ? inputRef : null,\n                                                        value: message,\n                                                        onChange: handleInputChange,\n                                                        onKeyDown: handleKeyPress,\n                                                        placeholder: \"Ask anything\",\n                                                        rows: 1,\n                                                        style: {\n                                                            boxSizing: \"border-box\",\n                                                            maxHeight: \"250px\",\n                                                            zIndex: 1001,\n                                                            overscrollBehavior: \"none\",\n                                                            position: \"relative\"\n                                                        },\n                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"w-full min-h-[104px] pt-3 px-4 pb-10 pr-12 text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base resize-none overflow-y-auto reset-height\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 997,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        disabled: message.trim().length === 0,\n                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + `absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation ${message.trim().length === 0 ? \"bg-[#E0E0E0] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"}`,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                                            className: \"w-4 h-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 1021,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1013,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 993,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 988,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-center py-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"This chat is powered by\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-black\",\n                                                        children: \"Driply.me\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1028,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1026,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1025,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 984,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 974,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 951,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"absolute bottom-0 left-0 right-0 bg-white\",\n                        children: messages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"120ms\" : \"0ms\",\n                                        position: \"relative\",\n                                        zIndex: 10,\n                                        backgroundColor: \"white\",\n                                        paddingBottom: \"10px\"\n                                    },\n                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + `${isMobile ? \"px-0\" : \"px-4\"} pt-2 pb-2 transition-all duration-500 ease-in-out ${isTyping ? \"opacity-0 pointer-events-none\" : showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"overflow-hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                ref: carouselRef,\n                                                style: {\n                                                    transform: `translateX(-${currentSlide * (isMobile ? 50 : 100)}%)`,\n                                                    paddingLeft: isMobile ? \"1rem\" : \"0\"\n                                                },\n                                                onTouchStart: handleTouchStart,\n                                                onTouchMove: handleTouchMove,\n                                                onTouchEnd: handleTouchEnd,\n                                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"flex transition-transform duration-300 ease-in-out\",\n                                                children: isMobile ? (()=>{\n                                                    const circularCards = [\n                                                        ..._utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS,\n                                                        ..._utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS\n                                                    ];\n                                                    return circularCards.map((card, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"flex-shrink-0 mt-3 mr-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleSuggestionClick(card.title),\n                                                                style: {\n                                                                    width: \"fit-content\"\n                                                                },\n                                                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"py-3 px-2.5 rounded-[8px] bg-[#F6F6F6] text-left hover:bg-[#D6EDFF] transition-all duration-200 inline-block touch-manipulation\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-[16px] font-[600] text-black mb-0.5 leading-tight whitespace-nowrap overflow-hidden text-ellipsis\",\n                                                                        children: card.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                        lineNumber: 1088,\n                                                                        columnNumber: 31\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-[16px] text-gray-500 leading-tight whitespace-nowrap overflow-hidden text-ellipsis\",\n                                                                        children: card.subtitle\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                        lineNumber: 1091,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                lineNumber: 1081,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        }, `${index}-${card.title}`, false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 1077,\n                                                            columnNumber: 27\n                                                        }, undefined));\n                                                })() : Array.from({\n                                                    length: Math.ceil(_utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.length / 2)\n                                                }).map((_, slideIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"w-full flex-shrink-0 flex gap-2 justify-center\",\n                                                        children: _utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.slice(slideIndex * 2, slideIndex * 2 + 2).map((card, cardIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleSuggestionClick(card.title),\n                                                                style: {\n                                                                    width: \"fit-content\"\n                                                                },\n                                                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"py-2 px-2.5 rounded-[8px] bg-[#F6F6F6] text-left group hover:bg-[#D6EDFF] transition-all duration-200 inline-block touch-manipulation\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-[14px] font-[600] text-black mb-0.5 leading-tight whitespace-nowrap\",\n                                                                        children: card.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                        lineNumber: 1117,\n                                                                        columnNumber: 31\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-[16px] text-gray-500 leading-tight whitespace-nowrap\",\n                                                                        children: card.subtitle\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                        lineNumber: 1120,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                ]\n                                                            }, slideIndex * 2 + cardIndex, true, {\n                                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                lineNumber: 1109,\n                                                                columnNumber: 29\n                                                            }, undefined))\n                                                    }, slideIndex, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1101,\n                                                        columnNumber: 25\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1058,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1057,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handlePrevSlide,\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"hidden md:block absolute left-28 top-1/2 transform -translate-y-1/2 -translate-x-6 w-8 h-8 bg-white border border-gray-200 rounded-full items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronLeft, {\n                                                className: \"w-3 h-3 text-gray-600  ms-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1135,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1131,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleNextSlide,\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"hidden md:block absolute right-28 top-1/2 transform -translate-y-1/2 translate-x-6 w-8 h-8 bg-white border border-gray-200 rounded-full items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronRight, {\n                                                className: \"w-3 h-3 text-gray-600 ms-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1141,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1137,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1041,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"200ms\" : \"0ms\",\n                                        position: \"relative\",\n                                        zIndex: 5,\n                                        paddingBottom: \"env(safe-area-inset-bottom)\"\n                                    },\n                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + `px-4 bg-white transition-all duration-500 ${showInitialUI ? \"opacity-100 translate-y-0\" : \"translate-y-4\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"relative\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"relative w-full max-w-[890px] mx-auto\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        ref: messages.length === 0 ? inputRef : null,\n                                                        value: message,\n                                                        onChange: handleInputChange,\n                                                        onKeyDown: handleKeyPress,\n                                                        placeholder: \"Ask anything\",\n                                                        rows: 1,\n                                                        style: {\n                                                            boxSizing: \"border-box\",\n                                                            maxHeight: \"250px\",\n                                                            fontSize: \"16px\",\n                                                            zIndex: 1001,\n                                                            overscrollBehaviorY: \"contain\",\n                                                            position: \"relative\"\n                                                        },\n                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"w-full min-h-[104px] pt-3 px-4 pb-10 pr-12 text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base resize-none overflow-y-auto reset-height\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1158,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        disabled: message.trim().length === 0,\n                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + `absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation ${message.trim().length === 0 ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"}`,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                                            className: \"w-4 h-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 1183,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1175,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1157,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1156,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-center py-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"This chat is powered by\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-black\",\n                                                        children: \"Driply.me\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1190,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1188,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1187,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1146,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 1037,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 928,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"4bbd75c2920167f7\",\n                children: '.loading-dot{-webkit-animation:loading-dots 1.4s infinite ease-in-out both;-moz-animation:loading-dots 1.4s infinite ease-in-out both;-o-animation:loading-dots 1.4s infinite ease-in-out both;animation:loading-dots 1.4s infinite ease-in-out both}.loading-dot:nth-child(1){-webkit-animation-delay:-.32s;-moz-animation-delay:-.32s;-o-animation-delay:-.32s;animation-delay:-.32s}.loading-dot:nth-child(2){-webkit-animation-delay:-.16s;-moz-animation-delay:-.16s;-o-animation-delay:-.16s;animation-delay:-.16s}.loading-dot:nth-child(3){-webkit-animation-delay:0s;-moz-animation-delay:0s;-o-animation-delay:0s;animation-delay:0s}@-webkit-keyframes loading-dots{0%,80%,100%{-webkit-transform:scale(1);transform:scale(1);opacity:1}40%{-webkit-transform:scale(1.5);transform:scale(1.5);opacity:.7}}@-moz-keyframes loading-dots{0%,80%,100%{-moz-transform:scale(1);transform:scale(1);opacity:1}40%{-moz-transform:scale(1.5);transform:scale(1.5);opacity:.7}}@-o-keyframes loading-dots{0%,80%,100%{-o-transform:scale(1);transform:scale(1);opacity:1}40%{-o-transform:scale(1.5);transform:scale(1.5);opacity:.7}}@keyframes loading-dots{0%,80%,100%{-webkit-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1);opacity:1}40%{-webkit-transform:scale(1.5);-moz-transform:scale(1.5);-o-transform:scale(1.5);transform:scale(1.5);opacity:.7}}.hide-scrollbar{-ms-overflow-style:none!important;scrollbar-width:none!important}.hide-scrollbar::-webkit-scrollbar{display:none!important}@media only screen and (max-width:1023px){.lg\\\\\\\\.jsx-4bbd75c2920167f7:hidden .absolute.bottom-0.jsx-4bbd75c2920167f7{position:fixed!important;bottom:env(safe-area-inset-bottom,0);left:0;right:0;width:100%;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;z-index:1000;background-color:white}textarea.jsx-4bbd75c2920167f7{-webkit-user-select:auto!important;-moz-user-select:auto!important;-ms-user-select:auto!important;user-select:auto!important;-webkit-appearance:none;-moz-appearance:none;-ms-appearance:none;appearance:none;-ms-scroll-chaining:none;overscroll-behavior:none;line-height:24px;max-height:250px!important;position:relative!important;-webkit-transform:translatez(0);-moz-transform:translatez(0);-ms-transform:translatez(0);-o-transform:translatez(0);transform:translatez(0);-webkit-backface-visibility:hidden;-moz-backface-visibility:hidden;backface-visibility:hidden;-webkit-perspective:1000;-moz-perspective:1000;perspective:1000;z-index:1001!important;background-color:white}.lg\\\\\\\\.jsx-4bbd75c2920167f7:hidden form.jsx-4bbd75c2920167f7{position:relative!important;z-index:1001;background-color:white}button[type=\"submit\"].jsx-4bbd75c2920167f7{position:absolute!important;right:12px;bottom:12px;z-index:1002!important;-webkit-tap-highlight-color:transparent;-ms-touch-action:-ms-manipulation;-ms-touch-action:manipulation;touch-action:manipulation;-webkit-transform:translatez(0);-moz-transform:translatez(0);-ms-transform:translatez(0);-o-transform:translatez(0);transform:translatez(0)}.lg\\\\\\\\.jsx-4bbd75c2920167f7:hidden .bg-white.jsx-4bbd75c2920167f7{background-color:white!important}}@media only screen and (min-width:1024px){textarea.jsx-4bbd75c2920167f7{max-height:250px!important;overflow-y:auto!important;-ms-scroll-chaining:none;overscroll-behavior:contain}.reset-height.jsx-4bbd75c2920167f7{min-height:104px!important;max-height:250px!important;overflow-y:auto!important}}.fixed-bottom.jsx-4bbd75c2920167f7{position:fixed!important;bottom:0;left:0;right:0;background:white}'\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n        lineNumber: 741,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatInterface);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ChatInterface.jsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ChatInterfaceWrapper.jsx":
/*!*************************************************!*\
  !*** ./src/components/ChatInterfaceWrapper.jsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatInterfaceWrapper)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ChatInterface__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ChatInterface */ \"(ssr)/./src/components/ChatInterface.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n/**\r\n * Client-side wrapper for ChatInterface component\r\n * This separates client components from server components to fix metadata issues\r\n * Following Next.js App Router best practices\r\n */ function ChatInterfaceWrapper({ slug, query }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatInterface__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        slug: slug,\n        query: query\n    }, void 0, false, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterfaceWrapper.jsx\",\n        lineNumber: 12,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9DaGF0SW50ZXJmYWNlV3JhcHBlci5qc3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUUwQjtBQUNrQjtBQUU1Qzs7OztDQUlDLEdBQ2MsU0FBU0UscUJBQXFCLEVBQUVDLElBQUksRUFBRUMsS0FBSyxFQUFFO0lBQzFELHFCQUFPLDhEQUFDSCxzREFBYUE7UUFBQ0UsTUFBTUE7UUFBTUMsT0FBT0E7Ozs7OztBQUMzQyIsInNvdXJjZXMiOlsiRDpcXExhcHRvcCBkYXRhXFxEUklQTFktQ0hBVFxcY2hhdC1uZXh0XFxzcmNcXGNvbXBvbmVudHNcXENoYXRJbnRlcmZhY2VXcmFwcGVyLmpzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcclxuXHJcbmltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCBDaGF0SW50ZXJmYWNlIGZyb20gJy4vQ2hhdEludGVyZmFjZSc7XHJcblxyXG4vKipcclxuICogQ2xpZW50LXNpZGUgd3JhcHBlciBmb3IgQ2hhdEludGVyZmFjZSBjb21wb25lbnRcclxuICogVGhpcyBzZXBhcmF0ZXMgY2xpZW50IGNvbXBvbmVudHMgZnJvbSBzZXJ2ZXIgY29tcG9uZW50cyB0byBmaXggbWV0YWRhdGEgaXNzdWVzXHJcbiAqIEZvbGxvd2luZyBOZXh0LmpzIEFwcCBSb3V0ZXIgYmVzdCBwcmFjdGljZXNcclxuICovXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIENoYXRJbnRlcmZhY2VXcmFwcGVyKHsgc2x1ZywgcXVlcnkgfSkge1xyXG4gIHJldHVybiA8Q2hhdEludGVyZmFjZSBzbHVnPXtzbHVnfSBxdWVyeT17cXVlcnl9IC8+O1xyXG59XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkNoYXRJbnRlcmZhY2UiLCJDaGF0SW50ZXJmYWNlV3JhcHBlciIsInNsdWciLCJxdWVyeSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ChatInterfaceWrapper.jsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ClientWrapper.jsx":
/*!******************************************!*\
  !*** ./src/components/ClientWrapper.jsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClientWrapper)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Navbar */ \"(ssr)/./src/components/Navbar.jsx\");\n/* harmony import */ var _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/ChatContext */ \"(ssr)/./src/contexts/ChatContext.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n/**\r\n * Client wrapper component that contains all client-side logic\r\n * This separates client components from server components to fix metadata issues\r\n * Following Next.js App Router best practices\r\n */ function ClientWrapper({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_ChatContext__WEBPACK_IMPORTED_MODULE_3__.ChatProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ClientWrapper.jsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ClientWrapper.jsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9DbGllbnRXcmFwcGVyLmpzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUUwQjtBQUNJO0FBQ3lCO0FBRXZEOzs7O0NBSUMsR0FDYyxTQUFTRyxjQUFjLEVBQUVDLFFBQVEsRUFBRTtJQUNoRCxxQkFDRSw4REFBQ0YsK0RBQVlBOzswQkFDWCw4REFBQ0QsK0NBQU1BOzs7OztZQUNORzs7Ozs7OztBQUdQIiwic291cmNlcyI6WyJEOlxcTGFwdG9wIGRhdGFcXERSSVBMWS1DSEFUXFxjaGF0LW5leHRcXHNyY1xcY29tcG9uZW50c1xcQ2xpZW50V3JhcHBlci5qc3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XHJcblxyXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgTmF2YmFyIGZyb20gXCIuL05hdmJhclwiO1xyXG5pbXBvcnQgeyBDaGF0UHJvdmlkZXIgfSBmcm9tIFwiLi4vY29udGV4dHMvQ2hhdENvbnRleHRcIjtcclxuXHJcbi8qKlxyXG4gKiBDbGllbnQgd3JhcHBlciBjb21wb25lbnQgdGhhdCBjb250YWlucyBhbGwgY2xpZW50LXNpZGUgbG9naWNcclxuICogVGhpcyBzZXBhcmF0ZXMgY2xpZW50IGNvbXBvbmVudHMgZnJvbSBzZXJ2ZXIgY29tcG9uZW50cyB0byBmaXggbWV0YWRhdGEgaXNzdWVzXHJcbiAqIEZvbGxvd2luZyBOZXh0LmpzIEFwcCBSb3V0ZXIgYmVzdCBwcmFjdGljZXNcclxuICovXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIENsaWVudFdyYXBwZXIoeyBjaGlsZHJlbiB9KSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxDaGF0UHJvdmlkZXI+XHJcbiAgICAgIDxOYXZiYXIgLz5cclxuICAgICAge2NoaWxkcmVufVxyXG4gICAgPC9DaGF0UHJvdmlkZXI+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJOYXZiYXIiLCJDaGF0UHJvdmlkZXIiLCJDbGllbnRXcmFwcGVyIiwiY2hpbGRyZW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ClientWrapper.jsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Navbar.jsx":
/*!***********************************!*\
  !*** ./src/components/Navbar.jsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_FiEdit_react_icons_fi__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FiEdit!=!react-icons/fi */ \"(ssr)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _assets_images_logo_png__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../assets/images/logo.png */ \"(ssr)/./src/assets/images/logo.png\");\n/* harmony import */ var _barrel_optimize_names_HiUserCircle_react_icons_hi2__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=HiUserCircle!=!react-icons/hi2 */ \"(ssr)/./node_modules/react-icons/hi2/index.mjs\");\n/* harmony import */ var _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../contexts/ChatContext */ \"(ssr)/./src/contexts/ChatContext.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nconst Navbar = ()=>{\n    const [businessName, setBusinessName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Get context values\n    const { metadata, hasMetadata } = (0,_contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__.useChatContext)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            // First, try to get business name from context\n            if (hasMetadata() && metadata.businessName) {\n                setBusinessName(metadata.businessName);\n                return;\n            }\n            // Fallback to localStorage and event listeners\n            const checkBusinessName = {\n                \"Navbar.useEffect.checkBusinessName\": ()=>{\n                    const storedName = localStorage.getItem(\"BusinessName\") || \"Driply\";\n                    if (storedName && storedName !== \"undefined\" && storedName !== \"null\") {\n                        setBusinessName(storedName);\n                        return true;\n                    }\n                    return false;\n                }\n            }[\"Navbar.useEffect.checkBusinessName\"];\n            if (checkBusinessName()) {\n                return;\n            }\n            const handleBusinessNameLoaded = {\n                \"Navbar.useEffect.handleBusinessNameLoaded\": (event)=>{\n                    if (event.detail && event.detail.businessName) {\n                        setBusinessName(event.detail.businessName);\n                    }\n                }\n            }[\"Navbar.useEffect.handleBusinessNameLoaded\"];\n            window.addEventListener(\"businessNameLoaded\", handleBusinessNameLoaded);\n            const interval = setInterval({\n                \"Navbar.useEffect.interval\": ()=>{\n                    if (checkBusinessName()) {\n                        clearInterval(interval);\n                    }\n                }\n            }[\"Navbar.useEffect.interval\"], 100);\n            const timeout = setTimeout({\n                \"Navbar.useEffect.timeout\": ()=>{\n                    clearInterval(interval);\n                }\n            }[\"Navbar.useEffect.timeout\"], 5000);\n            return ({\n                \"Navbar.useEffect\": ()=>{\n                    window.removeEventListener(\"businessNameLoaded\", handleBusinessNameLoaded);\n                    clearInterval(interval);\n                    clearTimeout(timeout);\n                }\n            })[\"Navbar.useEffect\"];\n        }\n    }[\"Navbar.useEffect\"], [\n        metadata,\n        hasMetadata\n    ]);\n    const handleNewConversation = ()=>{\n        localStorage.removeItem(\"userID\");\n        localStorage.removeItem(\"customerName_userId\");\n        localStorage.removeItem(\"BusinessName\");\n        localStorage.removeItem(\"userId\");\n        window.location.reload();\n    };\n    const handleHomeClick = (e)=>{\n        e.preventDefault();\n        window.location.reload();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-white fixed top-0 left-0 right-0 z-50\",\n        style: {\n            height: \"60px\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-between items-center h-full px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 \",\n                    children: [\n                        metadata.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                src: metadata.image,\n                                alt: \"user-image\",\n                                className: \"h-10 w-10 hover:border hover:border-gray-400 rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\Navbar.jsx\",\n                                lineNumber: 85,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiUserCircle_react_icons_hi2__WEBPACK_IMPORTED_MODULE_6__.HiUserCircle, {\n                            className: \"text-black text-xl w-10 h-10 rounded-full object-cover hover:border hover:border-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\Navbar.jsx\",\n                            lineNumber: 92,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            onClick: handleHomeClick,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-[17px] font-[500] text-black \",\n                                children: businessName\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\Navbar.jsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\Navbar.jsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\Navbar.jsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleNewConversation,\n                    className: \"px-4 py-2 rounded-lg cursor-pointer\",\n                    title: \"Start New Conversation\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit_react_icons_fi__WEBPACK_IMPORTED_MODULE_7__.FiEdit, {\n                        className: \"text-black text-xl\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\Navbar.jsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\Navbar.jsx\",\n                    lineNumber: 100,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\Navbar.jsx\",\n            lineNumber: 81,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\Navbar.jsx\",\n        lineNumber: 80,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Navbar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Navbar.jsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/ChatContext.jsx":
/*!**************************************!*\
  !*** ./src/contexts/ChatContext.jsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatProvider: () => (/* binding */ ChatProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useChatContext: () => (/* binding */ useChatContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useChatContext,ChatProvider,default auto */ \n\n// Create the context\nconst ChatContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\n// Custom hook to use the chat context\nconst useChatContext = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ChatContext);\n    if (!context) {\n        throw new Error('useChatContext must be used within a ChatProvider');\n    }\n    return context;\n};\n// Chat Provider component\nconst ChatProvider = ({ children })=>{\n    console.log(\"ChatProvider\", children);\n    const [metadata, setMetadata] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: null,\n        keywords: null,\n        description: null,\n        image: null\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Function to update metadata\n    const updateMetadata = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[updateMetadata]\": (newMetadata)=>{\n            setMetadata({\n                \"ChatProvider.useCallback[updateMetadata]\": (prevMetadata)=>({\n                        ...prevMetadata,\n                        ...newMetadata\n                    })\n            }[\"ChatProvider.useCallback[updateMetadata]\"]);\n        }\n    }[\"ChatProvider.useCallback[updateMetadata]\"], []);\n    // Function to clear metadata\n    const clearMetadata = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[clearMetadata]\": ()=>{\n            setMetadata({\n                title: null,\n                keywords: null,\n                description: null,\n                image: null\n            });\n        }\n    }[\"ChatProvider.useCallback[clearMetadata]\"], []);\n    // Function to set loading state\n    const setLoadingState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[setLoadingState]\": (isLoading)=>{\n            setLoading(isLoading);\n        }\n    }[\"ChatProvider.useCallback[setLoadingState]\"], []);\n    // Function to set error state\n    const setErrorState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[setErrorState]\": (errorMessage)=>{\n            setError(errorMessage);\n        }\n    }[\"ChatProvider.useCallback[setErrorState]\"], []);\n    // Function to clear error\n    const clearError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[clearError]\": ()=>{\n            setError(null);\n        }\n    }[\"ChatProvider.useCallback[clearError]\"], []);\n    const value = {\n        metadata,\n        loading,\n        error,\n        updateMetadata,\n        clearMetadata,\n        setLoadingState,\n        setErrorState,\n        clearError,\n        // Helper getters for easy access\n        getTitle: ()=>metadata.title,\n        getKeywords: ()=>metadata.keywords,\n        getDescription: ()=>metadata.description,\n        getImage: ()=>metadata.image,\n        hasMetadata: ()=>metadata.title !== null || metadata.keywords !== null || metadata.description !== null\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\contexts\\\\ChatContext.jsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatContext);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/ChatContext.jsx\n");

/***/ }),

/***/ "(ssr)/./src/utils/config.js":
/*!*****************************!*\
  !*** ./src/utils/config.js ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_ENDPOINTS: () => (/* binding */ API_ENDPOINTS),\n/* harmony export */   EXTERNAL_API_BASE_URL: () => (/* binding */ EXTERNAL_API_BASE_URL),\n/* harmony export */   EXTERNAL_API_ENDPOINTS: () => (/* binding */ EXTERNAL_API_ENDPOINTS),\n/* harmony export */   getExternalApiUrl: () => (/* binding */ getExternalApiUrl),\n/* harmony export */   getSSEUrl: () => (/* binding */ getSSEUrl)\n/* harmony export */ });\nconst EXTERNAL_API_BASE_URL = \"https://api-develop.driply.me/\" || 0;\nconst getExternalApiUrl = ()=>{\n    const url = EXTERNAL_API_BASE_URL;\n    return url.endsWith('/') ? url : `${url}/`;\n};\nconst API_ENDPOINTS = {\n    CHAT_INIT: '/api/chat/init',\n    CHAT_SEND: '/api/chat/send',\n    CHAT_MESSAGES: '/api/chat/messages',\n    CHAT_SETTINGS: '/api/chat/settings'\n};\nconst getSSEUrl = (userId)=>{\n    return `${getExternalApiUrl()}chat-pusher/chat?stream=${userId}`;\n};\nconst EXTERNAL_API_ENDPOINTS = {\n    CHAT_INIT: `${getExternalApiUrl()}api/chat/init`,\n    CHAT_SEND: `${getExternalApiUrl()}api/chat/message`,\n    CHAT_MESSAGES: `${getExternalApiUrl()}api/chat/messages`,\n    CHAT_SETTINGS: `${getExternalApiUrl()}api/chat/settings`\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/config.js\n");

/***/ }),

/***/ "(ssr)/./src/utils/constants.js":
/*!********************************!*\
  !*** ./src/utils/constants.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APP_NAME: () => (/* binding */ APP_NAME),\n/* harmony export */   SUGGESTION_CARDS: () => (/* binding */ SUGGESTION_CARDS)\n/* harmony export */ });\nconst APP_NAME = 'DRIPLY';\nconst SUGGESTION_CARDS = [\n    {\n        title: \"Design a schema\",\n        subtitle: \"for an online merch store\"\n    },\n    {\n        title: \"Explain airplane\",\n        subtitle: \"to someone 5 years old\"\n    },\n    {\n        title: \"Create a work plan\",\n        subtitle: \"for beginners at home\"\n    },\n    {\n        title: \"Write a recipe\",\n        subtitle: \"for chocolate chip cookies\"\n    },\n    {\n        title: \"Plan a budget\",\n        subtitle: \"for a weekend trip\"\n    },\n    {\n        title: \"Learn JavaScript \",\n        subtitle: \"basic concepts \"\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvdXRpbHMvY29uc3RhbnRzLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQ08sTUFBTUEsV0FBVyxTQUFRO0FBR3pCLE1BQU1DLG1CQUFtQjtJQUM5QjtRQUNFQyxPQUFPO1FBQ1BDLFVBQVU7SUFDWjtJQUNBO1FBQ0VELE9BQU87UUFDUEMsVUFBVTtJQUNaO0lBQ0E7UUFDRUQsT0FBTztRQUNQQyxVQUFVO0lBQ1o7SUFDQTtRQUNFRCxPQUFPO1FBQ1BDLFVBQVU7SUFDWjtJQUNBO1FBQ0VELE9BQU87UUFDUEMsVUFBVTtJQUNaO0lBQ0E7UUFDRUQsT0FBTztRQUNQQyxVQUFVO0lBQ1o7Q0FDRCIsInNvdXJjZXMiOlsiRDpcXExhcHRvcCBkYXRhXFxEUklQTFktQ0hBVFxcY2hhdC1uZXh0XFxzcmNcXHV0aWxzXFxjb25zdGFudHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXHJcbmV4cG9ydCBjb25zdCBBUFBfTkFNRSA9ICdEUklQTFknXHJcblxyXG5cclxuZXhwb3J0IGNvbnN0IFNVR0dFU1RJT05fQ0FSRFMgPSBbXHJcbiAge1xyXG4gICAgdGl0bGU6IFwiRGVzaWduIGEgc2NoZW1hXCIsXHJcbiAgICBzdWJ0aXRsZTogXCJmb3IgYW4gb25saW5lIG1lcmNoIHN0b3JlXCJcclxuICB9LFxyXG4gIHtcclxuICAgIHRpdGxlOiBcIkV4cGxhaW4gYWlycGxhbmVcIixcclxuICAgIHN1YnRpdGxlOiBcInRvIHNvbWVvbmUgNSB5ZWFycyBvbGRcIlxyXG4gIH0sXHJcbiAge1xyXG4gICAgdGl0bGU6IFwiQ3JlYXRlIGEgd29yayBwbGFuXCIsXHJcbiAgICBzdWJ0aXRsZTogXCJmb3IgYmVnaW5uZXJzIGF0IGhvbWVcIlxyXG4gIH0sXHJcbiAge1xyXG4gICAgdGl0bGU6IFwiV3JpdGUgYSByZWNpcGVcIixcclxuICAgIHN1YnRpdGxlOiBcImZvciBjaG9jb2xhdGUgY2hpcCBjb29raWVzXCJcclxuICB9LFxyXG4gIHtcclxuICAgIHRpdGxlOiBcIlBsYW4gYSBidWRnZXRcIixcclxuICAgIHN1YnRpdGxlOiBcImZvciBhIHdlZWtlbmQgdHJpcFwiXHJcbiAgfSxcclxuICB7XHJcbiAgICB0aXRsZTogXCJMZWFybiBKYXZhU2NyaXB0IFwiLFxyXG4gICAgc3VidGl0bGU6IFwiYmFzaWMgY29uY2VwdHMgXCJcclxuICB9XHJcbl1cclxuXHJcblxyXG4iXSwibmFtZXMiOlsiQVBQX05BTUUiLCJTVUdHRVNUSU9OX0NBUkRTIiwidGl0bGUiLCJzdWJ0aXRsZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/constants.js\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "node:os":
/*!**************************!*\
  !*** external "node:os" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:os");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:tty":
/*!***************************!*\
  !*** external "node:tty" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:tty");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/axios","vendor-chunks/asynckit","vendor-chunks/math-intrinsics","vendor-chunks/react-icons","vendor-chunks/es-errors","vendor-chunks/@swc","vendor-chunks/form-data","vendor-chunks/call-bind-apply-helpers","vendor-chunks/debug","vendor-chunks/get-proto","vendor-chunks/styled-jsx","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/follow-redirects","vendor-chunks/supports-color","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/get-intrinsic","vendor-chunks/es-set-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/delayed-stream","vendor-chunks/combined-stream"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Bslug%5D%2Fpage&page=%2F%5Bslug%5D%2Fpage&appPaths=%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2F%5Bslug%5D%2Fpage.js&appDir=D%3A%5CLaptop%20data%5CDRIPLY-CHAT%5Cchat-next%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CLaptop%20data%5CDRIPLY-CHAT%5Cchat-next&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();