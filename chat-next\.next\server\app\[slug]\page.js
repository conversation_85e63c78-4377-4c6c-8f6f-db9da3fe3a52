/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/[slug]/page";
exports.ids = ["app/[slug]/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Bslug%5D%2Fpage&page=%2F%5Bslug%5D%2Fpage&appPaths=%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2F%5Bslug%5D%2Fpage.js&appDir=D%3A%5CLaptop%20data%5CDRIPLY-CHAT%5Cchat-next%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CLaptop%20data%5CDRIPLY-CHAT%5Cchat-next&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Bslug%5D%2Fpage&page=%2F%5Bslug%5D%2Fpage&appPaths=%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2F%5Bslug%5D%2Fpage.js&appDir=D%3A%5CLaptop%20data%5CDRIPLY-CHAT%5Cchat-next%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CLaptop%20data%5CDRIPLY-CHAT%5Cchat-next&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.js */ \"(rsc)/./src/app/layout.js\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[slug]/page.js */ \"(rsc)/./src/app/[slug]/page.js\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '[slug]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\app\\\\[slug]\\\\page.js\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\app\\\\layout.js\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\app\\\\[slug]\\\\page.js\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/[slug]/page\",\n        pathname: \"/[slug]\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Bslug%5D%2Fpage&page=%2F%5Bslug%5D%2Fpage&appPaths=%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2F%5Bslug%5D%2Fpage.js&appDir=D%3A%5CLaptop%20data%5CDRIPLY-CHAT%5Cchat-next%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CLaptop%20data%5CDRIPLY-CHAT%5Cchat-next&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNMYXB0b3AlMjBkYXRhJTVDJTVDRFJJUExZLUNIQVQlNUMlNUNjaGF0LW5leHQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjbGllbnQtcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDTGFwdG9wJTIwZGF0YSU1QyU1Q0RSSVBMWS1DSEFUJTVDJTVDY2hhdC1uZXh0JTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDY2xpZW50LXNlZ21lbnQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q0xhcHRvcCUyMGRhdGElNUMlNUNEUklQTFktQ0hBVCU1QyU1Q2NoYXQtbmV4dCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNMYXB0b3AlMjBkYXRhJTVDJTVDRFJJUExZLUNIQVQlNUMlNUNjaGF0LW5leHQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNodHRwLWFjY2Vzcy1mYWxsYmFjayU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNMYXB0b3AlMjBkYXRhJTVDJTVDRFJJUExZLUNIQVQlNUMlNUNjaGF0LW5leHQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNsYXlvdXQtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNMYXB0b3AlMjBkYXRhJTVDJTVDRFJJUExZLUNIQVQlNUMlNUNjaGF0LW5leHQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q2FzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNMYXB0b3AlMjBkYXRhJTVDJTVDRFJJUExZLUNIQVQlNUMlNUNjaGF0LW5leHQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q21ldGFkYXRhLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNMYXB0b3AlMjBkYXRhJTVDJTVDRFJJUExZLUNIQVQlNUMlNUNjaGF0LW5leHQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvT0FBMEk7QUFDMUk7QUFDQSwwT0FBNkk7QUFDN0k7QUFDQSwwT0FBNkk7QUFDN0k7QUFDQSxvUkFBbUs7QUFDbks7QUFDQSx3T0FBNEk7QUFDNUk7QUFDQSw0UEFBdUo7QUFDdko7QUFDQSxrUUFBMEo7QUFDMUo7QUFDQSxzUUFBMkoiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXExhcHRvcCBkYXRhXFxcXERSSVBMWS1DSEFUXFxcXGNoYXQtbmV4dFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGNsaWVudC1wYWdlLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxMYXB0b3AgZGF0YVxcXFxEUklQTFktQ0hBVFxcXFxjaGF0LW5leHRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxjbGllbnQtc2VnbWVudC5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcTGFwdG9wIGRhdGFcXFxcRFJJUExZLUNIQVRcXFxcY2hhdC1uZXh0XFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXExhcHRvcCBkYXRhXFxcXERSSVBMWS1DSEFUXFxcXGNoYXQtbmV4dFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGh0dHAtYWNjZXNzLWZhbGxiYWNrXFxcXGVycm9yLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxMYXB0b3AgZGF0YVxcXFxEUklQTFktQ0hBVFxcXFxjaGF0LW5leHRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxsYXlvdXQtcm91dGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxMYXB0b3AgZGF0YVxcXFxEUklQTFktQ0hBVFxcXFxjaGF0LW5leHRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxtZXRhZGF0YVxcXFxhc3luYy1tZXRhZGF0YS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcTGFwdG9wIGRhdGFcXFxcRFJJUExZLUNIQVRcXFxcY2hhdC1uZXh0XFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbWV0YWRhdGFcXFxcbWV0YWRhdGEtYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXExhcHRvcCBkYXRhXFxcXERSSVBMWS1DSEFUXFxcXGNoYXQtbmV4dFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXHJlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Wix_Madefor_Text%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-wix-madefor-text%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22wixMadeforText%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CClientWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Wix_Madefor_Text%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-wix-madefor-text%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22wixMadeforText%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CClientWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ClientWrapper.jsx */ \"(rsc)/./src/components/ClientWrapper.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Wix_Madefor_Text%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-wix-madefor-text%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22wixMadeforText%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CClientWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CChatInterfaceWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CChatInterfaceWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ChatInterfaceWrapper.jsx */ \"(rsc)/./src/components/ChatInterfaceWrapper.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNMYXB0b3AlMjBkYXRhJTVDJTVDRFJJUExZLUNIQVQlNUMlNUNjaGF0LW5leHQlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDQ2hhdEludGVyZmFjZVdyYXBwZXIuanN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOExBQW9KIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiRDpcXFxcTGFwdG9wIGRhdGFcXFxcRFJJUExZLUNIQVRcXFxcY2hhdC1uZXh0XFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXENoYXRJbnRlcmZhY2VXcmFwcGVyLmpzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CChatInterfaceWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxMYXB0b3AgZGF0YVxcRFJJUExZLUNIQVRcXGNoYXQtbmV4dFxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/[slug]/page.js":
/*!********************************!*\
  !*** ./src/app/[slug]/page.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   generateMetadata: () => (/* binding */ generateMetadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ChatInterfaceWrapper__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/ChatInterfaceWrapper */ \"(rsc)/./src/components/ChatInterfaceWrapper.jsx\");\n\n\n\n// Dynamic metadata generation using your API response data\nasync function generateMetadata({ params }) {\n    const { slug } = await params;\n    // Default fallback metadata\n    let title = `Chat with ${slug} - Driply`;\n    let description = `Start a conversation with ${slug} on Driply platform`;\n    let keywords = [\n        'chat',\n        slug,\n        'driply',\n        'ai',\n        'conversation'\n    ];\n    let businessName = 'Driply';\n    let image = '/og-image.jpg';\n    try {\n        // Fetch the same data that your ChatContext uses\n        const response = await fetch(`${\"https://api-develop.driply.me/\" || 0}/api/chat/settings?customerName=${slug}`, {\n            method: 'GET',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            // Cache for better performance\n            next: {\n                revalidate: 300\n            }\n        });\n        if (response.ok) {\n            const data = await response.json();\n            console.log('[Metadata] API Response:', data);\n            if (data && data.customerName) {\n                // Extract data from your API response structure\n                businessName = data.businessName || 'Driply';\n                const metaData = data.metaData || {};\n                // Use the metadata from your API response\n                title = metaData.title || `${businessName} Chat - ${slug}`;\n                description = metaData.description || `Chat with ${businessName} - Powered by Driply`;\n                // Handle keywords - convert string to array if needed\n                if (metaData.keywords) {\n                    keywords = typeof metaData.keywords === 'string' ? metaData.keywords.split(',').map((k)=>k.trim()) : Array.isArray(metaData.keywords) ? metaData.keywords : [\n                        metaData.keywords\n                    ];\n                } else {\n                    keywords = [\n                        'chat',\n                        slug,\n                        businessName.toLowerCase(),\n                        'driply',\n                        'ai',\n                        'conversation'\n                    ];\n                }\n                // Use custom image if provided\n                if (metaData.image && metaData.image.trim()) {\n                    image = metaData.image;\n                }\n                console.log('[Metadata] Using dynamic metadata:', {\n                    title,\n                    description,\n                    keywords,\n                    businessName,\n                    image\n                });\n            }\n        } else {\n            console.warn('[Metadata] API request failed, using fallback metadata');\n        }\n    } catch (error) {\n        console.warn('[Metadata] Error fetching dynamic metadata:', error.message);\n    // Will use fallback metadata\n    }\n    // Return the complete metadata object\n    return {\n        title: title,\n        description: description,\n        keywords: keywords,\n        openGraph: {\n            title: title,\n            description: description,\n            type: 'website',\n            url: `https://yourdomain.com/${slug}`,\n            siteName: businessName,\n            images: [\n                {\n                    url: image,\n                    width: 1200,\n                    height: 630,\n                    alt: `${businessName} Chat - ${slug}`\n                }\n            ],\n            locale: 'en_US'\n        },\n        twitter: {\n            card: 'summary_large_image',\n            title: title,\n            description: description,\n            images: [\n                image\n            ]\n        },\n        robots: {\n            index: true,\n            follow: true\n        },\n        authors: [\n            {\n                name: businessName\n            }\n        ],\n        creator: businessName,\n        publisher: 'Driply'\n    };\n}\n// Server component - no \"use client\" directive\nconst page = async ({ params, searchParams })=>{\n    const { slug } = await params;\n    const queryParams = await searchParams;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatInterfaceWrapper__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            slug: slug,\n            query: queryParams\n        }, void 0, false, {\n            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\app\\\\[slug]\\\\page.js\",\n            lineNumber: 110,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\app\\\\[slug]\\\\page.js\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (page);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/[slug]/page.js\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"f57a3c65339e\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcTGFwdG9wIGRhdGFcXERSSVBMWS1DSEFUXFxjaGF0LW5leHRcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImY1N2EzYzY1MzM5ZVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.js":
/*!***************************!*\
  !*** ./src/app/layout.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.js\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.js\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_js_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.js\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.js\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_js_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Wix_Madefor_Text_arguments_variable_font_wix_madefor_text_subsets_latin_variableName_wixMadeforText___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.js\",\"import\":\"Wix_Madefor_Text\",\"arguments\":[{\"variable\":\"--font-wix-madefor-text\",\"subsets\":[\"latin\"]}],\"variableName\":\"wixMadeforText\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.js\\\",\\\"import\\\":\\\"Wix_Madefor_Text\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-wix-madefor-text\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"wixMadeforText\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Wix_Madefor_Text_arguments_variable_font_wix_madefor_text_subsets_latin_variableName_wixMadeforText___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_js_import_Wix_Madefor_Text_arguments_variable_font_wix_madefor_text_subsets_latin_variableName_wixMadeforText___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_ClientWrapper__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/ClientWrapper */ \"(rsc)/./src/components/ClientWrapper.jsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: {\n        default: \"Driply - AI Chat Platform\",\n        template: \"%s | Driply\"\n    },\n    description: \"Driply is an advanced AI chat platform that provides intelligent conversations and seamless user experience.\",\n    keywords: [\n        \"AI\",\n        \"chat\",\n        \"conversation\",\n        \"driply\",\n        \"artificial intelligence\"\n    ],\n    authors: [\n        {\n            name: \"Driply Team\"\n        }\n    ],\n    creator: \"Driply\",\n    publisher: \"Driply\",\n    robots: {\n        index: true,\n        follow: true\n    },\n    openGraph: {\n        type: \"website\",\n        locale: \"en_US\",\n        url: \"https://yourdomain.com\",\n        siteName: \"Driply\",\n        title: \"Driply - AI Chat Platform\",\n        description: \"Driply is an advanced AI chat platform that provides intelligent conversations and seamless user experience.\",\n        images: [\n            {\n                url: \"/og-image.jpg\",\n                width: 1200,\n                height: 630,\n                alt: \"Driply AI Chat Platform\"\n            }\n        ]\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"Driply - AI Chat Platform\",\n        description: \"Driply is an advanced AI chat platform that provides intelligent conversations and seamless user experience.\",\n        images: [\n            \"/og-image.jpg\"\n        ]\n    },\n    viewport: \"width=device-width, initial-scale=1\",\n    themeColor: \"#000000\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_js_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default().variable)} ${(next_font_google_target_css_path_src_app_layout_js_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default().variable)} ${(next_font_google_target_css_path_src_app_layout_js_import_Wix_Madefor_Text_arguments_variable_font_wix_madefor_text_subsets_latin_variableName_wixMadeforText___WEBPACK_IMPORTED_MODULE_5___default().variable)} antialiased`,\n            suppressHydrationWarning: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientWrapper__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\app\\\\layout.js\",\n                lineNumber: 71,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\app\\\\layout.js\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\app\\\\layout.js\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBSU1BO0FBS0FDO0FBS0FDO0FBYmlCO0FBQ2lDO0FBaUJqRCxNQUFNRSxXQUFXO0lBQ3RCQyxPQUFPO1FBQ0xDLFNBQVM7UUFDVEMsVUFBVTtJQUNaO0lBQ0FDLGFBQ0U7SUFDRkMsVUFBVTtRQUFDO1FBQU07UUFBUTtRQUFnQjtRQUFVO0tBQTBCO0lBQzdFQyxTQUFTO1FBQUM7WUFBRUMsTUFBTTtRQUFjO0tBQUU7SUFDbENDLFNBQVM7SUFDVEMsV0FBVztJQUNYQyxRQUFRO1FBQ05DLE9BQU87UUFDUEMsUUFBUTtJQUNWO0lBQ0FDLFdBQVc7UUFDVEMsTUFBTTtRQUNOQyxRQUFRO1FBQ1JDLEtBQUs7UUFDTEMsVUFBVTtRQUNWaEIsT0FBTztRQUNQRyxhQUNFO1FBQ0ZjLFFBQVE7WUFDTjtnQkFDRUYsS0FBSztnQkFDTEcsT0FBTztnQkFDUEMsUUFBUTtnQkFDUkMsS0FBSztZQUNQO1NBQ0Q7SUFDSDtJQUNBQyxTQUFTO1FBQ1BDLE1BQU07UUFDTnRCLE9BQU87UUFDUEcsYUFDRTtRQUNGYyxRQUFRO1lBQUM7U0FBZ0I7SUFDM0I7SUFDQU0sVUFBVTtJQUNWQyxZQUFZO0FBQ2QsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFBRUMsUUFBUSxFQUFFO0lBQzdDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUNDQyxXQUFXLEdBQUduQywwTEFBa0IsQ0FBQyxDQUFDLEVBQUVDLCtMQUFrQixDQUFDLENBQUMsRUFBRUMsZ05BQXVCLENBQUMsWUFBWSxDQUFDO1lBQy9GbUMsMEJBQTBCO3NCQUcxQiw0RUFBQ2xDLGlFQUFhQTswQkFBRTRCOzs7Ozs7Ozs7Ozs7Ozs7O0FBSXhCIiwic291cmNlcyI6WyJEOlxcTGFwdG9wIGRhdGFcXERSSVBMWS1DSEFUXFxjaGF0LW5leHRcXHNyY1xcYXBwXFxsYXlvdXQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgR2Vpc3QsIEdlaXN0X01vbm8sIFdpeF9NYWRlZm9yX1RleHQgfSBmcm9tIFwibmV4dC9mb250L2dvb2dsZVwiO1xyXG5pbXBvcnQgXCIuL2dsb2JhbHMuY3NzXCI7XHJcbmltcG9ydCBDbGllbnRXcmFwcGVyIGZyb20gXCIuLi9jb21wb25lbnRzL0NsaWVudFdyYXBwZXJcIjtcclxuXHJcbmNvbnN0IGdlaXN0U2FucyA9IEdlaXN0KHtcclxuICB2YXJpYWJsZTogXCItLWZvbnQtZ2Vpc3Qtc2Fuc1wiLFxyXG4gIHN1YnNldHM6IFtcImxhdGluXCJdLFxyXG59KTtcclxuXHJcbmNvbnN0IGdlaXN0TW9ubyA9IEdlaXN0X01vbm8oe1xyXG4gIHZhcmlhYmxlOiBcIi0tZm9udC1nZWlzdC1tb25vXCIsXHJcbiAgc3Vic2V0czogW1wibGF0aW5cIl0sXHJcbn0pO1xyXG5cclxuY29uc3Qgd2l4TWFkZWZvclRleHQgPSBXaXhfTWFkZWZvcl9UZXh0KHtcclxuICB2YXJpYWJsZTogXCItLWZvbnQtd2l4LW1hZGVmb3ItdGV4dFwiLFxyXG4gIHN1YnNldHM6IFtcImxhdGluXCJdLFxyXG59KTtcclxuXHJcbmV4cG9ydCBjb25zdCBtZXRhZGF0YSA9IHtcclxuICB0aXRsZToge1xyXG4gICAgZGVmYXVsdDogXCJEcmlwbHkgLSBBSSBDaGF0IFBsYXRmb3JtXCIsXHJcbiAgICB0ZW1wbGF0ZTogXCIlcyB8IERyaXBseVwiLFxyXG4gIH0sXHJcbiAgZGVzY3JpcHRpb246XHJcbiAgICBcIkRyaXBseSBpcyBhbiBhZHZhbmNlZCBBSSBjaGF0IHBsYXRmb3JtIHRoYXQgcHJvdmlkZXMgaW50ZWxsaWdlbnQgY29udmVyc2F0aW9ucyBhbmQgc2VhbWxlc3MgdXNlciBleHBlcmllbmNlLlwiLFxyXG4gIGtleXdvcmRzOiBbXCJBSVwiLCBcImNoYXRcIiwgXCJjb252ZXJzYXRpb25cIiwgXCJkcmlwbHlcIiwgXCJhcnRpZmljaWFsIGludGVsbGlnZW5jZVwiXSxcclxuICBhdXRob3JzOiBbeyBuYW1lOiBcIkRyaXBseSBUZWFtXCIgfV0sXHJcbiAgY3JlYXRvcjogXCJEcmlwbHlcIixcclxuICBwdWJsaXNoZXI6IFwiRHJpcGx5XCIsXHJcbiAgcm9ib3RzOiB7XHJcbiAgICBpbmRleDogdHJ1ZSxcclxuICAgIGZvbGxvdzogdHJ1ZSxcclxuICB9LFxyXG4gIG9wZW5HcmFwaDoge1xyXG4gICAgdHlwZTogXCJ3ZWJzaXRlXCIsXHJcbiAgICBsb2NhbGU6IFwiZW5fVVNcIixcclxuICAgIHVybDogXCJodHRwczovL3lvdXJkb21haW4uY29tXCIsXHJcbiAgICBzaXRlTmFtZTogXCJEcmlwbHlcIixcclxuICAgIHRpdGxlOiBcIkRyaXBseSAtIEFJIENoYXQgUGxhdGZvcm1cIixcclxuICAgIGRlc2NyaXB0aW9uOlxyXG4gICAgICBcIkRyaXBseSBpcyBhbiBhZHZhbmNlZCBBSSBjaGF0IHBsYXRmb3JtIHRoYXQgcHJvdmlkZXMgaW50ZWxsaWdlbnQgY29udmVyc2F0aW9ucyBhbmQgc2VhbWxlc3MgdXNlciBleHBlcmllbmNlLlwiLFxyXG4gICAgaW1hZ2VzOiBbXHJcbiAgICAgIHtcclxuICAgICAgICB1cmw6IFwiL29nLWltYWdlLmpwZ1wiLCAvLyBNYWtlIHN1cmUgdG8gYWRkIHRoaXMgaW1hZ2UgdG8geW91ciBwdWJsaWMgZm9sZGVyXHJcbiAgICAgICAgd2lkdGg6IDEyMDAsXHJcbiAgICAgICAgaGVpZ2h0OiA2MzAsXHJcbiAgICAgICAgYWx0OiBcIkRyaXBseSBBSSBDaGF0IFBsYXRmb3JtXCIsXHJcbiAgICAgIH0sXHJcbiAgICBdLFxyXG4gIH0sXHJcbiAgdHdpdHRlcjoge1xyXG4gICAgY2FyZDogXCJzdW1tYXJ5X2xhcmdlX2ltYWdlXCIsXHJcbiAgICB0aXRsZTogXCJEcmlwbHkgLSBBSSBDaGF0IFBsYXRmb3JtXCIsXHJcbiAgICBkZXNjcmlwdGlvbjpcclxuICAgICAgXCJEcmlwbHkgaXMgYW4gYWR2YW5jZWQgQUkgY2hhdCBwbGF0Zm9ybSB0aGF0IHByb3ZpZGVzIGludGVsbGlnZW50IGNvbnZlcnNhdGlvbnMgYW5kIHNlYW1sZXNzIHVzZXIgZXhwZXJpZW5jZS5cIixcclxuICAgIGltYWdlczogW1wiL29nLWltYWdlLmpwZ1wiXSxcclxuICB9LFxyXG4gIHZpZXdwb3J0OiBcIndpZHRoPWRldmljZS13aWR0aCwgaW5pdGlhbC1zY2FsZT0xXCIsXHJcbiAgdGhlbWVDb2xvcjogXCIjMDAwMDAwXCIsXHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHsgY2hpbGRyZW4gfSkge1xyXG4gIHJldHVybiAoXHJcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cclxuICAgICAgPGJvZHlcclxuICAgICAgICBjbGFzc05hbWU9e2Ake2dlaXN0U2Fucy52YXJpYWJsZX0gJHtnZWlzdE1vbm8udmFyaWFibGV9ICR7d2l4TWFkZWZvclRleHQudmFyaWFibGV9IGFudGlhbGlhc2VkYH1cclxuICAgICAgICBzdXBwcmVzc0h5ZHJhdGlvbldhcm5pbmc9e3RydWV9XHJcbiAgICAgID5cclxuICAgICAgICB7LyogVXNlIENsaWVudFdyYXBwZXIgdG8gc2VwYXJhdGUgY2xpZW50IGNvbXBvbmVudHMgZnJvbSBzZXJ2ZXIgbGF5b3V0ICovfVxyXG4gICAgICAgIDxDbGllbnRXcmFwcGVyPntjaGlsZHJlbn08L0NsaWVudFdyYXBwZXI+XHJcbiAgICAgIDwvYm9keT5cclxuICAgIDwvaHRtbD5cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJnZWlzdFNhbnMiLCJnZWlzdE1vbm8iLCJ3aXhNYWRlZm9yVGV4dCIsIkNsaWVudFdyYXBwZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVmYXVsdCIsInRlbXBsYXRlIiwiZGVzY3JpcHRpb24iLCJrZXl3b3JkcyIsImF1dGhvcnMiLCJuYW1lIiwiY3JlYXRvciIsInB1Ymxpc2hlciIsInJvYm90cyIsImluZGV4IiwiZm9sbG93Iiwib3BlbkdyYXBoIiwidHlwZSIsImxvY2FsZSIsInVybCIsInNpdGVOYW1lIiwiaW1hZ2VzIiwid2lkdGgiLCJoZWlnaHQiLCJhbHQiLCJ0d2l0dGVyIiwiY2FyZCIsInZpZXdwb3J0IiwidGhlbWVDb2xvciIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSIsInZhcmlhYmxlIiwic3VwcHJlc3NIeWRyYXRpb25XYXJuaW5nIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.js\n");

/***/ }),

/***/ "(rsc)/./src/components/ChatInterfaceWrapper.jsx":
/*!*************************************************!*\
  !*** ./src/components/ChatInterfaceWrapper.jsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterfaceWrapper.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Laptop data\\DRIPLY-CHAT\\chat-next\\src\\components\\ChatInterfaceWrapper.jsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/ClientWrapper.jsx":
/*!******************************************!*\
  !*** ./src/components/ClientWrapper.jsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ClientWrapper.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Laptop data\\DRIPLY-CHAT\\chat-next\\src\\components\\ClientWrapper.jsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNMYXB0b3AlMjBkYXRhJTVDJTVDRFJJUExZLUNIQVQlNUMlNUNjaGF0LW5leHQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjbGllbnQtcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDTGFwdG9wJTIwZGF0YSU1QyU1Q0RSSVBMWS1DSEFUJTVDJTVDY2hhdC1uZXh0JTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDY2xpZW50LXNlZ21lbnQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q0xhcHRvcCUyMGRhdGElNUMlNUNEUklQTFktQ0hBVCU1QyU1Q2NoYXQtbmV4dCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNMYXB0b3AlMjBkYXRhJTVDJTVDRFJJUExZLUNIQVQlNUMlNUNjaGF0LW5leHQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNodHRwLWFjY2Vzcy1mYWxsYmFjayU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNMYXB0b3AlMjBkYXRhJTVDJTVDRFJJUExZLUNIQVQlNUMlNUNjaGF0LW5leHQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNsYXlvdXQtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNMYXB0b3AlMjBkYXRhJTVDJTVDRFJJUExZLUNIQVQlNUMlNUNjaGF0LW5leHQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q2FzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNMYXB0b3AlMjBkYXRhJTVDJTVDRFJJUExZLUNIQVQlNUMlNUNjaGF0LW5leHQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q21ldGFkYXRhLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNMYXB0b3AlMjBkYXRhJTVDJTVDRFJJUExZLUNIQVQlNUMlNUNjaGF0LW5leHQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvT0FBMEk7QUFDMUk7QUFDQSwwT0FBNkk7QUFDN0k7QUFDQSwwT0FBNkk7QUFDN0k7QUFDQSxvUkFBbUs7QUFDbks7QUFDQSx3T0FBNEk7QUFDNUk7QUFDQSw0UEFBdUo7QUFDdko7QUFDQSxrUUFBMEo7QUFDMUo7QUFDQSxzUUFBMkoiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXExhcHRvcCBkYXRhXFxcXERSSVBMWS1DSEFUXFxcXGNoYXQtbmV4dFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGNsaWVudC1wYWdlLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxMYXB0b3AgZGF0YVxcXFxEUklQTFktQ0hBVFxcXFxjaGF0LW5leHRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxjbGllbnQtc2VnbWVudC5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcTGFwdG9wIGRhdGFcXFxcRFJJUExZLUNIQVRcXFxcY2hhdC1uZXh0XFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXExhcHRvcCBkYXRhXFxcXERSSVBMWS1DSEFUXFxcXGNoYXQtbmV4dFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGh0dHAtYWNjZXNzLWZhbGxiYWNrXFxcXGVycm9yLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxMYXB0b3AgZGF0YVxcXFxEUklQTFktQ0hBVFxcXFxjaGF0LW5leHRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxsYXlvdXQtcm91dGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxMYXB0b3AgZGF0YVxcXFxEUklQTFktQ0hBVFxcXFxjaGF0LW5leHRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxtZXRhZGF0YVxcXFxhc3luYy1tZXRhZGF0YS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcTGFwdG9wIGRhdGFcXFxcRFJJUExZLUNIQVRcXFxcY2hhdC1uZXh0XFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbWV0YWRhdGFcXFxcbWV0YWRhdGEtYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXExhcHRvcCBkYXRhXFxcXERSSVBMWS1DSEFUXFxcXGNoYXQtbmV4dFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXHJlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Wix_Madefor_Text%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-wix-madefor-text%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22wixMadeforText%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CClientWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Wix_Madefor_Text%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-wix-madefor-text%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22wixMadeforText%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CClientWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ClientWrapper.jsx */ \"(ssr)/./src/components/ClientWrapper.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Wix_Madefor_Text%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-wix-madefor-text%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22wixMadeforText%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CClientWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CChatInterfaceWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CChatInterfaceWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ChatInterfaceWrapper.jsx */ \"(ssr)/./src/components/ChatInterfaceWrapper.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNMYXB0b3AlMjBkYXRhJTVDJTVDRFJJUExZLUNIQVQlNUMlNUNjaGF0LW5leHQlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDQ2hhdEludGVyZmFjZVdyYXBwZXIuanN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOExBQW9KIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiRDpcXFxcTGFwdG9wIGRhdGFcXFxcRFJJUExZLUNIQVRcXFxcY2hhdC1uZXh0XFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXENoYXRJbnRlcmZhY2VXcmFwcGVyLmpzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CChatInterfaceWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/assets/images/logo.png":
/*!************************************!*\
  !*** ./src/assets/images/logo.png ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/logo.425de58c.png\",\"height\":188,\"width\":188,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Flogo.425de58c.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXNzZXRzL2ltYWdlcy9sb2dvLnBuZyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsQ0FBQyw0TEFBNEwiLCJzb3VyY2VzIjpbIkQ6XFxMYXB0b3AgZGF0YVxcRFJJUExZLUNIQVRcXGNoYXQtbmV4dFxcc3JjXFxhc3NldHNcXGltYWdlc1xcbG9nby5wbmciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2xvZ28uNDI1ZGU1OGMucG5nXCIsXCJoZWlnaHRcIjoxODgsXCJ3aWR0aFwiOjE4OCxcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZsb2dvLjQyNWRlNThjLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo4fTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/assets/images/logo.png\n");

/***/ }),

/***/ "(ssr)/./src/components/ChatInterface.jsx":
/*!******************************************!*\
  !*** ./src/components/ChatInterface.jsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(ssr)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/constants */ \"(ssr)/./src/utils/constants.js\");\n/* harmony import */ var _utils_config__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/config */ \"(ssr)/./src/utils/config.js\");\n/* harmony import */ var _barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FaArrowUp,FaChevronLeft,FaChevronRight!=!react-icons/fa6 */ \"(ssr)/./node_modules/react-icons/fa6/index.mjs\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../contexts/ChatContext */ \"(ssr)/./src/contexts/ChatContext.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nconst ChatInterface = ({ slug, query })=>{\n    // Context API\n    const { metadata, updateMetadata, setLoadingState, setErrorState, clearError, settingData, updateSettingData, getCustomerName, getBusinessName, hasMetadata, loading: contextLoading, error: contextError } = (0,_contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__.useChatContext)();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [currentSlide, setCurrentSlide] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [touchStart, setTouchStart] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [touchEnd, setTouchEnd] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isTablet, setIsTablet] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showInitialUI, setShowInitialUI] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isBotTyping, setIsBotTyping] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isBotThinking, setIsBotThinking] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [sseConnection, setSseConnection] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isFirstMessage, setIsFirstMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [shouldScrollToTop, setShouldScrollToTop] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const carouselRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const desktopMessagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const mobileMessagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const tabletMessagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // Debug: Log state updates when they actually happen\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (metadata && Object.keys(metadata).some({\n                \"ChatInterface.useEffect\": (key)=>metadata[key] !== null\n            }[\"ChatInterface.useEffect\"])) {\n                console.log(\"✅ METADATA STATE UPDATED:\", metadata);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        metadata\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (settingData && Object.keys(settingData).some({\n                \"ChatInterface.useEffect\": (key)=>settingData[key] !== null\n            }[\"ChatInterface.useEffect\"])) {\n                console.log(\"✅ SETTING DATA STATE UPDATED:\", settingData);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        settingData\n    ]);\n    // Detect iOS for targeted fixes\n    const isIOS = ()=>{\n        if (false) {}\n        return false;\n    };\n    const username = slug;\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (username && \"undefined\" !== \"undefined\") {}\n        }\n    }[\"ChatInterface.useEffect\"], [\n        username\n    ]);\n    const fetchCustomer = async (customerName)=>{\n        try {\n            setLoadingState(true);\n            clearError();\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(`${_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_SETTINGS}?customerName=${customerName}`);\n            // Extract data from axios response\n            const responseData = response.data;\n            const metadataFromResponse = responseData.metaData;\n            updateSettingData(responseData);\n            updateMetadata(metadataFromResponse);\n            if (responseData && responseData.customerName) {\n                //  localStorage for backward compatibility\n                localStorage.setItem(\"customerName_userId\", responseData.customerName);\n                localStorage.setItem(\"BusinessName\", responseData.businessName);\n                window.dispatchEvent(new CustomEvent(\"businessNameLoaded\", {\n                    detail: {\n                        businessName: responseData.businessName\n                    }\n                }));\n                const existingUserId = localStorage.getItem(\"userID\");\n                if (existingUserId) {\n                    await fetchExistingMessages(existingUserId);\n                } else {\n                    await initializeChatSession();\n                }\n            } else {\n                await initializeChatSession();\n            }\n        } catch (error) {\n            console.error(\"Error fetching customer data:\", error);\n            setErrorState(\"Failed to load customer settings\");\n            // Fallback metadata\n            updateMetadata({\n                customerName: customerName,\n                businessName: \"Driply\"\n            });\n            localStorage.setItem(\"BusinessName\", \"Driply\");\n            window.dispatchEvent(new CustomEvent(\"businessNameLoaded\", {\n                detail: {\n                    businessName: \"Driply\"\n                }\n            }));\n            await initializeChatSession();\n        } finally{\n            setLoadingState(false);\n        }\n    };\n    const fetchExistingMessages = async (userId)=>{\n        // console.log(\"fetchExistingMessages\");\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(`${_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_MESSAGES}?userId=${userId}`);\n            if (response.data && response.data.length > 0) {\n                setMessages(response.data.map((msg)=>({\n                        text: msg.message,\n                        timestamp: new Date(msg.createdAt).getTime(),\n                        type: msg.type,\n                        source: msg.source\n                    })));\n            }\n            connectToSSE(userId);\n        } catch (error) {\n            await initializeChatSession();\n        }\n    };\n    const initializeChatSession = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_INIT, {\n                customerName: username\n            });\n            if (response.data && response.data.userId) {\n                localStorage.setItem(\"userId\", response.data.userId);\n                localStorage.setItem(\"userID\", response.data._id);\n                await fetchExistingMessages(response.data._id);\n            }\n        } catch (error) {\n            if (!localStorage.getItem(\"BusinessName\")) {\n                localStorage.setItem(\"BusinessName\", \"Driply\");\n                window.dispatchEvent(new CustomEvent(\"businessNameLoaded\", {\n                    detail: {\n                        businessName: \"Driply\"\n                    }\n                }));\n            }\n        }\n    };\n    const connectToSSE = (userId)=>{\n        try {\n            if (sseConnection) {\n                sseConnection.close();\n            }\n            const sseUrl = (0,_utils_config__WEBPACK_IMPORTED_MODULE_4__.getSSEUrl)(userId);\n            const eventSource = new EventSource(sseUrl);\n            setSseConnection(eventSource);\n            eventSource.addEventListener(\"message\", ({ data })=>{\n                try {\n                    const contents = JSON.parse(data);\n                    handleSSEMessage(contents);\n                } catch (error) {\n                // Handle parsing error silently\n                }\n            });\n            eventSource.addEventListener(\"error\", ()=>{\n            // Handle connection error silently\n            });\n        } catch (error) {\n        // Handle SSE connection error silently\n        }\n    };\n    const handleSSEMessage = (data)=>{\n        const subtype = data.subType || data.subtype || data.type || \"UNKNOWN\";\n        const content = data.content || \"\";\n        const message = data.message || data.text || \"\";\n        if ([\n            \"TYPING\",\n            \"THINKING\",\n            \"BEHAVIOUR_MESSAGE\"\n        ].includes(subtype)) {\n            switch(subtype){\n                case \"TYPING\":\n                    setIsBotTyping(true);\n                    setIsBotThinking(false);\n                    break;\n                case \"THINKING\":\n                    setIsBotThinking(true);\n                    setIsBotTyping(false);\n                    break;\n                case \"BEHAVIOUR_MESSAGE\":\n                    setIsBotTyping(false);\n                    setIsBotThinking(false);\n                    break;\n            }\n            return;\n        }\n        const displayText = message || content;\n        if (!displayText) return;\n        const displayType = [\n            \"TEXT\",\n            \"MESSAGE\",\n            \"DATA_MESSAGE\"\n        ].includes(subtype) ? subtype : \"UNKNOWN\";\n        setMessages((prev)=>[\n                ...prev,\n                {\n                    text: displayText,\n                    timestamp: Date.now(),\n                    type: displayType,\n                    source: \"BOT\"\n                }\n            ]);\n        setIsBotTyping(false);\n        setIsBotThinking(false);\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (message.trim()) {\n            const userMessage = message.trim();\n            const isFirstMsg = messages.length === 0;\n            setMessages((prev)=>[\n                    ...prev,\n                    {\n                        text: userMessage,\n                        timestamp: Date.now(),\n                        type: \"TEXT\",\n                        source: \"USER\"\n                    }\n                ]);\n            setMessage(\"\");\n            setIsTyping(false);\n            // For first message, set flag to scroll to top\n            if (isFirstMsg) {\n                setShouldScrollToTop(true);\n                setIsFirstMessage(false);\n            }\n            // Show loading dots immediately after sending message\n            setIsBotThinking(true);\n            setIsBotTyping(false);\n            // Reset textarea\n            if (inputRef.current) {\n                inputRef.current.style.height = \"104px\";\n                inputRef.current.scrollTop = 0;\n                inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n                inputRef.current.classList.add(\"reset-height\");\n            }\n            try {\n                const userId = localStorage.getItem(\"userID\");\n                if (userId) {\n                    await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_SEND, {\n                        userId: userId,\n                        message: userMessage,\n                        typs: \"TEXT\",\n                        source: \"USER\",\n                        isTest: query.isTest === \"1\" ? true : false\n                    });\n                }\n            } catch (error) {\n                // Hide loading dots on error\n                setIsBotThinking(false);\n                setIsBotTyping(false);\n                setMessages((prev)=>[\n                        ...prev,\n                        {\n                            text: \"Sorry, there was an error sending your message. Please try again.\",\n                            timestamp: Date.now(),\n                            type: \"TEXT\",\n                            source: \"BOT\"\n                        }\n                    ]);\n            }\n        }\n    };\n    const handleTextareaResize = (textarea)=>{\n        if (textarea) {\n            textarea.style.height = \"104px\";\n            const scrollHeight = textarea.scrollHeight;\n            if (isIOS()) {\n                if (scrollHeight > 48) {\n                    textarea.style.overflowY = \"auto\";\n                } else {\n                    textarea.style.overflowY = \"hidden\";\n                }\n            } else {\n                const newHeight = Math.min(250, Math.max(104, scrollHeight));\n                textarea.style.height = `${newHeight}px`;\n                textarea.style.overflowY = scrollHeight > newHeight ? \"auto\" : \"hidden\";\n            }\n        }\n    };\n    const handleInputChange = (e)=>{\n        const value = e.target.value;\n        setMessage(value);\n        handleTextareaResize(e.target);\n        if (messages.length === 0) {\n            setIsTyping(value.length > 0);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length > 0 && inputRef.current && !message) {\n                const timer = setTimeout({\n                    \"ChatInterface.useEffect.timer\": ()=>{\n                        if (inputRef.current) {\n                            inputRef.current.style.height = \"104px\";\n                            inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n                            inputRef.current.classList.add(\"reset-height\");\n                        }\n                    }\n                }[\"ChatInterface.useEffect.timer\"], 50);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                })[\"ChatInterface.useEffect\"];\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages,\n        message\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length > 0) {\n                handleChatGPTScroll();\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages,\n        isBotTyping,\n        isBotThinking\n    ]);\n    const handleChatGPTScroll = ()=>{\n        setTimeout(()=>{\n            // Determine screen type and get appropriate container\n            const screenWidth = window.innerWidth;\n            const isDesktopLayout = screenWidth >= 1024;\n            const isTabletLayout = screenWidth >= 768 && screenWidth < 1024;\n            let container;\n            if (isDesktopLayout) {\n                container = desktopMessagesContainerRef.current;\n            } else if (isTabletLayout) {\n                container = tabletMessagesContainerRef.current;\n            } else {\n                container = mobileMessagesContainerRef.current;\n            }\n            if (!container) return;\n            if (shouldScrollToTop) {\n                // For first message, scroll to top to show the message there\n                container.scrollTo({\n                    top: 0,\n                    behavior: \"smooth\"\n                });\n                setShouldScrollToTop(false);\n            } else {\n                // Different scrolling behavior for each screen type\n                const messageElements = container.querySelectorAll(\".message-pair\");\n                if (messageElements.length > 0) {\n                    const lastMessagePair = messageElements[messageElements.length - 1];\n                    const containerHeight = container.clientHeight;\n                    const pairHeight = lastMessagePair.offsetHeight;\n                    const pairTop = lastMessagePair.offsetTop;\n                    if (isTabletLayout) {\n                        // Tablet-specific scrolling: Keep messages visible by scrolling to bottom of container\n                        // This ensures both user message and AI response remain visible\n                        const scrollPosition = Math.max(0, pairTop + pairHeight - containerHeight + 100);\n                        container.scrollTo({\n                            top: scrollPosition,\n                            behavior: \"smooth\"\n                        });\n                    } else {\n                        // Desktop and mobile: Center the message pair on screen\n                        const scrollPosition = pairTop - (containerHeight - pairHeight) / 2;\n                        container.scrollTo({\n                            top: Math.max(0, scrollPosition),\n                            behavior: \"smooth\"\n                        });\n                    }\n                }\n            }\n        }, 150);\n    };\n    const renderMessagePairs = ()=>{\n        const pairs = [];\n        const userMessages = messages.filter((msg)=>msg.source === \"USER\");\n        const botMessages = messages.filter((msg)=>msg.source === \"BOT\");\n        // Create pairs of user and bot messages\n        for(let i = 0; i < userMessages.length; i++){\n            const userMsg = userMessages[i];\n            const botMsg = botMessages[i];\n            pairs.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"message-pair flex flex-col justify-start\",\n                style: {\n                    minHeight: i === userMessages.length - 1 ? isMobile ? \"calc(100vh - 200px)\" : \"calc(100vh - 200px)\" : \"\",\n                    // Less padding for first message\n                    paddingTop: i === 0 ? \"1rem\" : \"1rem\",\n                    paddingBottom: \"0rem\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-end mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `text-[16px] font-[400] rounded-3xl max-w-xs lg:max-w-lg break-words hyphens-auto bg-gray-100 ${isMobile ? \"rounded-[15px] px-3 py-2\" : \"px-4 py-3\"}`,\n                            style: {\n                                overflowWrap: \"break-word\",\n                                wordBreak: \"break-word\",\n                                maxWidth: isMobile ? \"90%\" : undefined\n                            },\n                            children: userMsg.text\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 463,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 462,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-start\",\n                        children: botMsg ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `text-[16px] font-[400] rounded-3xl max-w-xs lg:max-w-lg break-words hyphens-auto bg-white ${isMobile ? \"rounded-[15px] px-3 py-2\" : \"px-4 py-3\"}`,\n                            style: {\n                                overflowWrap: \"break-word\",\n                                wordBreak: \"break-word\",\n                                maxWidth: isMobile ? \"90%\" : undefined\n                            },\n                            children: botMsg.text\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 480,\n                            columnNumber: 15\n                        }, undefined) : // Show loading dots for the last pair if bot is thinking/typing\n                        i === userMessages.length - 1 && (isBotTyping || isBotThinking) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `bg-white ${isMobile ? \"px-3 py-2 rounded-[15px]\" : \"px-4 py-3 rounded-3xl\"}`,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-black rounded-full loading-dot\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 504,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-black rounded-full loading-dot\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 505,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-black rounded-full loading-dot\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 506,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 503,\n                                columnNumber: 19\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 496,\n                            columnNumber: 17\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 478,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, `pair-${i}`, true, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 443,\n                columnNumber: 9\n            }, undefined));\n        }\n        return pairs;\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSubmit(e);\n        }\n    };\n    const handleSuggestionClick = async (cardTitle)=>{\n        const isFirstMsg = messages.length === 0;\n        setMessages((prev)=>[\n                ...prev,\n                {\n                    text: cardTitle,\n                    timestamp: Date.now(),\n                    type: \"TEXT\",\n                    source: \"USER\"\n                }\n            ]);\n        setMessage(\"\");\n        setIsTyping(false);\n        // For first message, set flag to scroll to top\n        if (isFirstMsg) {\n            setShouldScrollToTop(true);\n            setIsFirstMessage(false);\n        }\n        // Show loading dots immediately after sending message\n        setIsBotThinking(true);\n        setIsBotTyping(false);\n        if (inputRef.current) {\n            inputRef.current.style.height = \"104px\";\n            inputRef.current.scrollTop = 0;\n            inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n            inputRef.current.classList.add(\"reset-height\");\n        }\n        try {\n            const userId = localStorage.getItem(\"userID\");\n            if (userId) {\n                await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_SEND, {\n                    userId: userId,\n                    message: cardTitle,\n                    customerName: username\n                });\n            }\n        } catch (error) {\n            // Hide loading dots on error\n            setIsBotThinking(false);\n            setIsBotTyping(false);\n            setMessages((prev)=>[\n                    ...prev,\n                    {\n                        text: \"Sorry, there was an error sending your message. Please try again.\",\n                        timestamp: Date.now(),\n                        type: \"TEXT\",\n                        source: \"BOT\"\n                    }\n                ]);\n        }\n    };\n    const getMaxSlides = ()=>{\n        if (isMobile) return settingData?.suggestedTopics.length;\n        return Math.ceil(settingData?.suggestedTopics.length / 2);\n    };\n    const nextSlide = ()=>{\n        if (isMobile) {\n            setCurrentSlide((prev)=>prev < settingData?.suggestedTopics.length - 1 ? prev + 1 : prev);\n        } else {\n            const maxSlides = getMaxSlides();\n            setCurrentSlide((prev)=>(prev + 1) % maxSlides);\n        }\n    };\n    const prevSlide = ()=>{\n        if (isMobile) {\n            setCurrentSlide((prev)=>prev > 0 ? prev - 1 : prev);\n        } else {\n            const maxSlides = getMaxSlides();\n            setCurrentSlide((prev)=>(prev - 1 + maxSlides) % maxSlides);\n        }\n    };\n    const handleNextSlide = ()=>{\n        nextSlide();\n    };\n    const handlePrevSlide = ()=>{\n        prevSlide();\n    };\n    const handleTouchStart = (e)=>{\n        setTouchStart(e.targetTouches[0].clientX);\n    };\n    const handleTouchMove = (e)=>{\n        setTouchEnd(e.targetTouches[0].clientX);\n    };\n    const handleTouchEnd = ()=>{\n        if (!touchStart || !touchEnd) return;\n        const swipeDistance = touchStart - touchEnd;\n        const minSwipeDistance = 50;\n        if (swipeDistance > minSwipeDistance) {\n            nextSlide();\n        }\n        if (swipeDistance < -minSwipeDistance) {\n            prevSlide();\n        }\n        setTouchStart(0);\n        setTouchEnd(0);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length === 0) {\n                setShowInitialUI(false);\n                const timer = setTimeout({\n                    \"ChatInterface.useEffect.timer\": ()=>setShowInitialUI(true)\n                }[\"ChatInterface.useEffect.timer\"], 60);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                })[\"ChatInterface.useEffect\"];\n            } else {\n                setShowInitialUI(false);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages.length\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const checkScreenSize = {\n                \"ChatInterface.useEffect.checkScreenSize\": ()=>{\n                    if (false) {}\n                }\n            }[\"ChatInterface.useEffect.checkScreenSize\"];\n            checkScreenSize();\n            const handleResize = {\n                \"ChatInterface.useEffect.handleResize\": ()=>{\n                    setCurrentSlide(0);\n                    checkScreenSize();\n                }\n            }[\"ChatInterface.useEffect.handleResize\"];\n            if (false) {}\n        }\n    }[\"ChatInterface.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (inputRef.current && messages.length === 0) {\n                const shouldAutoFocus = window.innerWidth >= 768;\n                if (shouldAutoFocus) {\n                    const timer = setTimeout({\n                        \"ChatInterface.useEffect.timer\": ()=>{\n                            inputRef.current?.focus();\n                        }\n                    }[\"ChatInterface.useEffect.timer\"], 100);\n                    return ({\n                        \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                    })[\"ChatInterface.useEffect\"];\n                }\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages.length\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            return ({\n                \"ChatInterface.useEffect\": ()=>{\n                    if (sseConnection) {\n                        sseConnection.close();\n                    }\n                }\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], [\n        sseConnection\n    ]);\n    // Check if settingData has been populated with actual data\n    const hasSettingData = settingData && Object.keys(settingData).some((key)=>settingData[key] !== null);\n    console.log(\"🔍 RENDER CHECK:\", {\n        settingData,\n        hasSettingData,\n        contextLoading,\n        contextError\n    });\n    // Show loading state while waiting for data\n    if (!hasSettingData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white flex flex-col min-h-screen items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 705,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading chat settings...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 706,\n                        columnNumber: 11\n                    }, undefined),\n                    contextError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-500 mt-2\",\n                        children: [\n                            \"Error: \",\n                            contextError\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 708,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 704,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n            lineNumber: 703,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"bg-white flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"hidden lg:flex flex-1 flex-col px-4 \",\n                children: messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex flex-1 items-center justify-center min-h-[calc(100vh-64px)] mt-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex flex-col items-center w-[768px] justify-center mx-auto \",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    transitionDelay: showInitialUI ? \"40ms\" : \"0ms\"\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + `text-4xl text-gray-900 mb-6 text-center transition-all duration-700 ${showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"}`,\n                                children: \"How can I help you?\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 722,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                style: {\n                                    transitionDelay: showInitialUI ? \"120ms\" : \"0ms\"\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + `relative w-full mb-6 flex flex-col items-center transition-all duration-500 ${showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        ref: messages.length === 0 ? inputRef : null,\n                                        value: message,\n                                        onChange: handleInputChange,\n                                        onKeyDown: handleKeyPress,\n                                        placeholder: \"Ask anything\",\n                                        autoFocus: messages.length === 0,\n                                        rows: 1,\n                                        style: {\n                                            boxSizing: \"border-box\",\n                                            zIndex: 1001,\n                                            position: \"relative\"\n                                        },\n                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"pt-3 px-4 lg:p-4 pb-16 pr-12 w-full min-h-[104px] max-h-[250px] overflow-y-auto hide-scrollbar text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base shadow-md resize-none reset-height\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 742,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: message.trim().length === 0,\n                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + `absolute right-3 bottom-3 w-9 h-9 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation ${message.trim().length === 0 ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                            className: \"w-4 h-4 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 766,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 757,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 733,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    transitionDelay: showInitialUI ? \"200ms\" : \"0ms\",\n                                    zIndex: 10\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + `relative w-full max-w-2xl transition-all duration-500 ease-in-out ${isTyping ? \"opacity-0 pointer-events-none\" : showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: carouselRef,\n                                            style: {\n                                                transform: `translateX(-${currentSlide * 100}%)`\n                                            },\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex transition-transform duration-300 ease-in-out\",\n                                            children: Array.from({\n                                                length: Math.ceil(settingData?.suggestedTopics.length / 2)\n                                            }).map((_, slideIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"w-full flex-shrink-0 flex gap-2 justify-center\",\n                                                    children: settingData?.suggestedTopics.slice(slideIndex * 2, slideIndex * 2 + 2).map((card, cardIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleSuggestionClick(card?.question),\n                                                            style: {\n                                                                width: \"fit-content\"\n                                                            },\n                                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"py-2 px-3 rounded-[8px] bg-[#f6f6f6] text-left group transition-all duration-200 touch-manipulation\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-[16px] font-[600] text-black mb-0.5 leading-tight whitespace-nowrap\",\n                                                                    children: card.question\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                    lineNumber: 810,\n                                                                    columnNumber: 31\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-[16px] text-gray-500 leading-tight whitespace-nowrap\",\n                                                                    children: card.subtitle\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                    lineNumber: 813,\n                                                                    columnNumber: 31\n                                                                }, undefined)\n                                                            ]\n                                                        }, slideIndex * 2 + cardIndex, true, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 802,\n                                                            columnNumber: 29\n                                                        }, undefined))\n                                                }, slideIndex, false, {\n                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                    lineNumber: 795,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 783,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 782,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handlePrevSlide,\n                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"absolute left-28 top-1/2 transform -translate-y-1/2 -translate-x-6 w-8 h-8 bg-white border border-gray-200 rounded-full flex items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronLeft, {\n                                            className: \"w-3 h-3 text-gray-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 826,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 822,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleNextSlide,\n                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"absolute right-28 top-1/2 transform -translate-y-1/2 translate-x-6 w-8 h-8 bg-white border border-gray-200 rounded-full flex items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronRight, {\n                                            className: \"w-3 h-3 text-gray-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 832,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 828,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 769,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 721,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                    lineNumber: 720,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: desktopMessagesContainerRef,\n                            style: {\n                                overscrollBehavior: \"contain\"\n                            },\n                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex-1 overflow-y-auto max-h-[calc(100vh-200px)] mt-14\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"w-full lg:w-[768px] mx-auto px-4 h-fit \",\n                                children: [\n                                    renderMessagePairs(),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: messagesEndRef,\n                                        className: \"jsx-69ac67aa0147cd65\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 846,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 844,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 839,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                zIndex: 1000,\n                                marginTop: \"20px\"\n                            },\n                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"fixed bottom-0 left-0 right-0 p-4 mt-5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSubmit,\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"relative w-full lg:w-[768px] mx-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            ref: messages.length > 0 ? inputRef : null,\n                                            value: message,\n                                            onChange: handleInputChange,\n                                            onKeyDown: handleKeyPress,\n                                            placeholder: \"Ask anything\",\n                                            rows: 1,\n                                            style: {\n                                                boxSizing: \"border-box\",\n                                                zIndex: 1001,\n                                                position: \"relative\"\n                                            },\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"pt-3 px-4 md:px-5 pb-10 pr-12 w-full min-h-[104px] max-h-[250px] overflow-y-auto text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base shadow-lg resize-none reset-height hide-scrollbar\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 858,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: message.trim().length === 0,\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + `absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation ${message.trim().length === 0 ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"}`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                                className: \"w-4 h-4 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 881,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 872,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 854,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-center mt-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-xs text-gray-500\",\n                                        children: [\n                                            \"This chat is powered by\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-black\",\n                                                children: \"Driply.me\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 887,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 885,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 884,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 850,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 718,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"lg:hidden overflow-hidden fixed inset-0 flex flex-col  mt-10 \",\n                children: [\n                    messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex flex-col items-center pt-15 pb-4 px-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"hidden md:flex lg:hidden flex-col items-center\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 900,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 899,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    paddingBottom: \"260px\"\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex flex-col items-center justify-center flex-1 px-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"40ms\" : \"0ms\"\n                                    },\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + `text-2xl md:text-4xl text-gray-900 text-center leading-relaxed transition-all duration-700 ${showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"}`,\n                                    children: \"How can I help you?\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 906,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 902,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: mobileMessagesContainerRef,\n                                style: {\n                                    overscrollBehavior: \"contain\"\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"md:hidden flex-1 overflow-y-auto max-h-[calc(100vh-190px)] hide-scrollbar\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"w-full max-w-[803px] mx-auto px-4  pt-12\",\n                                    children: [\n                                        renderMessagePairs(),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: messagesEndRef,\n                                            className: \"jsx-69ac67aa0147cd65\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 928,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 926,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 921,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: tabletMessagesContainerRef,\n                                style: {\n                                    overscrollBehavior: \"contain\"\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"hidden md:block lg:hidden flex-1 overflow-y-auto max-h-[calc(100vh-100px)]  pb-20 hide-scrollbar\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"w-full max-w-[803px] mx-auto px-4 pb-6 pt-12\",\n                                    children: [\n                                        renderMessagePairs(),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: messagesEndRef,\n                                            className: \"jsx-69ac67aa0147cd65\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 940,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 938,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 933,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    minHeight: \"160px\",\n                                    zIndex: 1000,\n                                    paddingBottom: \"env(safe-area-inset-bottom, 0)\",\n                                    transform: \"translateZ(0)\",\n                                    backfaceVisibility: \"hidden\"\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"fixed bottom-0 left-0 right-0 bg-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        transform: \"translateZ(0)\"\n                                    },\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"px-4 py-4 bg-white\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            style: {\n                                                zIndex: 1001\n                                            },\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"relative bg-white\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    transform: \"translateZ(0)\"\n                                                },\n                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"relative w-full max-w-[890px] mx-auto bg-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        ref: messages.length > 0 ? inputRef : null,\n                                                        value: message,\n                                                        onChange: handleInputChange,\n                                                        onKeyDown: handleKeyPress,\n                                                        placeholder: \"Ask anything\",\n                                                        rows: 1,\n                                                        style: {\n                                                            boxSizing: \"border-box\",\n                                                            maxHeight: \"250px\",\n                                                            zIndex: 1001,\n                                                            overscrollBehavior: \"none\",\n                                                            position: \"relative\"\n                                                        },\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"w-full min-h-[104px] pt-3 px-4 pb-10 pr-12 text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base resize-none overflow-y-auto reset-height\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 967,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        disabled: message.trim().length === 0,\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + `absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation ${message.trim().length === 0 ? \"bg-[#E0E0E0] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"}`,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                                            className: \"w-4 h-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 992,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 983,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 963,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 958,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-center py-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"This chat is powered by\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-black\",\n                                                        children: \"Driply.me\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 999,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 997,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 996,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 954,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 944,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"absolute bottom-0 left-0 right-0 bg-white\",\n                        children: messages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"120ms\" : \"0ms\",\n                                        position: \"relative\",\n                                        zIndex: 10,\n                                        backgroundColor: \"white\",\n                                        paddingBottom: \"10px\"\n                                    },\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + `${isMobile ? \"px-0\" : \"px-4\"} pt-2 pb-2 transition-all duration-500 ease-in-out ${isTyping ? \"opacity-0 pointer-events-none\" : showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"overflow-hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                ref: carouselRef,\n                                                style: {\n                                                    transform: `translateX(-${currentSlide * (isMobile ? 50 : 100)}%)`,\n                                                    paddingLeft: isMobile ? \"1rem\" : \"0\"\n                                                },\n                                                onTouchStart: handleTouchStart,\n                                                onTouchMove: handleTouchMove,\n                                                onTouchEnd: handleTouchEnd,\n                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex transition-transform duration-300 ease-in-out\",\n                                                children: isMobile ? (()=>{\n                                                    const circularCards = [\n                                                        ...settingData?.suggestedTopics,\n                                                        ...settingData?.suggestedTopics\n                                                    ];\n                                                    return circularCards.map((card, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex-shrink-0 mt-3 mr-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleSuggestionClick(card.question),\n                                                                style: {\n                                                                    width: \"fit-content\"\n                                                                },\n                                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"py-3 px-2.5 rounded-[8px] bg-[#F6F6F6] text-left hover:bg-[#D6EDFF] transition-all duration-200 inline-block touch-manipulation\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-[16px] font-[600] text-black mb-0.5 leading-tight whitespace-nowrap overflow-hidden text-ellipsis\",\n                                                                        children: card.question\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                        lineNumber: 1062,\n                                                                        columnNumber: 33\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-[16px] text-gray-500 leading-tight whitespace-nowrap overflow-hidden text-ellipsis\",\n                                                                        children: card.subtitle\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                        lineNumber: 1065,\n                                                                        columnNumber: 33\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                lineNumber: 1055,\n                                                                columnNumber: 31\n                                                            }, undefined)\n                                                        }, `${index}-${card.question}`, false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 1051,\n                                                            columnNumber: 29\n                                                        }, undefined));\n                                                })() : Array.from({\n                                                    length: Math.ceil(settingData?.suggestedTopics.length / 2)\n                                                }).map((_, slideIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"w-full flex-shrink-0 flex gap-2 justify-center\",\n                                                        children: settingData?.suggestedTopics.slice(slideIndex * 2, slideIndex * 2 + 2).map((card, cardIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleSuggestionClick(card.question),\n                                                                style: {\n                                                                    width: \"fit-content\"\n                                                                },\n                                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"py-2 px-2.5 rounded-[8px] bg-[#F6F6F6] text-left group hover:bg-[#D6EDFF] transition-all duration-200 inline-block touch-manipulation\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-[14px] font-[600] text-black mb-0.5 leading-tight whitespace-nowrap\",\n                                                                        children: card.question\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                        lineNumber: 1092,\n                                                                        columnNumber: 35\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-[16px] text-gray-500 leading-tight whitespace-nowrap\",\n                                                                        children: card.subtitle\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                        lineNumber: 1095,\n                                                                        columnNumber: 35\n                                                                    }, undefined)\n                                                                ]\n                                                            }, slideIndex * 2 + cardIndex, true, {\n                                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                lineNumber: 1084,\n                                                                columnNumber: 33\n                                                            }, undefined))\n                                                    }, slideIndex, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1077,\n                                                        columnNumber: 27\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1031,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1030,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handlePrevSlide,\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"hidden md:block absolute left-28 top-1/2 transform -translate-y-1/2 -translate-x-6 w-8 h-8 bg-white border border-gray-200 rounded-full items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronLeft, {\n                                                className: \"w-3 h-3 text-gray-600  ms-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1110,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1106,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleNextSlide,\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"hidden md:block absolute right-28 top-1/2 transform -translate-y-1/2 translate-x-6 w-8 h-8 bg-white border border-gray-200 rounded-full items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronRight, {\n                                                className: \"w-3 h-3 text-gray-600 ms-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1116,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1112,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1012,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"200ms\" : \"0ms\",\n                                        position: \"relative\",\n                                        zIndex: 5,\n                                        paddingBottom: \"env(safe-area-inset-bottom)\"\n                                    },\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + `px-4 bg-white transition-all duration-500 ${showInitialUI ? \"opacity-100 translate-y-0\" : \"translate-y-4\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"relative\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"relative w-full max-w-[890px] mx-auto\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        ref: messages.length === 0 ? inputRef : null,\n                                                        value: message,\n                                                        onChange: handleInputChange,\n                                                        onKeyDown: handleKeyPress,\n                                                        placeholder: \"Ask anything\",\n                                                        rows: 1,\n                                                        style: {\n                                                            boxSizing: \"border-box\",\n                                                            maxHeight: \"250px\",\n                                                            fontSize: \"16px\",\n                                                            zIndex: 1001,\n                                                            overscrollBehaviorY: \"contain\",\n                                                            position: \"relative\"\n                                                        },\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"w-full min-h-[104px] pt-3 px-4 pb-10 pr-12 text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base resize-none overflow-y-auto reset-height\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1134,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        disabled: message.trim().length === 0,\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + `absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation ${message.trim().length === 0 ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"}`,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                                            className: \"w-4 h-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 1160,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1151,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1133,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1132,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-center py-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"This chat is powered by\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-black\",\n                                                        children: \"Driply.me\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1167,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1165,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1164,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1121,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 1008,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 896,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"69ac67aa0147cd65\",\n                children: '.loading-dot{-webkit-animation:loading-dots 1.4s infinite ease-in-out both;-moz-animation:loading-dots 1.4s infinite ease-in-out both;-o-animation:loading-dots 1.4s infinite ease-in-out both;animation:loading-dots 1.4s infinite ease-in-out both}.loading-dot:nth-child(1){-webkit-animation-delay:-.32s;-moz-animation-delay:-.32s;-o-animation-delay:-.32s;animation-delay:-.32s}.loading-dot:nth-child(2){-webkit-animation-delay:-.16s;-moz-animation-delay:-.16s;-o-animation-delay:-.16s;animation-delay:-.16s}.loading-dot:nth-child(3){-webkit-animation-delay:0s;-moz-animation-delay:0s;-o-animation-delay:0s;animation-delay:0s}@-webkit-keyframes loading-dots{0%,80%,100%{-webkit-transform:scale(1);transform:scale(1);opacity:1}40%{-webkit-transform:scale(1.5);transform:scale(1.5);opacity:.7}}@-moz-keyframes loading-dots{0%,80%,100%{-moz-transform:scale(1);transform:scale(1);opacity:1}40%{-moz-transform:scale(1.5);transform:scale(1.5);opacity:.7}}@-o-keyframes loading-dots{0%,80%,100%{-o-transform:scale(1);transform:scale(1);opacity:1}40%{-o-transform:scale(1.5);transform:scale(1.5);opacity:.7}}@keyframes loading-dots{0%,80%,100%{-webkit-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1);opacity:1}40%{-webkit-transform:scale(1.5);-moz-transform:scale(1.5);-o-transform:scale(1.5);transform:scale(1.5);opacity:.7}}.hide-scrollbar{-ms-overflow-style:none!important;scrollbar-width:none!important}.hide-scrollbar::-webkit-scrollbar{display:none!important}@media only screen and (max-width:1023px){.lg\\\\\\\\.jsx-69ac67aa0147cd65:hidden .absolute.bottom-0.jsx-69ac67aa0147cd65{position:fixed!important;bottom:env(safe-area-inset-bottom,0);left:0;right:0;width:100%;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;z-index:1000;background-color:white}textarea.jsx-69ac67aa0147cd65{-webkit-user-select:auto!important;-moz-user-select:auto!important;-ms-user-select:auto!important;user-select:auto!important;-webkit-appearance:none;-moz-appearance:none;-ms-appearance:none;appearance:none;-ms-scroll-chaining:none;overscroll-behavior:none;line-height:24px;max-height:250px!important;position:relative!important;-webkit-transform:translatez(0);-moz-transform:translatez(0);-ms-transform:translatez(0);-o-transform:translatez(0);transform:translatez(0);-webkit-backface-visibility:hidden;-moz-backface-visibility:hidden;backface-visibility:hidden;-webkit-perspective:1000;-moz-perspective:1000;perspective:1000;z-index:1001!important;background-color:white}.lg\\\\\\\\.jsx-69ac67aa0147cd65:hidden form.jsx-69ac67aa0147cd65{position:relative!important;z-index:1001;background-color:white}button[type=\"submit\"].jsx-69ac67aa0147cd65{position:absolute!important;right:12px;bottom:12px;z-index:1002!important;-webkit-tap-highlight-color:transparent;-ms-touch-action:-ms-manipulation;-ms-touch-action:manipulation;touch-action:manipulation;-webkit-transform:translatez(0);-moz-transform:translatez(0);-ms-transform:translatez(0);-o-transform:translatez(0);transform:translatez(0)}.lg\\\\\\\\.jsx-69ac67aa0147cd65:hidden .bg-white.jsx-69ac67aa0147cd65{background-color:white!important}}@media only screen and (min-width:1024px){textarea.jsx-69ac67aa0147cd65{max-height:250px!important;overflow-y:auto!important;-ms-scroll-chaining:none;overscroll-behavior:contain}.reset-height.jsx-69ac67aa0147cd65{min-height:104px!important;max-height:250px!important;overflow-y:auto!important}}.fixed-bottom.jsx-69ac67aa0147cd65{position:fixed!important;bottom:0;left:0;right:0;background:white}'\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n        lineNumber: 716,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatInterface);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ChatInterface.jsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ChatInterfaceWrapper.jsx":
/*!*************************************************!*\
  !*** ./src/components/ChatInterfaceWrapper.jsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatInterfaceWrapper)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ChatInterface__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ChatInterface */ \"(ssr)/./src/components/ChatInterface.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n/**\r\n * Client-side wrapper for ChatInterface component\r\n * This separates client components from server components to fix metadata issues\r\n * Following Next.js App Router best practices\r\n */ function ChatInterfaceWrapper({ slug, query }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatInterface__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        slug: slug,\n        query: query\n    }, void 0, false, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterfaceWrapper.jsx\",\n        lineNumber: 12,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9DaGF0SW50ZXJmYWNlV3JhcHBlci5qc3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUUwQjtBQUNrQjtBQUU1Qzs7OztDQUlDLEdBQ2MsU0FBU0UscUJBQXFCLEVBQUVDLElBQUksRUFBRUMsS0FBSyxFQUFFO0lBQzFELHFCQUFPLDhEQUFDSCxzREFBYUE7UUFBQ0UsTUFBTUE7UUFBTUMsT0FBT0E7Ozs7OztBQUMzQyIsInNvdXJjZXMiOlsiRDpcXExhcHRvcCBkYXRhXFxEUklQTFktQ0hBVFxcY2hhdC1uZXh0XFxzcmNcXGNvbXBvbmVudHNcXENoYXRJbnRlcmZhY2VXcmFwcGVyLmpzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcclxuXHJcbmltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCBDaGF0SW50ZXJmYWNlIGZyb20gJy4vQ2hhdEludGVyZmFjZSc7XHJcblxyXG4vKipcclxuICogQ2xpZW50LXNpZGUgd3JhcHBlciBmb3IgQ2hhdEludGVyZmFjZSBjb21wb25lbnRcclxuICogVGhpcyBzZXBhcmF0ZXMgY2xpZW50IGNvbXBvbmVudHMgZnJvbSBzZXJ2ZXIgY29tcG9uZW50cyB0byBmaXggbWV0YWRhdGEgaXNzdWVzXHJcbiAqIEZvbGxvd2luZyBOZXh0LmpzIEFwcCBSb3V0ZXIgYmVzdCBwcmFjdGljZXNcclxuICovXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIENoYXRJbnRlcmZhY2VXcmFwcGVyKHsgc2x1ZywgcXVlcnkgfSkge1xyXG4gIHJldHVybiA8Q2hhdEludGVyZmFjZSBzbHVnPXtzbHVnfSBxdWVyeT17cXVlcnl9IC8+O1xyXG59XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkNoYXRJbnRlcmZhY2UiLCJDaGF0SW50ZXJmYWNlV3JhcHBlciIsInNsdWciLCJxdWVyeSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ChatInterfaceWrapper.jsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ClientWrapper.jsx":
/*!******************************************!*\
  !*** ./src/components/ClientWrapper.jsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClientWrapper)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Navbar */ \"(ssr)/./src/components/Navbar.jsx\");\n/* harmony import */ var _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/ChatContext */ \"(ssr)/./src/contexts/ChatContext.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n/**\r\n * Client wrapper component that contains all client-side logic\r\n * This separates client components from server components to fix metadata issues\r\n * Following Next.js App Router best practices\r\n */ function ClientWrapper({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_ChatContext__WEBPACK_IMPORTED_MODULE_3__.ChatProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ClientWrapper.jsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ClientWrapper.jsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9DbGllbnRXcmFwcGVyLmpzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUUwQjtBQUNJO0FBQ3lCO0FBRXZEOzs7O0NBSUMsR0FDYyxTQUFTRyxjQUFjLEVBQUVDLFFBQVEsRUFBRTtJQUNoRCxxQkFDRSw4REFBQ0YsK0RBQVlBOzswQkFDWCw4REFBQ0QsK0NBQU1BOzs7OztZQUNORzs7Ozs7OztBQUdQIiwic291cmNlcyI6WyJEOlxcTGFwdG9wIGRhdGFcXERSSVBMWS1DSEFUXFxjaGF0LW5leHRcXHNyY1xcY29tcG9uZW50c1xcQ2xpZW50V3JhcHBlci5qc3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XHJcblxyXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgTmF2YmFyIGZyb20gXCIuL05hdmJhclwiO1xyXG5pbXBvcnQgeyBDaGF0UHJvdmlkZXIgfSBmcm9tIFwiLi4vY29udGV4dHMvQ2hhdENvbnRleHRcIjtcclxuXHJcbi8qKlxyXG4gKiBDbGllbnQgd3JhcHBlciBjb21wb25lbnQgdGhhdCBjb250YWlucyBhbGwgY2xpZW50LXNpZGUgbG9naWNcclxuICogVGhpcyBzZXBhcmF0ZXMgY2xpZW50IGNvbXBvbmVudHMgZnJvbSBzZXJ2ZXIgY29tcG9uZW50cyB0byBmaXggbWV0YWRhdGEgaXNzdWVzXHJcbiAqIEZvbGxvd2luZyBOZXh0LmpzIEFwcCBSb3V0ZXIgYmVzdCBwcmFjdGljZXNcclxuICovXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIENsaWVudFdyYXBwZXIoeyBjaGlsZHJlbiB9KSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxDaGF0UHJvdmlkZXI+XHJcbiAgICAgIDxOYXZiYXIgLz5cclxuICAgICAge2NoaWxkcmVufVxyXG4gICAgPC9DaGF0UHJvdmlkZXI+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJOYXZiYXIiLCJDaGF0UHJvdmlkZXIiLCJDbGllbnRXcmFwcGVyIiwiY2hpbGRyZW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ClientWrapper.jsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Navbar.jsx":
/*!***********************************!*\
  !*** ./src/components/Navbar.jsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_FiEdit_react_icons_fi__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FiEdit!=!react-icons/fi */ \"(ssr)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _assets_images_logo_png__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../assets/images/logo.png */ \"(ssr)/./src/assets/images/logo.png\");\n/* harmony import */ var _barrel_optimize_names_HiUserCircle_react_icons_hi2__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=HiUserCircle!=!react-icons/hi2 */ \"(ssr)/./node_modules/react-icons/hi2/index.mjs\");\n/* harmony import */ var _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../contexts/ChatContext */ \"(ssr)/./src/contexts/ChatContext.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nconst Navbar = ()=>{\n    const [businessName, setBusinessName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Get context values\n    const { metadata, hasMetadata } = (0,_contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__.useChatContext)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            // First, try to get business name from context\n            if (hasMetadata() && metadata.businessName) {\n                setBusinessName(metadata.businessName);\n                return;\n            }\n            // Fallback to localStorage and event listeners\n            const checkBusinessName = {\n                \"Navbar.useEffect.checkBusinessName\": ()=>{\n                    const storedName = localStorage.getItem(\"BusinessName\") || \"Driply\";\n                    if (storedName && storedName !== \"undefined\" && storedName !== \"null\") {\n                        setBusinessName(storedName);\n                        return true;\n                    }\n                    return false;\n                }\n            }[\"Navbar.useEffect.checkBusinessName\"];\n            if (checkBusinessName()) {\n                return;\n            }\n            const handleBusinessNameLoaded = {\n                \"Navbar.useEffect.handleBusinessNameLoaded\": (event)=>{\n                    if (event.detail && event.detail.businessName) {\n                        setBusinessName(event.detail.businessName);\n                    }\n                }\n            }[\"Navbar.useEffect.handleBusinessNameLoaded\"];\n            window.addEventListener(\"businessNameLoaded\", handleBusinessNameLoaded);\n            const interval = setInterval({\n                \"Navbar.useEffect.interval\": ()=>{\n                    if (checkBusinessName()) {\n                        clearInterval(interval);\n                    }\n                }\n            }[\"Navbar.useEffect.interval\"], 100);\n            const timeout = setTimeout({\n                \"Navbar.useEffect.timeout\": ()=>{\n                    clearInterval(interval);\n                }\n            }[\"Navbar.useEffect.timeout\"], 5000);\n            return ({\n                \"Navbar.useEffect\": ()=>{\n                    window.removeEventListener(\"businessNameLoaded\", handleBusinessNameLoaded);\n                    clearInterval(interval);\n                    clearTimeout(timeout);\n                }\n            })[\"Navbar.useEffect\"];\n        }\n    }[\"Navbar.useEffect\"], [\n        metadata,\n        hasMetadata\n    ]);\n    const handleNewConversation = ()=>{\n        localStorage.removeItem(\"userID\");\n        localStorage.removeItem(\"customerName_userId\");\n        localStorage.removeItem(\"BusinessName\");\n        localStorage.removeItem(\"userId\");\n        window.location.reload();\n    };\n    const handleHomeClick = (e)=>{\n        e.preventDefault();\n        window.location.reload();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-white fixed top-0 left-0 right-0 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-between items-center h-15 px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 \",\n                    children: [\n                        metadata.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                src: metadata.image,\n                                alt: \"user-image\",\n                                className: \"h-10 w-10 hover:border hover:border-gray-400 rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\Navbar.jsx\",\n                                lineNumber: 85,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiUserCircle_react_icons_hi2__WEBPACK_IMPORTED_MODULE_6__.HiUserCircle, {\n                            className: \"text-black text-xl w-10 h-10 rounded-full object-cover hover:border hover:border-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\Navbar.jsx\",\n                            lineNumber: 92,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            onClick: handleHomeClick,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-[17px] font-[500] text-black \",\n                                children: businessName\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\Navbar.jsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\Navbar.jsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\Navbar.jsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleNewConversation,\n                    className: \"px-4 py-2 rounded-lg cursor-pointer\",\n                    title: \"Start New Conversation\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit_react_icons_fi__WEBPACK_IMPORTED_MODULE_7__.FiEdit, {\n                        className: \"text-black text-xl\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\Navbar.jsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\Navbar.jsx\",\n                    lineNumber: 100,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\Navbar.jsx\",\n            lineNumber: 81,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\Navbar.jsx\",\n        lineNumber: 80,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Navbar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9OYXZiYXIuanN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFbUQ7QUFDdEI7QUFDRTtBQUNTO0FBQ0s7QUFDRTtBQUNVO0FBRXpELE1BQU1TLFNBQVM7SUFDYixNQUFNLENBQUNDLGNBQWNDLGdCQUFnQixHQUFHVCwrQ0FBUUEsQ0FBQztJQUVqRCxxQkFBcUI7SUFDckIsTUFBTSxFQUFFVSxRQUFRLEVBQUVDLFdBQVcsRUFBRSxHQUFHTCxxRUFBY0E7SUFFaERQLGdEQUFTQTs0QkFBQztZQUNSLCtDQUErQztZQUMvQyxJQUFJWSxpQkFBaUJELFNBQVNGLFlBQVksRUFBRTtnQkFDMUNDLGdCQUFnQkMsU0FBU0YsWUFBWTtnQkFDckM7WUFDRjtZQUVBLCtDQUErQztZQUMvQyxNQUFNSTtzREFBb0I7b0JBQ3hCLE1BQU1DLGFBQWFDLGFBQWFDLE9BQU8sQ0FBQyxtQkFBbUI7b0JBQzNELElBQUlGLGNBQWNBLGVBQWUsZUFBZUEsZUFBZSxRQUFRO3dCQUNyRUosZ0JBQWdCSTt3QkFDaEIsT0FBTztvQkFDVDtvQkFDQSxPQUFPO2dCQUNUOztZQUVBLElBQUlELHFCQUFxQjtnQkFDdkI7WUFDRjtZQUVBLE1BQU1JOzZEQUEyQixDQUFDQztvQkFDaEMsSUFBSUEsTUFBTUMsTUFBTSxJQUFJRCxNQUFNQyxNQUFNLENBQUNWLFlBQVksRUFBRTt3QkFDN0NDLGdCQUFnQlEsTUFBTUMsTUFBTSxDQUFDVixZQUFZO29CQUMzQztnQkFDRjs7WUFFQVcsT0FBT0MsZ0JBQWdCLENBQUMsc0JBQXNCSjtZQUU5QyxNQUFNSyxXQUFXQzs2Q0FBWTtvQkFDM0IsSUFBSVYscUJBQXFCO3dCQUN2QlcsY0FBY0Y7b0JBQ2hCO2dCQUNGOzRDQUFHO1lBRUgsTUFBTUcsVUFBVUM7NENBQVc7b0JBQ3pCRixjQUFjRjtnQkFDaEI7MkNBQUc7WUFFSDtvQ0FBTztvQkFDTEYsT0FBT08sbUJBQW1CLENBQ3hCLHNCQUNBVjtvQkFFRk8sY0FBY0Y7b0JBQ2RNLGFBQWFIO2dCQUNmOztRQUNGOzJCQUFHO1FBQUNkO1FBQVVDO0tBQVk7SUFFMUIsTUFBTWlCLHdCQUF3QjtRQUM1QmQsYUFBYWUsVUFBVSxDQUFDO1FBQ3hCZixhQUFhZSxVQUFVLENBQUM7UUFDeEJmLGFBQWFlLFVBQVUsQ0FBQztRQUN4QmYsYUFBYWUsVUFBVSxDQUFDO1FBQ3hCVixPQUFPVyxRQUFRLENBQUNDLE1BQU07SUFDeEI7SUFFQSxNQUFNQyxrQkFBa0IsQ0FBQ0M7UUFDdkJBLEVBQUVDLGNBQWM7UUFDaEJmLE9BQU9XLFFBQVEsQ0FBQ0MsTUFBTTtJQUN4QjtJQUVBLHFCQUNFLDhEQUFDSTtRQUFJQyxXQUFVO2tCQUNiLDRFQUFDQztZQUFJRCxXQUFVOzs4QkFDYiw4REFBQ0M7b0JBQUlELFdBQVU7O3dCQUNaMUIsU0FBUzRCLEtBQUssaUJBQ2I7c0NBQ0UsNEVBQUNwQyxrREFBS0E7Z0NBQ0pxQyxLQUFLN0IsU0FBUzRCLEtBQUs7Z0NBQ25CRSxLQUFJO2dDQUNKSixXQUFVOzs7Ozs7MERBSWQsOERBQUMvQiw2RkFBWUE7NEJBQUMrQixXQUFVOzs7Ozs7c0NBRTFCLDhEQUFDbkMsa0RBQUlBOzRCQUFDd0MsTUFBSzs0QkFBSUMsU0FBU1Y7c0NBQ3RCLDRFQUFDVztnQ0FBS1AsV0FBVTswQ0FDYjVCOzs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFJUCw4REFBQ29DO29CQUNDRixTQUFTZDtvQkFDVFEsV0FBVTtvQkFDVlMsT0FBTTs4QkFFTiw0RUFBQzFDLGdGQUFNQTt3QkFBQ2lDLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLNUI7QUFFQSxpRUFBZTdCLE1BQU1BLEVBQUMiLCJzb3VyY2VzIjpbIkQ6XFxMYXB0b3AgZGF0YVxcRFJJUExZLUNIQVRcXGNoYXQtbmV4dFxcc3JjXFxjb21wb25lbnRzXFxOYXZiYXIuanN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xyXG5cclxuaW1wb3J0IFJlYWN0LCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tIFwicmVhY3RcIjtcclxuaW1wb3J0IExpbmsgZnJvbSBcIm5leHQvbGlua1wiO1xyXG5pbXBvcnQgSW1hZ2UgZnJvbSBcIm5leHQvaW1hZ2VcIjtcclxuaW1wb3J0IHsgRmlFZGl0IH0gZnJvbSBcInJlYWN0LWljb25zL2ZpXCI7XHJcbmltcG9ydCBsb2dvIGZyb20gXCIuLi9hc3NldHMvaW1hZ2VzL2xvZ28ucG5nXCI7XHJcbmltcG9ydCB7IEhpVXNlckNpcmNsZSB9IGZyb20gXCJyZWFjdC1pY29ucy9oaTJcIjtcclxuaW1wb3J0IHsgdXNlQ2hhdENvbnRleHQgfSBmcm9tIFwiLi4vY29udGV4dHMvQ2hhdENvbnRleHRcIjtcclxuXHJcbmNvbnN0IE5hdmJhciA9ICgpID0+IHtcclxuICBjb25zdCBbYnVzaW5lc3NOYW1lLCBzZXRCdXNpbmVzc05hbWVdID0gdXNlU3RhdGUoXCJcIik7XHJcblxyXG4gIC8vIEdldCBjb250ZXh0IHZhbHVlc1xyXG4gIGNvbnN0IHsgbWV0YWRhdGEsIGhhc01ldGFkYXRhIH0gPSB1c2VDaGF0Q29udGV4dCgpO1xyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgLy8gRmlyc3QsIHRyeSB0byBnZXQgYnVzaW5lc3MgbmFtZSBmcm9tIGNvbnRleHRcclxuICAgIGlmIChoYXNNZXRhZGF0YSgpICYmIG1ldGFkYXRhLmJ1c2luZXNzTmFtZSkge1xyXG4gICAgICBzZXRCdXNpbmVzc05hbWUobWV0YWRhdGEuYnVzaW5lc3NOYW1lKTtcclxuICAgICAgcmV0dXJuO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIEZhbGxiYWNrIHRvIGxvY2FsU3RvcmFnZSBhbmQgZXZlbnQgbGlzdGVuZXJzXHJcbiAgICBjb25zdCBjaGVja0J1c2luZXNzTmFtZSA9ICgpID0+IHtcclxuICAgICAgY29uc3Qgc3RvcmVkTmFtZSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKFwiQnVzaW5lc3NOYW1lXCIpIHx8IFwiRHJpcGx5XCI7XHJcbiAgICAgIGlmIChzdG9yZWROYW1lICYmIHN0b3JlZE5hbWUgIT09IFwidW5kZWZpbmVkXCIgJiYgc3RvcmVkTmFtZSAhPT0gXCJudWxsXCIpIHtcclxuICAgICAgICBzZXRCdXNpbmVzc05hbWUoc3RvcmVkTmFtZSk7XHJcbiAgICAgICAgcmV0dXJuIHRydWU7XHJcbiAgICAgIH1cclxuICAgICAgcmV0dXJuIGZhbHNlO1xyXG4gICAgfTtcclxuXHJcbiAgICBpZiAoY2hlY2tCdXNpbmVzc05hbWUoKSkge1xyXG4gICAgICByZXR1cm47XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgaGFuZGxlQnVzaW5lc3NOYW1lTG9hZGVkID0gKGV2ZW50KSA9PiB7XHJcbiAgICAgIGlmIChldmVudC5kZXRhaWwgJiYgZXZlbnQuZGV0YWlsLmJ1c2luZXNzTmFtZSkge1xyXG4gICAgICAgIHNldEJ1c2luZXNzTmFtZShldmVudC5kZXRhaWwuYnVzaW5lc3NOYW1lKTtcclxuICAgICAgfVxyXG4gICAgfTtcclxuXHJcbiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcihcImJ1c2luZXNzTmFtZUxvYWRlZFwiLCBoYW5kbGVCdXNpbmVzc05hbWVMb2FkZWQpO1xyXG5cclxuICAgIGNvbnN0IGludGVydmFsID0gc2V0SW50ZXJ2YWwoKCkgPT4ge1xyXG4gICAgICBpZiAoY2hlY2tCdXNpbmVzc05hbWUoKSkge1xyXG4gICAgICAgIGNsZWFySW50ZXJ2YWwoaW50ZXJ2YWwpO1xyXG4gICAgICB9XHJcbiAgICB9LCAxMDApO1xyXG5cclxuICAgIGNvbnN0IHRpbWVvdXQgPSBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgY2xlYXJJbnRlcnZhbChpbnRlcnZhbCk7XHJcbiAgICB9LCA1MDAwKTtcclxuXHJcbiAgICByZXR1cm4gKCkgPT4ge1xyXG4gICAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcihcclxuICAgICAgICBcImJ1c2luZXNzTmFtZUxvYWRlZFwiLFxyXG4gICAgICAgIGhhbmRsZUJ1c2luZXNzTmFtZUxvYWRlZFxyXG4gICAgICApO1xyXG4gICAgICBjbGVhckludGVydmFsKGludGVydmFsKTtcclxuICAgICAgY2xlYXJUaW1lb3V0KHRpbWVvdXQpO1xyXG4gICAgfTtcclxuICB9LCBbbWV0YWRhdGEsIGhhc01ldGFkYXRhXSk7XHJcblxyXG4gIGNvbnN0IGhhbmRsZU5ld0NvbnZlcnNhdGlvbiA9ICgpID0+IHtcclxuICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKFwidXNlcklEXCIpO1xyXG4gICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oXCJjdXN0b21lck5hbWVfdXNlcklkXCIpO1xyXG4gICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oXCJCdXNpbmVzc05hbWVcIik7XHJcbiAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbShcInVzZXJJZFwiKTtcclxuICAgIHdpbmRvdy5sb2NhdGlvbi5yZWxvYWQoKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVIb21lQ2xpY2sgPSAoZSkgPT4ge1xyXG4gICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xyXG4gICAgd2luZG93LmxvY2F0aW9uLnJlbG9hZCgpO1xyXG4gIH07XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8bmF2IGNsYXNzTmFtZT1cImJnLXdoaXRlIGZpeGVkIHRvcC0wIGxlZnQtMCByaWdodC0wIHotNTBcIj5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgaC0xNSBweC00XCI+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBcIj5cclxuICAgICAgICAgIHttZXRhZGF0YS5pbWFnZSA/IChcclxuICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICA8SW1hZ2VcclxuICAgICAgICAgICAgICAgIHNyYz17bWV0YWRhdGEuaW1hZ2V9XHJcbiAgICAgICAgICAgICAgICBhbHQ9XCJ1c2VyLWltYWdlXCJcclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtMTAgdy0xMCBob3Zlcjpib3JkZXIgaG92ZXI6Ym9yZGVyLWdyYXktNDAwIHJvdW5kZWQtZnVsbFwiXHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPC8+XHJcbiAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICA8SGlVc2VyQ2lyY2xlIGNsYXNzTmFtZT1cInRleHQtYmxhY2sgdGV4dC14bCB3LTEwIGgtMTAgcm91bmRlZC1mdWxsIG9iamVjdC1jb3ZlciBob3Zlcjpib3JkZXIgaG92ZXI6Ym9yZGVyLWdyYXktNDAwXCIgLz5cclxuICAgICAgICAgICl9XHJcbiAgICAgICAgICA8TGluayBocmVmPVwiL1wiIG9uQ2xpY2s9e2hhbmRsZUhvbWVDbGlja30+XHJcbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtWzE3cHhdIGZvbnQtWzUwMF0gdGV4dC1ibGFjayBcIj5cclxuICAgICAgICAgICAgICB7YnVzaW5lc3NOYW1lfVxyXG4gICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgb25DbGljaz17aGFuZGxlTmV3Q29udmVyc2F0aW9ufVxyXG4gICAgICAgICAgY2xhc3NOYW1lPVwicHgtNCBweS0yIHJvdW5kZWQtbGcgY3Vyc29yLXBvaW50ZXJcIlxyXG4gICAgICAgICAgdGl0bGU9XCJTdGFydCBOZXcgQ29udmVyc2F0aW9uXCJcclxuICAgICAgICA+XHJcbiAgICAgICAgICA8RmlFZGl0IGNsYXNzTmFtZT1cInRleHQtYmxhY2sgdGV4dC14bFwiIC8+XHJcbiAgICAgICAgPC9idXR0b24+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgPC9uYXY+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IE5hdmJhcjtcclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJMaW5rIiwiSW1hZ2UiLCJGaUVkaXQiLCJsb2dvIiwiSGlVc2VyQ2lyY2xlIiwidXNlQ2hhdENvbnRleHQiLCJOYXZiYXIiLCJidXNpbmVzc05hbWUiLCJzZXRCdXNpbmVzc05hbWUiLCJtZXRhZGF0YSIsImhhc01ldGFkYXRhIiwiY2hlY2tCdXNpbmVzc05hbWUiLCJzdG9yZWROYW1lIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsImhhbmRsZUJ1c2luZXNzTmFtZUxvYWRlZCIsImV2ZW50IiwiZGV0YWlsIiwid2luZG93IiwiYWRkRXZlbnRMaXN0ZW5lciIsImludGVydmFsIiwic2V0SW50ZXJ2YWwiLCJjbGVhckludGVydmFsIiwidGltZW91dCIsInNldFRpbWVvdXQiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwiY2xlYXJUaW1lb3V0IiwiaGFuZGxlTmV3Q29udmVyc2F0aW9uIiwicmVtb3ZlSXRlbSIsImxvY2F0aW9uIiwicmVsb2FkIiwiaGFuZGxlSG9tZUNsaWNrIiwiZSIsInByZXZlbnREZWZhdWx0IiwibmF2IiwiY2xhc3NOYW1lIiwiZGl2IiwiaW1hZ2UiLCJzcmMiLCJhbHQiLCJocmVmIiwib25DbGljayIsInNwYW4iLCJidXR0b24iLCJ0aXRsZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Navbar.jsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/ChatContext.jsx":
/*!**************************************!*\
  !*** ./src/contexts/ChatContext.jsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatProvider: () => (/* binding */ ChatProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useChatContext: () => (/* binding */ useChatContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useChatContext,ChatProvider,default auto */ \n\n// Create the context\nconst ChatContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\n// Custom hook to use the chat context\nconst useChatContext = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ChatContext);\n    if (!context) {\n        throw new Error('useChatContext must be used within a ChatProvider');\n    }\n    return context;\n};\n// Chat Provider component\nconst ChatProvider = ({ children })=>{\n    console.log(\"ChatProvider\", children);\n    const [metadata, setMetadata] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: null,\n        keywords: null,\n        description: null,\n        image: null\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [settingData, setSettingData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        customerName: null,\n        businessName: null,\n        chatDesignSettings: null,\n        details: null,\n        firstSentence: null,\n        isActive: null,\n        metaData: null,\n        suggestedTopics: null\n    });\n    // Function that update setting response data\n    const updateSettingData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[updateSettingData]\": (newSettingData)=>{\n            setSettingData({\n                \"ChatProvider.useCallback[updateSettingData]\": (prevSettingData)=>({\n                        ...prevSettingData,\n                        ...newSettingData\n                    })\n            }[\"ChatProvider.useCallback[updateSettingData]\"]);\n        }\n    }[\"ChatProvider.useCallback[updateSettingData]\"], []);\n    // Function to update metadata\n    const updateMetadata = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[updateMetadata]\": (newMetadata)=>{\n            setMetadata({\n                \"ChatProvider.useCallback[updateMetadata]\": (prevMetadata)=>({\n                        ...prevMetadata,\n                        ...newMetadata\n                    })\n            }[\"ChatProvider.useCallback[updateMetadata]\"]);\n        }\n    }[\"ChatProvider.useCallback[updateMetadata]\"], []);\n    // Function to clear metadata\n    const clearMetadata = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[clearMetadata]\": ()=>{\n            setMetadata({\n                title: null,\n                keywords: null,\n                description: null,\n                image: null\n            });\n        }\n    }[\"ChatProvider.useCallback[clearMetadata]\"], []);\n    // Function to set loading state\n    const setLoadingState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[setLoadingState]\": (isLoading)=>{\n            setLoading(isLoading);\n        }\n    }[\"ChatProvider.useCallback[setLoadingState]\"], []);\n    // Function to set error state\n    const setErrorState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[setErrorState]\": (errorMessage)=>{\n            setError(errorMessage);\n        }\n    }[\"ChatProvider.useCallback[setErrorState]\"], []);\n    // Function to clear error\n    const clearError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[clearError]\": ()=>{\n            setError(null);\n        }\n    }[\"ChatProvider.useCallback[clearError]\"], []);\n    const value = {\n        metadata,\n        loading,\n        error,\n        updateMetadata,\n        clearMetadata,\n        setLoadingState,\n        setErrorState,\n        clearError,\n        settingData,\n        updateSettingData,\n        // Helper getters for easy access\n        getTitle: ()=>metadata.title,\n        getKeywords: ()=>metadata.keywords,\n        getDescription: ()=>metadata.description,\n        getImage: ()=>metadata.image,\n        hasMetadata: ()=>metadata.title !== null || metadata.keywords !== null || metadata.description !== null\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\contexts\\\\ChatContext.jsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatContext);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/ChatContext.jsx\n");

/***/ }),

/***/ "(ssr)/./src/utils/config.js":
/*!*****************************!*\
  !*** ./src/utils/config.js ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_ENDPOINTS: () => (/* binding */ API_ENDPOINTS),\n/* harmony export */   EXTERNAL_API_BASE_URL: () => (/* binding */ EXTERNAL_API_BASE_URL),\n/* harmony export */   EXTERNAL_API_ENDPOINTS: () => (/* binding */ EXTERNAL_API_ENDPOINTS),\n/* harmony export */   getExternalApiUrl: () => (/* binding */ getExternalApiUrl),\n/* harmony export */   getSSEUrl: () => (/* binding */ getSSEUrl)\n/* harmony export */ });\nconst EXTERNAL_API_BASE_URL = \"https://api-develop.driply.me/\" || 0;\nconst getExternalApiUrl = ()=>{\n    const url = EXTERNAL_API_BASE_URL;\n    return url.endsWith('/') ? url : `${url}/`;\n};\nconst API_ENDPOINTS = {\n    CHAT_INIT: '/api/chat/init',\n    CHAT_SEND: '/api/chat/send',\n    CHAT_MESSAGES: '/api/chat/messages',\n    CHAT_SETTINGS: '/api/chat/settings'\n};\nconst getSSEUrl = (userId)=>{\n    return `${getExternalApiUrl()}chat-pusher/chat?stream=${userId}`;\n};\nconst EXTERNAL_API_ENDPOINTS = {\n    CHAT_INIT: `${getExternalApiUrl()}api/chat/init`,\n    CHAT_SEND: `${getExternalApiUrl()}api/chat/message`,\n    CHAT_MESSAGES: `${getExternalApiUrl()}api/chat/messages`,\n    CHAT_SETTINGS: `${getExternalApiUrl()}api/chat/settings`\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/config.js\n");

/***/ }),

/***/ "(ssr)/./src/utils/constants.js":
/*!********************************!*\
  !*** ./src/utils/constants.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APP_NAME: () => (/* binding */ APP_NAME),\n/* harmony export */   SUGGESTION_CARDS: () => (/* binding */ SUGGESTION_CARDS)\n/* harmony export */ });\nconst APP_NAME = 'DRIPLY';\nconst SUGGESTION_CARDS = [\n    {\n        title: \"Design a schema\"\n    },\n    {\n        title: \"Explain airplane\"\n    },\n    {\n        title: \"Create a work plan\"\n    },\n    {\n        title: \"Write a recipe\"\n    },\n    {\n        title: \"Plan a budget\"\n    },\n    {\n        title: \"Learn JavaScript \"\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvdXRpbHMvY29uc3RhbnRzLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQ08sTUFBTUEsV0FBVyxTQUFRO0FBR3pCLE1BQU1DLG1CQUFtQjtJQUM5QjtRQUNFQyxPQUFPO0lBQ1Q7SUFDQTtRQUNFQSxPQUFPO0lBQ1Q7SUFDQTtRQUNFQSxPQUFPO0lBQ1Q7SUFDQTtRQUNFQSxPQUFPO0lBQ1Q7SUFDQTtRQUNFQSxPQUFPO0lBQ1Q7SUFDQTtRQUNFQSxPQUFPO0lBQ1Q7Q0FDRCIsInNvdXJjZXMiOlsiRDpcXExhcHRvcCBkYXRhXFxEUklQTFktQ0hBVFxcY2hhdC1uZXh0XFxzcmNcXHV0aWxzXFxjb25zdGFudHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXHJcbmV4cG9ydCBjb25zdCBBUFBfTkFNRSA9ICdEUklQTFknXHJcblxyXG5cclxuZXhwb3J0IGNvbnN0IFNVR0dFU1RJT05fQ0FSRFMgPSBbXHJcbiAge1xyXG4gICAgdGl0bGU6IFwiRGVzaWduIGEgc2NoZW1hXCIsXHJcbiAgfSxcclxuICB7XHJcbiAgICB0aXRsZTogXCJFeHBsYWluIGFpcnBsYW5lXCIsXHJcbiAgfSxcclxuICB7XHJcbiAgICB0aXRsZTogXCJDcmVhdGUgYSB3b3JrIHBsYW5cIixcclxuICB9LFxyXG4gIHtcclxuICAgIHRpdGxlOiBcIldyaXRlIGEgcmVjaXBlXCIsXHJcbiAgfSxcclxuICB7XHJcbiAgICB0aXRsZTogXCJQbGFuIGEgYnVkZ2V0XCIsXHJcbiAgfSxcclxuICB7XHJcbiAgICB0aXRsZTogXCJMZWFybiBKYXZhU2NyaXB0IFwiLFxyXG4gIH1cclxuXVxyXG5cclxuXHJcbiJdLCJuYW1lcyI6WyJBUFBfTkFNRSIsIlNVR0dFU1RJT05fQ0FSRFMiLCJ0aXRsZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/constants.js\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "node:os":
/*!**************************!*\
  !*** external "node:os" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:os");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:tty":
/*!***************************!*\
  !*** external "node:tty" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:tty");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/axios","vendor-chunks/asynckit","vendor-chunks/math-intrinsics","vendor-chunks/react-icons","vendor-chunks/es-errors","vendor-chunks/form-data","vendor-chunks/call-bind-apply-helpers","vendor-chunks/debug","vendor-chunks/get-proto","vendor-chunks/styled-jsx","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/follow-redirects","vendor-chunks/supports-color","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/get-intrinsic","vendor-chunks/es-set-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/delayed-stream","vendor-chunks/combined-stream"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Bslug%5D%2Fpage&page=%2F%5Bslug%5D%2Fpage&appPaths=%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2F%5Bslug%5D%2Fpage.js&appDir=D%3A%5CLaptop%20data%5CDRIPLY-CHAT%5Cchat-next%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CLaptop%20data%5CDRIPLY-CHAT%5Cchat-next&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();