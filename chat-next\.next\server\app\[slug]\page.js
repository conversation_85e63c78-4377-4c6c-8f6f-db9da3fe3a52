/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/[slug]/page";
exports.ids = ["app/[slug]/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Bslug%5D%2Fpage&page=%2F%5Bslug%5D%2Fpage&appPaths=%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2F%5Bslug%5D%2Fpage.js&appDir=D%3A%5CLaptop%20data%5CDRIPLY-CHAT%5Cchat-next%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CLaptop%20data%5CDRIPLY-CHAT%5Cchat-next&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Bslug%5D%2Fpage&page=%2F%5Bslug%5D%2Fpage&appPaths=%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2F%5Bslug%5D%2Fpage.js&appDir=D%3A%5CLaptop%20data%5CDRIPLY-CHAT%5Cchat-next%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CLaptop%20data%5CDRIPLY-CHAT%5Cchat-next&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.js */ \"(rsc)/./src/app/layout.js\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[slug]/page.js */ \"(rsc)/./src/app/[slug]/page.js\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '[slug]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\app\\\\[slug]\\\\page.js\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\app\\\\layout.js\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\app\\\\[slug]\\\\page.js\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/[slug]/page\",\n        pathname: \"/[slug]\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Bslug%5D%2Fpage&page=%2F%5Bslug%5D%2Fpage&appPaths=%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2F%5Bslug%5D%2Fpage.js&appDir=D%3A%5CLaptop%20data%5CDRIPLY-CHAT%5Cchat-next%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CLaptop%20data%5CDRIPLY-CHAT%5Cchat-next&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Wix_Madefor_Text%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-wix-madefor-text%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22wixMadeforText%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CClientWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Wix_Madefor_Text%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-wix-madefor-text%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22wixMadeforText%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CClientWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ClientWrapper.jsx */ \"(rsc)/./src/components/ClientWrapper.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Wix_Madefor_Text%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-wix-madefor-text%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22wixMadeforText%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CClientWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CChatInterfaceWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CChatInterfaceWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ChatInterfaceWrapper.jsx */ \"(rsc)/./src/components/ChatInterfaceWrapper.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNMYXB0b3AlMjBkYXRhJTVDJTVDRFJJUExZLUNIQVQlNUMlNUNjaGF0LW5leHQlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDQ2hhdEludGVyZmFjZVdyYXBwZXIuanN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOExBQW9KIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiRDpcXFxcTGFwdG9wIGRhdGFcXFxcRFJJUExZLUNIQVRcXFxcY2hhdC1uZXh0XFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXENoYXRJbnRlcmZhY2VXcmFwcGVyLmpzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CChatInterfaceWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxMYXB0b3AgZGF0YVxcRFJJUExZLUNIQVRcXGNoYXQtbmV4dFxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/[slug]/page.js":
/*!********************************!*\
  !*** ./src/app/[slug]/page.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   generateMetadata: () => (/* binding */ generateMetadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ChatInterfaceWrapper__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/ChatInterfaceWrapper */ \"(rsc)/./src/components/ChatInterfaceWrapper.jsx\");\n\n\n\n// Dynamic metadata generation using your API response data\nasync function generateMetadata({ params }) {\n    const { slug } = await params;\n    // Default fallback metadata\n    let title = `Chat with ${slug} - Driply`;\n    let description = `Start a conversation with ${slug} on Driply platform`;\n    let keywords = [\n        'chat',\n        slug,\n        'driply',\n        'ai',\n        'conversation'\n    ];\n    let businessName = 'Driply';\n    let image = '/og-image.jpg';\n    try {\n        // Fetch the same data that your ChatContext uses\n        const response = await fetch(`${\"https://api-develop.driply.me/\" || 0}/api/chat/settings?customerName=${slug}`, {\n            method: 'GET',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            // Cache for better performance\n            next: {\n                revalidate: 300\n            }\n        });\n        if (response.ok) {\n            const data = await response.json();\n            console.log('[Metadata] API Response:', data);\n            if (data && data.customerName) {\n                // Extract data from your API response structure\n                businessName = data.businessName || 'Driply';\n                const metaData = data.metaData || {};\n                // Use the metadata from your API response\n                title = metaData.title || `${businessName} Chat - ${slug}`;\n                description = metaData.description || `Chat with ${businessName} - Powered by Driply`;\n                // Handle keywords - convert string to array if needed\n                if (metaData.keywords) {\n                    keywords = typeof metaData.keywords === 'string' ? metaData.keywords.split(',').map((k)=>k.trim()) : Array.isArray(metaData.keywords) ? metaData.keywords : [\n                        metaData.keywords\n                    ];\n                } else {\n                    keywords = [\n                        'chat',\n                        slug,\n                        businessName.toLowerCase(),\n                        'driply',\n                        'ai',\n                        'conversation'\n                    ];\n                }\n                // Use custom image if provided\n                if (metaData.image && metaData.image.trim()) {\n                    image = metaData.image;\n                }\n                console.log('[Metadata] Using dynamic metadata:', {\n                    title,\n                    description,\n                    keywords,\n                    businessName,\n                    image\n                });\n            }\n        } else {\n            console.warn('[Metadata] API request failed, using fallback metadata');\n        }\n    } catch (error) {\n        console.warn('[Metadata] Error fetching dynamic metadata:', error.message);\n    // Will use fallback metadata\n    }\n    // Return the complete metadata object\n    return {\n        title: title,\n        description: description,\n        keywords: keywords,\n        openGraph: {\n            title: title,\n            description: description,\n            type: 'website',\n            url: `https://yourdomain.com/${slug}`,\n            siteName: businessName,\n            images: [\n                {\n                    url: image,\n                    width: 1200,\n                    height: 630,\n                    alt: `${businessName} Chat - ${slug}`\n                }\n            ],\n            locale: 'en_US'\n        },\n        twitter: {\n            card: 'summary_large_image',\n            title: title,\n            description: description,\n            images: [\n                image\n            ]\n        },\n        robots: {\n            index: true,\n            follow: true\n        },\n        authors: [\n            {\n                name: businessName\n            }\n        ],\n        creator: businessName,\n        publisher: 'Driply'\n    };\n}\n// Server component - no \"use client\" directive\nconst page = async ({ params, searchParams })=>{\n    const { slug } = await params;\n    const queryParams = await searchParams;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatInterfaceWrapper__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            slug: slug,\n            query: queryParams\n        }, void 0, false, {\n            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\app\\\\[slug]\\\\page.js\",\n            lineNumber: 110,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\app\\\\[slug]\\\\page.js\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (page);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/[slug]/page.js\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"f57a3c65339e\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcTGFwdG9wIGRhdGFcXERSSVBMWS1DSEFUXFxjaGF0LW5leHRcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImY1N2EzYzY1MzM5ZVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.js":
/*!***************************!*\
  !*** ./src/app/layout.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.js\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.js\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_js_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.js\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.js\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_js_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Wix_Madefor_Text_arguments_variable_font_wix_madefor_text_subsets_latin_variableName_wixMadeforText___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.js\",\"import\":\"Wix_Madefor_Text\",\"arguments\":[{\"variable\":\"--font-wix-madefor-text\",\"subsets\":[\"latin\"]}],\"variableName\":\"wixMadeforText\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.js\\\",\\\"import\\\":\\\"Wix_Madefor_Text\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-wix-madefor-text\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"wixMadeforText\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Wix_Madefor_Text_arguments_variable_font_wix_madefor_text_subsets_latin_variableName_wixMadeforText___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_js_import_Wix_Madefor_Text_arguments_variable_font_wix_madefor_text_subsets_latin_variableName_wixMadeforText___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_ClientWrapper__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/ClientWrapper */ \"(rsc)/./src/components/ClientWrapper.jsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: {\n        default: \"Driply - AI Chat Platform\",\n        template: \"%s | Driply\"\n    },\n    description: \"Driply is an advanced AI chat platform that provides intelligent conversations and seamless user experience.\",\n    keywords: [\n        \"AI\",\n        \"chat\",\n        \"conversation\",\n        \"driply\",\n        \"artificial intelligence\"\n    ],\n    authors: [\n        {\n            name: \"Driply Team\"\n        }\n    ],\n    creator: \"Driply\",\n    publisher: \"Driply\",\n    robots: {\n        index: true,\n        follow: true\n    },\n    openGraph: {\n        type: \"website\",\n        locale: \"en_US\",\n        url: \"https://yourdomain.com\",\n        siteName: \"Driply\",\n        title: \"Driply - AI Chat Platform\",\n        description: \"Driply is an advanced AI chat platform that provides intelligent conversations and seamless user experience.\",\n        images: [\n            {\n                url: \"/og-image.jpg\",\n                width: 1200,\n                height: 630,\n                alt: \"Driply AI Chat Platform\"\n            }\n        ]\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"Driply - AI Chat Platform\",\n        description: \"Driply is an advanced AI chat platform that provides intelligent conversations and seamless user experience.\",\n        images: [\n            \"/og-image.jpg\"\n        ]\n    },\n    viewport: \"width=device-width, initial-scale=1\",\n    themeColor: \"#000000\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_js_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default().variable)} ${(next_font_google_target_css_path_src_app_layout_js_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default().variable)} ${(next_font_google_target_css_path_src_app_layout_js_import_Wix_Madefor_Text_arguments_variable_font_wix_madefor_text_subsets_latin_variableName_wixMadeforText___WEBPACK_IMPORTED_MODULE_5___default().variable)} antialiased`,\n            suppressHydrationWarning: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientWrapper__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\app\\\\layout.js\",\n                lineNumber: 71,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\app\\\\layout.js\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\app\\\\layout.js\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.js\n");

/***/ }),

/***/ "(rsc)/./src/components/ChatInterfaceWrapper.jsx":
/*!*************************************************!*\
  !*** ./src/components/ChatInterfaceWrapper.jsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterfaceWrapper.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Laptop data\\DRIPLY-CHAT\\chat-next\\src\\components\\ChatInterfaceWrapper.jsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/ClientWrapper.jsx":
/*!******************************************!*\
  !*** ./src/components/ClientWrapper.jsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ClientWrapper.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Laptop data\\DRIPLY-CHAT\\chat-next\\src\\components\\ClientWrapper.jsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Wix_Madefor_Text%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-wix-madefor-text%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22wixMadeforText%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CClientWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Wix_Madefor_Text%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-wix-madefor-text%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22wixMadeforText%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CClientWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ClientWrapper.jsx */ \"(ssr)/./src/components/ClientWrapper.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Wix_Madefor_Text%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-wix-madefor-text%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22wixMadeforText%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CClientWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CChatInterfaceWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CChatInterfaceWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ChatInterfaceWrapper.jsx */ \"(ssr)/./src/components/ChatInterfaceWrapper.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNMYXB0b3AlMjBkYXRhJTVDJTVDRFJJUExZLUNIQVQlNUMlNUNjaGF0LW5leHQlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDQ2hhdEludGVyZmFjZVdyYXBwZXIuanN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOExBQW9KIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiRDpcXFxcTGFwdG9wIGRhdGFcXFxcRFJJUExZLUNIQVRcXFxcY2hhdC1uZXh0XFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXENoYXRJbnRlcmZhY2VXcmFwcGVyLmpzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CChatInterfaceWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/assets/images/logo.png":
/*!************************************!*\
  !*** ./src/assets/images/logo.png ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/logo.425de58c.png\",\"height\":188,\"width\":188,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Flogo.425de58c.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXNzZXRzL2ltYWdlcy9sb2dvLnBuZyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsQ0FBQyw0TEFBNEwiLCJzb3VyY2VzIjpbIkQ6XFxMYXB0b3AgZGF0YVxcRFJJUExZLUNIQVRcXGNoYXQtbmV4dFxcc3JjXFxhc3NldHNcXGltYWdlc1xcbG9nby5wbmciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2xvZ28uNDI1ZGU1OGMucG5nXCIsXCJoZWlnaHRcIjoxODgsXCJ3aWR0aFwiOjE4OCxcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZsb2dvLjQyNWRlNThjLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo4fTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/assets/images/logo.png\n");

/***/ }),

/***/ "(ssr)/./src/components/ChatInterface.jsx":
/*!******************************************!*\
  !*** ./src/components/ChatInterface.jsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(ssr)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/constants */ \"(ssr)/./src/utils/constants.js\");\n/* harmony import */ var _utils_config__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/config */ \"(ssr)/./src/utils/config.js\");\n/* harmony import */ var _barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FaArrowUp,FaChevronLeft,FaChevronRight!=!react-icons/fa6 */ \"(ssr)/./node_modules/react-icons/fa6/index.mjs\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../contexts/ChatContext */ \"(ssr)/./src/contexts/ChatContext.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nconst ChatInterface = ({ slug, query })=>{\n    // Context API\n    const { metadata, updateMetadata, setLoadingState, setErrorState, clearError, settingData, updateSettingData, getCustomerName, getBusinessName, hasMetadata, loading: contextLoading, error: contextError } = (0,_contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__.useChatContext)();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [currentSlide, setCurrentSlide] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [touchStart, setTouchStart] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [touchEnd, setTouchEnd] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isTablet, setIsTablet] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showInitialUI, setShowInitialUI] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isBotTyping, setIsBotTyping] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isBotThinking, setIsBotThinking] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [sseConnection, setSseConnection] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isFirstMessage, setIsFirstMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [shouldScrollToTop, setShouldScrollToTop] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Enhanced message pairing state\n    const [pendingQueries, setPendingQueries] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(new Map()); // Track queries waiting for responses\n    const [messageSequence, setMessageSequence] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0); // Global sequence counter\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const carouselRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const desktopMessagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const mobileMessagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const tabletMessagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // Debug: Log state updates when they actually happen\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (metadata && Object.keys(metadata).some({\n                \"ChatInterface.useEffect\": (key)=>metadata[key] !== null\n            }[\"ChatInterface.useEffect\"])) {\n                console.log(\"✅ METADATA STATE UPDATED:\", metadata);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        metadata\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (settingData && Object.keys(settingData).some({\n                \"ChatInterface.useEffect\": (key)=>settingData[key] !== null\n            }[\"ChatInterface.useEffect\"])) {\n                console.log(\"✅ SETTING DATA STATE UPDATED:\", settingData);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        settingData\n    ]);\n    // Cleanup pending queries that are too old (timeout mechanism)\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const cleanup = setInterval({\n                \"ChatInterface.useEffect.cleanup\": ()=>{\n                    const now = Date.now();\n                    const TIMEOUT_MS = 60000; // 60 seconds timeout\n                    setPendingQueries({\n                        \"ChatInterface.useEffect.cleanup\": (prev)=>{\n                            const updated = new Map(prev);\n                            let hasChanges = false;\n                            for (const [queryId, queryData] of prev.entries()){\n                                if (now - queryData.sentAt > TIMEOUT_MS) {\n                                    updated.delete(queryId);\n                                    hasChanges = true;\n                                }\n                            }\n                            return hasChanges ? updated : prev;\n                        }\n                    }[\"ChatInterface.useEffect.cleanup\"]);\n                }\n            }[\"ChatInterface.useEffect.cleanup\"], 10000); // Check every 10 seconds\n            return ({\n                \"ChatInterface.useEffect\": ()=>clearInterval(cleanup)\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], []);\n    // Initialize carousel to start from middle section for better circular behavior\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const totalCards = settingData?.suggestedTopics?.length || 0;\n            if (totalCards > 0 && currentSlide === 0) {\n                // Start from the middle section (second copy of cards)\n                setCurrentSlide(totalCards);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        settingData?.suggestedTopics?.length\n    ]);\n    // Utility functions for message pairing\n    const generateMessageId = ()=>{\n        const sequence = messageSequence + 1;\n        setMessageSequence(sequence);\n        return `msg_${Date.now()}_${sequence}_${Math.random().toString(36).substr(2, 9)}`;\n    };\n    const createMessagePairs = (messages)=>{\n        const pairs = [];\n        const processedMessages = new Set();\n        // Sort messages by timestamp to handle out-of-order arrivals\n        const sortedMessages = [\n            ...messages\n        ].sort((a, b)=>a.timestamp - b.timestamp);\n        // Group messages by queryId for proper pairing\n        const messageGroups = new Map();\n        sortedMessages.forEach((msg)=>{\n            if (msg.source === \"USER\") {\n                // User message starts a new conversation pair\n                const queryId = msg.queryId || msg.id || `fallback_${msg.timestamp}`;\n                if (!messageGroups.has(queryId)) {\n                    messageGroups.set(queryId, {\n                        user: null,\n                        bot: null,\n                        timestamp: msg.timestamp\n                    });\n                }\n                messageGroups.get(queryId).user = msg;\n            } else if (msg.source === \"BOT\") {\n                // Bot message should be paired with corresponding user message\n                const queryId = msg.queryId || msg.responseToId;\n                if (queryId && messageGroups.has(queryId)) {\n                    messageGroups.get(queryId).bot = msg;\n                } else {\n                    // Fallback: pair with most recent unpaired user message\n                    const unpairedGroups = Array.from(messageGroups.entries()).filter(([_, group])=>group.user && !group.bot).sort((a, b)=>b[1].timestamp - a[1].timestamp);\n                    if (unpairedGroups.length > 0) {\n                        const [groupId, group] = unpairedGroups[0];\n                        group.bot = msg;\n                        // Update the message with proper queryId for future reference\n                        msg.queryId = groupId;\n                    }\n                }\n            }\n        });\n        // Convert groups to pairs array, sorted by timestamp\n        const sortedGroups = Array.from(messageGroups.entries()).sort((a, b)=>a[1].timestamp - b[1].timestamp);\n        return sortedGroups.map(([queryId, group])=>({\n                id: queryId,\n                user: group.user,\n                bot: group.bot,\n                timestamp: group.timestamp,\n                isComplete: !!(group.user && group.bot),\n                isPending: !!(group.user && !group.bot)\n            }));\n    };\n    // Detect iOS for targeted fixes\n    const isIOS = ()=>{\n        if (false) {}\n        return false;\n    };\n    const username = slug;\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (username && \"undefined\" !== \"undefined\") {}\n        }\n    }[\"ChatInterface.useEffect\"], [\n        username\n    ]);\n    const fetchCustomer = async (customerName)=>{\n        try {\n            setLoadingState(true);\n            clearError();\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(`${_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_SETTINGS}?customerName=${customerName}`);\n            // Extract data from axios response\n            const responseData = response.data;\n            const metadataFromResponse = responseData.metaData;\n            updateSettingData(responseData);\n            updateMetadata(metadataFromResponse);\n            if (responseData && responseData.customerName) {\n                //  localStorage for backward compatibility\n                localStorage.setItem(\"customerName_userId\", responseData.customerName);\n                localStorage.setItem(\"BusinessName\", responseData.businessName);\n                window.dispatchEvent(new CustomEvent(\"businessNameLoaded\", {\n                    detail: {\n                        businessName: responseData.businessName\n                    }\n                }));\n                const existingUserId = localStorage.getItem(\"userID\");\n                if (existingUserId) {\n                    await fetchExistingMessages(existingUserId);\n                } else {\n                    await initializeChatSession();\n                }\n            } else {\n                await initializeChatSession();\n            }\n        } catch (error) {\n            console.error(\"Error fetching customer data:\", error);\n            setErrorState(\"Failed to load customer settings\");\n            // Fallback metadata\n            updateMetadata({\n                customerName: customerName,\n                businessName: \"Driply\"\n            });\n            localStorage.setItem(\"BusinessName\", \"Driply\");\n            window.dispatchEvent(new CustomEvent(\"businessNameLoaded\", {\n                detail: {\n                    businessName: \"Driply\"\n                }\n            }));\n            await initializeChatSession();\n        } finally{\n            setLoadingState(false);\n        }\n    };\n    const fetchExistingMessages = async (userId)=>{\n        // console.log(\"fetchExistingMessages\");\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(`${_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_MESSAGES}?userId=${userId}`);\n            if (response.data && response.data.length > 0) {\n                setMessages(response.data.map((msg)=>({\n                        text: msg.message,\n                        timestamp: new Date(msg.createdAt).getTime(),\n                        type: msg.type,\n                        source: msg.source\n                    })));\n            }\n            connectToSSE(userId);\n        } catch (error) {\n            await initializeChatSession();\n        }\n    };\n    const initializeChatSession = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_INIT, {\n                customerName: username\n            });\n            if (response.data && response.data.userId) {\n                localStorage.setItem(\"userId\", response.data.userId);\n                localStorage.setItem(\"userID\", response.data._id);\n                await fetchExistingMessages(response.data._id);\n            }\n        } catch (error) {\n            if (!localStorage.getItem(\"BusinessName\")) {\n                localStorage.setItem(\"BusinessName\", \"Driply\");\n                window.dispatchEvent(new CustomEvent(\"businessNameLoaded\", {\n                    detail: {\n                        businessName: \"Driply\"\n                    }\n                }));\n            }\n        }\n    };\n    const connectToSSE = (userId)=>{\n        try {\n            if (sseConnection) {\n                sseConnection.close();\n            }\n            const sseUrl = (0,_utils_config__WEBPACK_IMPORTED_MODULE_4__.getSSEUrl)(userId);\n            const eventSource = new EventSource(sseUrl);\n            setSseConnection(eventSource);\n            eventSource.addEventListener(\"message\", ({ data })=>{\n                try {\n                    const contents = JSON.parse(data);\n                    handleSSEMessage(contents);\n                } catch (error) {\n                // Handle parsing error silently\n                }\n            });\n            eventSource.addEventListener(\"error\", ()=>{\n            // Handle connection error silently\n            });\n        } catch (error) {\n        // Handle SSE connection error silently\n        }\n    };\n    const handleSSEMessage = (data)=>{\n        const subtype = data.subType || data.subtype || data.type || \"UNKNOWN\";\n        const content = data.content || \"\";\n        const message = data.message || data.text || \"\";\n        if ([\n            \"TYPING\",\n            \"THINKING\",\n            \"BEHAVIOUR_MESSAGE\"\n        ].includes(subtype)) {\n            switch(subtype){\n                case \"TYPING\":\n                    setIsBotTyping(true);\n                    setIsBotThinking(false);\n                    break;\n                case \"THINKING\":\n                    setIsBotThinking(true);\n                    setIsBotTyping(false);\n                    break;\n                case \"BEHAVIOUR_MESSAGE\":\n                    setIsBotTyping(false);\n                    setIsBotThinking(false);\n                    break;\n            }\n            return;\n        }\n        const displayText = message || content;\n        if (!displayText) return;\n        const displayType = [\n            \"TEXT\",\n            \"MESSAGE\",\n            \"DATA_MESSAGE\"\n        ].includes(subtype) ? subtype : \"UNKNOWN\";\n        // Enhanced message pairing logic\n        const responseToId = data.queryId || data.responseToId || data.correlationId;\n        let queryId = responseToId;\n        // If no explicit queryId, find the most recent pending query\n        if (!queryId) {\n            const pendingEntries = Array.from(pendingQueries.entries()).sort((a, b)=>b[1].timestamp - a[1].timestamp);\n            if (pendingEntries.length > 0) {\n                queryId = pendingEntries[0][0];\n            }\n        }\n        // Create bot message with proper pairing information\n        const botMessage = {\n            id: generateMessageId(),\n            text: displayText,\n            timestamp: Date.now(),\n            type: displayType,\n            source: \"BOT\",\n            queryId: queryId,\n            responseToId: queryId // Explicit response relationship\n        };\n        setMessages((prev)=>[\n                ...prev,\n                botMessage\n            ]);\n        // Remove from pending queries if we found a match\n        if (queryId && pendingQueries.has(queryId)) {\n            setPendingQueries((prev)=>{\n                const updated = new Map(prev);\n                updated.delete(queryId);\n                return updated;\n            });\n        }\n        setIsBotTyping(false);\n        setIsBotThinking(false);\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (message.trim()) {\n            const userMessage = message.trim();\n            const isFirstMsg = messages.length === 0;\n            // Generate unique ID for this query\n            const queryId = generateMessageId();\n            const userMessageObj = {\n                id: queryId,\n                queryId: queryId,\n                text: userMessage,\n                timestamp: Date.now(),\n                type: \"TEXT\",\n                source: \"USER\"\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    userMessageObj\n                ]);\n            // Track this query as pending response\n            setPendingQueries((prev)=>new Map(prev).set(queryId, {\n                    id: queryId,\n                    message: userMessage,\n                    timestamp: userMessageObj.timestamp,\n                    sentAt: Date.now()\n                }));\n            setMessage(\"\");\n            setIsTyping(false);\n            // For first message, set flag to scroll to top\n            if (isFirstMsg) {\n                setShouldScrollToTop(true);\n                setIsFirstMessage(false);\n            }\n            // Show loading dots immediately after sending message\n            setIsBotThinking(true);\n            setIsBotTyping(false);\n            // Reset textarea\n            if (inputRef.current) {\n                inputRef.current.style.height = \"104px\";\n                inputRef.current.scrollTop = 0;\n                inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n                inputRef.current.classList.add(\"reset-height\");\n            }\n            try {\n                const userId = localStorage.getItem(\"userID\");\n                if (userId) {\n                    await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_SEND, {\n                        userId: userId,\n                        message: userMessage,\n                        queryId: queryId,\n                        typs: \"TEXT\",\n                        source: \"USER\",\n                        isTest: query.isTest === \"1\" ? true : false\n                    });\n                }\n            } catch (error) {\n                // Hide loading dots on error\n                setIsBotThinking(false);\n                setIsBotTyping(false);\n                const errorMessage = {\n                    id: generateMessageId(),\n                    text: \"Sorry, there was an error sending your message. Please try again.\",\n                    timestamp: Date.now(),\n                    type: \"TEXT\",\n                    source: \"BOT\",\n                    queryId: queryId,\n                    isError: true\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        errorMessage\n                    ]);\n                // Remove from pending queries since we got an error\n                setPendingQueries((prev)=>{\n                    const updated = new Map(prev);\n                    updated.delete(queryId);\n                    return updated;\n                });\n            }\n        }\n    };\n    const handleTextareaResize = (textarea)=>{\n        if (textarea) {\n            textarea.style.height = \"104px\";\n            const scrollHeight = textarea.scrollHeight;\n            if (isIOS()) {\n                if (scrollHeight > 48) {\n                    textarea.style.overflowY = \"auto\";\n                } else {\n                    textarea.style.overflowY = \"hidden\";\n                }\n            } else {\n                const newHeight = Math.min(250, Math.max(104, scrollHeight));\n                textarea.style.height = `${newHeight}px`;\n                textarea.style.overflowY = scrollHeight > newHeight ? \"auto\" : \"hidden\";\n            }\n        }\n    };\n    const handleInputChange = (e)=>{\n        const value = e.target.value;\n        setMessage(value);\n        handleTextareaResize(e.target);\n        if (messages.length === 0) {\n            setIsTyping(value.length > 0);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length > 0 && inputRef.current && !message) {\n                const timer = setTimeout({\n                    \"ChatInterface.useEffect.timer\": ()=>{\n                        if (inputRef.current) {\n                            inputRef.current.style.height = \"104px\";\n                            inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n                            inputRef.current.classList.add(\"reset-height\");\n                        }\n                    }\n                }[\"ChatInterface.useEffect.timer\"], 50);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                })[\"ChatInterface.useEffect\"];\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages,\n        message\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length > 0) {\n                handleChatGPTScroll();\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages,\n        isBotTyping,\n        isBotThinking\n    ]);\n    const handleChatGPTScroll = ()=>{\n        setTimeout(()=>{\n            // Determine screen type and get appropriate container\n            const screenWidth = window.innerWidth;\n            const isDesktopLayout = screenWidth >= 1024;\n            const isTabletLayout = screenWidth >= 768 && screenWidth < 1024;\n            let container;\n            if (isDesktopLayout) {\n                container = desktopMessagesContainerRef.current;\n            } else if (isTabletLayout) {\n                container = tabletMessagesContainerRef.current;\n            } else {\n                container = mobileMessagesContainerRef.current;\n            }\n            if (!container) return;\n            if (shouldScrollToTop) {\n                // For first message, scroll to top to show the message there\n                container.scrollTo({\n                    top: 0,\n                    behavior: \"smooth\"\n                });\n                setShouldScrollToTop(false);\n            } else {\n                // Different scrolling behavior for each screen type\n                const messageElements = container.querySelectorAll(\".message-pair\");\n                if (messageElements.length > 0) {\n                    const lastMessagePair = messageElements[messageElements.length - 1];\n                    const containerHeight = container.clientHeight;\n                    const pairHeight = lastMessagePair.offsetHeight;\n                    const pairTop = lastMessagePair.offsetTop;\n                    if (isTabletLayout) {\n                        // Tablet-specific scrolling: Keep messages visible by scrolling to bottom of container\n                        // This ensures both user message and AI response remain visible\n                        const scrollPosition = Math.max(0, pairTop + pairHeight - containerHeight + 100);\n                        container.scrollTo({\n                            top: scrollPosition,\n                            behavior: \"smooth\"\n                        });\n                    } else {\n                        // Desktop and mobile: Center the message pair on screen\n                        const scrollPosition = pairTop - (containerHeight - pairHeight) / 2;\n                        container.scrollTo({\n                            top: Math.max(0, scrollPosition),\n                            behavior: \"smooth\"\n                        });\n                    }\n                }\n            }\n        }, 150);\n    };\n    const renderMessagePairs = ()=>{\n        // Use the enhanced message pairing logic\n        const messagePairs = createMessagePairs(messages);\n        return messagePairs.map((pair, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"message-pair flex flex-col justify-start\",\n                \"data-pair-id\": pair.id,\n                \"data-is-complete\": pair.isComplete,\n                \"data-is-pending\": pair.isPending,\n                style: {\n                    minHeight: i === messagePairs.length - 1 ? isMobile ? \"calc(100vh - 200px)\" // Mobile-specific height for newest message\n                     : \"calc(100vh - 200px)\" // Desktop/tablet height for newest message\n                     : \"\",\n                    paddingTop: i === 0 ? \"1rem\" : \"1rem\",\n                    paddingBottom: \"0rem\"\n                },\n                children: [\n                    pair.user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-end mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `text-[16px] font-[400] rounded-3xl max-w-xs lg:max-w-lg break-words hyphens-auto bg-gray-100 ${isMobile ? \"rounded-[15px] px-3 py-2\" : \"px-4 py-3\"}`,\n                            \"data-message-id\": pair.user.id,\n                            \"data-query-id\": pair.user.queryId,\n                            style: {\n                                overflowWrap: \"break-word\",\n                                wordBreak: \"break-word\",\n                                maxWidth: isMobile ? \"90%\" : undefined\n                            },\n                            children: [\n                                pair.user.text,\n                                pair.user.isSuggestion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-500 ml-2\",\n                                    children: \"\\uD83D\\uDCA1\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 634,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 620,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 619,\n                        columnNumber: 11\n                    }, undefined),\n                    pair.bot && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-start\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `text-[16px] font-[400] rounded-3xl max-w-xs lg:max-w-lg break-words hyphens-auto bg-white ${isMobile ? \"rounded-[15px] px-3 py-2\" : \"px-4 py-3\"} ${pair.bot.isError ? \"border-red-200 bg-red-50\" : \"\"}`,\n                            \"data-message-id\": pair.bot.id,\n                            \"data-query-id\": pair.bot.queryId,\n                            \"data-response-to\": pair.bot.responseToId,\n                            style: {\n                                overflowWrap: \"break-word\",\n                                wordBreak: \"break-word\",\n                                maxWidth: isMobile ? \"90%\" : undefined\n                            },\n                            children: [\n                                pair.bot.text,\n                                pair.bot.isError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-red-500 ml-2\",\n                                    children: \"⚠️\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 658,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 643,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 642,\n                        columnNumber: 11\n                    }, undefined),\n                    i === messagePairs.length - 1 && (isBotTyping || isBotThinking) && !pair.bot && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-start\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `bg-white ${isMobile ? \"px-3 py-2 rounded-[15px]\" : \"px-4 py-3 rounded-3xl\"}`,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-black rounded-full loading-dot\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 677,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-black rounded-full loading-dot\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 678,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-black rounded-full loading-dot\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 679,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 676,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 669,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 668,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, `pair-${pair.id}`, true, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 600,\n                columnNumber: 7\n            }, undefined));\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSubmit(e);\n        }\n    };\n    const handleSuggestionClick = async (cardTitle)=>{\n        const isFirstMsg = messages.length === 0;\n        // Generate unique ID for this suggestion query\n        const queryId = generateMessageId();\n        const userMessageObj = {\n            id: queryId,\n            queryId: queryId,\n            text: cardTitle,\n            timestamp: Date.now(),\n            type: \"TEXT\",\n            source: \"USER\",\n            isSuggestion: true\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessageObj\n            ]);\n        // Track this suggestion query as pending response\n        setPendingQueries((prev)=>new Map(prev).set(queryId, {\n                id: queryId,\n                message: cardTitle,\n                timestamp: userMessageObj.timestamp,\n                sentAt: Date.now(),\n                isSuggestion: true\n            }));\n        setMessage(\"\");\n        setIsTyping(false);\n        // For first message, set flag to scroll to top\n        if (isFirstMsg) {\n            setShouldScrollToTop(true);\n            setIsFirstMessage(false);\n        }\n        // Show loading dots immediately after sending message\n        setIsBotThinking(true);\n        setIsBotTyping(false);\n        if (inputRef.current) {\n            inputRef.current.style.height = \"104px\";\n            inputRef.current.scrollTop = 0;\n            inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n            inputRef.current.classList.add(\"reset-height\");\n        }\n        try {\n            const userId = localStorage.getItem(\"userID\");\n            if (userId) {\n                await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_SEND, {\n                    userId: userId,\n                    message: cardTitle,\n                    queryId: queryId,\n                    customerName: username,\n                    isSuggestion: true\n                });\n            }\n        } catch (error) {\n            // Hide loading dots on error\n            setIsBotThinking(false);\n            setIsBotTyping(false);\n            const errorMessage = {\n                id: generateMessageId(),\n                text: \"Sorry, there was an error sending your message. Please try again.\",\n                timestamp: Date.now(),\n                type: \"TEXT\",\n                source: \"BOT\",\n                queryId: queryId,\n                isError: true\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n            // Remove from pending queries since we got an error\n            setPendingQueries((prev)=>{\n                const updated = new Map(prev);\n                updated.delete(queryId);\n                return updated;\n            });\n        }\n    };\n    const getMaxSlides = ()=>{\n        if (isMobile) return settingData?.suggestedTopics.length;\n        return Math.ceil(settingData?.suggestedTopics.length / 2);\n    };\n    const nextSlide = ()=>{\n        const totalCards = settingData?.suggestedTopics?.length || 0;\n        if (totalCards === 0) return;\n        // Fix: Prevent scrolling past the last card\n        const maxSlide = totalCards - 1; // Last card index (0-based)\n        setCurrentSlide((prev)=>{\n            const nextSlide = prev + 1;\n            return nextSlide > maxSlide ? maxSlide : nextSlide;\n        });\n    };\n    const prevSlide = ()=>{\n        setCurrentSlide((prev)=>{\n            const prevSlide = prev - 1;\n            return prevSlide < 0 ? 0 : prevSlide;\n        });\n    };\n    // Check if we're at the boundaries for button states\n    const isAtStart = currentSlide === 0;\n    const isAtEnd = ()=>{\n        const totalCards = settingData?.suggestedTopics?.length || 0;\n        if (totalCards === 0) return true;\n        return currentSlide >= totalCards - 1; // Fix: Use >= instead of >\n    };\n    const handleNextSlide = ()=>{\n        nextSlide();\n    };\n    const handlePrevSlide = ()=>{\n        prevSlide();\n    };\n    const handleTouchStart = (e)=>{\n        setTouchStart(e.targetTouches[0].clientX);\n    };\n    const handleTouchMove = (e)=>{\n        setTouchEnd(e.targetTouches[0].clientX);\n    };\n    const handleTouchEnd = ()=>{\n        if (!touchStart || !touchEnd) return;\n        const swipeDistance = touchStart - touchEnd;\n        const minSwipeDistance = 50;\n        if (swipeDistance > minSwipeDistance) {\n            nextSlide();\n        }\n        if (swipeDistance < -minSwipeDistance) {\n            prevSlide();\n        }\n        setTouchStart(0);\n        setTouchEnd(0);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length === 0) {\n                setShowInitialUI(false);\n                const timer = setTimeout({\n                    \"ChatInterface.useEffect.timer\": ()=>setShowInitialUI(true)\n                }[\"ChatInterface.useEffect.timer\"], 60);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                })[\"ChatInterface.useEffect\"];\n            } else {\n                setShowInitialUI(false);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages.length\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const checkScreenSize = {\n                \"ChatInterface.useEffect.checkScreenSize\": ()=>{\n                    if (false) {}\n                }\n            }[\"ChatInterface.useEffect.checkScreenSize\"];\n            checkScreenSize();\n            const handleResize = {\n                \"ChatInterface.useEffect.handleResize\": ()=>{\n                    setCurrentSlide(0);\n                    checkScreenSize();\n                }\n            }[\"ChatInterface.useEffect.handleResize\"];\n            if (false) {}\n        }\n    }[\"ChatInterface.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (inputRef.current && messages.length === 0) {\n                const shouldAutoFocus = window.innerWidth >= 768;\n                if (shouldAutoFocus) {\n                    const timer = setTimeout({\n                        \"ChatInterface.useEffect.timer\": ()=>{\n                            inputRef.current?.focus();\n                        }\n                    }[\"ChatInterface.useEffect.timer\"], 100);\n                    return ({\n                        \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                    })[\"ChatInterface.useEffect\"];\n                }\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages.length\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            return ({\n                \"ChatInterface.useEffect\": ()=>{\n                    if (sseConnection) {\n                        sseConnection.close();\n                    }\n                }\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], [\n        sseConnection\n    ]);\n    // Check if settingData has been populated with actual data\n    const hasSettingData = settingData && Object.keys(settingData).some((key)=>settingData[key] !== null);\n    // Show loading state while waiting for data\n    if (!hasSettingData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white flex flex-col min-h-screen items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 914,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading chat settings...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 915,\n                        columnNumber: 11\n                    }, undefined),\n                    contextError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-500 mt-2\",\n                        children: [\n                            \"Error: \",\n                            contextError\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 917,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 913,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n            lineNumber: 912,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"bg-white flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"hidden lg:flex flex-1 flex-col px-4 \",\n                children: messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex flex-1 items-center justify-center min-h-[calc(100vh-64px)] mt-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex flex-col items-center w-[768px] justify-center mx-auto \",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    transitionDelay: showInitialUI ? \"40ms\" : \"0ms\"\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + `text-4xl text-gray-900 mb-6 text-center transition-all duration-700 ${showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"}`,\n                                children: \"How can I help you?\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 931,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                style: {\n                                    transitionDelay: showInitialUI ? \"120ms\" : \"0ms\"\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + `relative w-full mb-6 flex flex-col items-center transition-all duration-500 ${showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        ref: messages.length === 0 ? inputRef : null,\n                                        value: message,\n                                        onChange: handleInputChange,\n                                        onKeyDown: handleKeyPress,\n                                        placeholder: \"Ask anything\",\n                                        autoFocus: messages.length === 0,\n                                        rows: 1,\n                                        style: {\n                                            boxSizing: \"border-box\",\n                                            zIndex: 1001,\n                                            position: \"relative\"\n                                        },\n                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"pt-3 px-4 lg:p-4 pb-16 pr-12 w-full min-h-[104px] max-h-[250px] overflow-y-auto hide-scrollbar text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base shadow-md resize-none reset-height\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 951,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: message.trim().length === 0,\n                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + `absolute right-3 bottom-3 w-9 h-9 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation ${message.trim().length === 0 ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                            className: \"w-4 h-4 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 975,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 966,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 942,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    transitionDelay: showInitialUI ? \"200ms\" : \"0ms\",\n                                    zIndex: 10\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + `relative w-full max-w-2xl transition-all duration-500 ease-in-out ${isTyping ? \"opacity-0 pointer-events-none\" : showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"overflow-hidden px-12\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: carouselRef,\n                                            style: {\n                                                transform: `translateX(-${currentSlide * 100}%)`,\n                                                paddingLeft: \"0\"\n                                            },\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex gap-4 transition-transform duration-300 ease-in-out\",\n                                            children: settingData?.suggestedTopics?.map((card, cardIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleSuggestionClick(card?.question),\n                                                    style: {\n                                                        // Calculate width based on container: max-w-2xl (672px) - px-12 (96px) - arrow space (80px) = ~496px\n                                                        width: isMobile ? \"calc(100vw - 120px)\" : \"496px\",\n                                                        minHeight: \"80px\" // Minimum height to accommodate wrapped text\n                                                    },\n                                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"py-3 px-4 rounded-[8px] bg-[#f6f6f6] text-left group transition-all duration-200 touch-manipulation flex-shrink-0 hover:bg-[#e6e6e6]\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-[16px] font-[600] text-black mb-1 leading-snug break-words word-wrap overflow-wrap-anywhere\",\n                                                            children: card.question\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 1013,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-[14px] text-gray-500 leading-snug break-words word-wrap overflow-wrap-anywhere\",\n                                                            children: card.subtitle\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 1016,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, cardIndex, true, {\n                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                    lineNumber: 1001,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 992,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 991,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handlePrevSlide,\n                                        disabled: isAtStart,\n                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + `absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-10 w-8 h-8 border rounded-full flex items-center justify-center shadow-md transition-all duration-200 touch-manipulation z-10 ${isAtStart ? 'bg-gray-100 border-gray-300 cursor-not-allowed' : 'bg-white border-gray-200 hover:bg-gray-50 cursor-pointer'}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronLeft, {\n                                            className: `w-3 h-3 ${isAtStart ? 'text-gray-400' : 'text-gray-600'}`\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1032,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 1023,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleNextSlide,\n                                        disabled: isAtEnd(),\n                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + `absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-10 w-8 h-8 border rounded-full flex items-center justify-center shadow-md transition-all duration-200 touch-manipulation z-10 ${isAtEnd() ? 'bg-gray-100 border-gray-300 cursor-not-allowed' : 'bg-white border-gray-200 hover:bg-gray-50 cursor-pointer'}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronRight, {\n                                            className: `w-3 h-3 ${isAtEnd() ? 'text-gray-400' : 'text-gray-600'}`\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1043,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 1034,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 978,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 930,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                    lineNumber: 929,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: desktopMessagesContainerRef,\n                            style: {\n                                overscrollBehavior: \"contain\"\n                            },\n                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex-1 overflow-y-auto max-h-[calc(100vh-200px)] mt-14\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"w-full lg:w-[768px] mx-auto px-4 h-fit \",\n                                children: [\n                                    renderMessagePairs(),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: messagesEndRef,\n                                        className: \"jsx-69ac67aa0147cd65\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 1057,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 1055,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 1050,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                zIndex: 1000,\n                                marginTop: \"20px\"\n                            },\n                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"fixed bottom-0 left-0 right-0 p-4 mt-5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSubmit,\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"relative w-full lg:w-[768px] mx-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            ref: messages.length > 0 ? inputRef : null,\n                                            value: message,\n                                            onChange: handleInputChange,\n                                            onKeyDown: handleKeyPress,\n                                            placeholder: \"Ask anything\",\n                                            rows: 1,\n                                            style: {\n                                                boxSizing: \"border-box\",\n                                                zIndex: 1001,\n                                                position: \"relative\"\n                                            },\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"pt-3 px-4 md:px-5 pb-10 pr-12 w-full min-h-[104px] max-h-[250px] overflow-y-auto text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base shadow-lg resize-none reset-height hide-scrollbar\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1069,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: message.trim().length === 0,\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + `absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation ${message.trim().length === 0 ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"}`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                                className: \"w-4 h-4 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1092,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1083,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1065,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-center mt-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-xs text-gray-500\",\n                                        children: [\n                                            \"This chat is powered by\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-black\",\n                                                children: \"Driply.me\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1098,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 1096,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1095,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 1061,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 927,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"lg:hidden overflow-hidden fixed inset-0 flex flex-col  mt-10 \",\n                children: [\n                    messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex flex-col items-center pt-15 pb-4 px-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"hidden md:flex lg:hidden flex-col items-center\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1111,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 1110,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    paddingBottom: \"260px\"\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex flex-col items-center justify-center flex-1 px-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"40ms\" : \"0ms\"\n                                    },\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + `text-2xl md:text-4xl text-gray-900 text-center leading-relaxed transition-all duration-700 ${showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"}`,\n                                    children: \"How can I help you?\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1117,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 1113,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: mobileMessagesContainerRef,\n                                style: {\n                                    overscrollBehavior: \"contain\"\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"md:hidden flex-1 overflow-y-auto max-h-[calc(100vh-190px)] hide-scrollbar\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"w-full max-w-[803px] mx-auto px-4  pt-12\",\n                                    children: [\n                                        renderMessagePairs(),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: messagesEndRef,\n                                            className: \"jsx-69ac67aa0147cd65\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1139,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1137,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 1132,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: tabletMessagesContainerRef,\n                                style: {\n                                    overscrollBehavior: \"contain\"\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"hidden md:block lg:hidden flex-1 overflow-y-auto max-h-[calc(100vh-100px)]  pb-20 hide-scrollbar\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"w-full max-w-[803px] mx-auto px-4 pb-6 pt-12\",\n                                    children: [\n                                        renderMessagePairs(),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: messagesEndRef,\n                                            className: \"jsx-69ac67aa0147cd65\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1151,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1149,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 1144,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    minHeight: \"160px\",\n                                    zIndex: 1000,\n                                    paddingBottom: \"env(safe-area-inset-bottom, 0)\",\n                                    transform: \"translateZ(0)\",\n                                    backfaceVisibility: \"hidden\"\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"fixed bottom-0 left-0 right-0 bg-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        transform: \"translateZ(0)\"\n                                    },\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"px-4 py-4 bg-white\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            style: {\n                                                zIndex: 1001\n                                            },\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"relative bg-white\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    transform: \"translateZ(0)\"\n                                                },\n                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"relative w-full max-w-[890px] mx-auto bg-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        ref: messages.length > 0 ? inputRef : null,\n                                                        value: message,\n                                                        onChange: handleInputChange,\n                                                        onKeyDown: handleKeyPress,\n                                                        placeholder: \"Ask anything\",\n                                                        rows: 1,\n                                                        style: {\n                                                            boxSizing: \"border-box\",\n                                                            maxHeight: \"250px\",\n                                                            zIndex: 1001,\n                                                            overscrollBehavior: \"none\",\n                                                            position: \"relative\"\n                                                        },\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"w-full min-h-[104px] pt-3 px-4 pb-10 pr-12 text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base resize-none overflow-y-auto reset-height\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1178,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        disabled: message.trim().length === 0,\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + `absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation ${message.trim().length === 0 ? \"bg-[#E0E0E0] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"}`,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                                            className: \"w-4 h-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 1203,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1194,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1174,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1169,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-center py-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"This chat is powered by\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-black\",\n                                                        children: \"Driply.me\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1210,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1208,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1207,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1165,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 1155,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"absolute bottom-0 left-0 right-0 bg-white\",\n                        children: messages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"120ms\" : \"0ms\",\n                                        position: \"relative\",\n                                        zIndex: 10,\n                                        backgroundColor: \"white\",\n                                        paddingBottom: \"10px\"\n                                    },\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + `${isMobile ? \"px-0\" : \"px-4\"} pt-2 pb-2 transition-all duration-500 ease-in-out ${isTyping ? \"opacity-0 pointer-events-none\" : showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + `overflow-hidden ${isMobile ? 'px-4' : 'px-12'}`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                ref: carouselRef,\n                                                style: {\n                                                    transform: `translateX(-${currentSlide * 100}%)`,\n                                                    paddingLeft: \"0\"\n                                                },\n                                                onTouchStart: handleTouchStart,\n                                                onTouchMove: handleTouchMove,\n                                                onTouchEnd: handleTouchEnd,\n                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex gap-4 transition-transform duration-300 ease-in-out\",\n                                                children: settingData?.suggestedTopics?.map((card, cardIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleSuggestionClick(card.question),\n                                                        style: {\n                                                            // Calculate width based on container size and padding\n                                                            width: isMobile ? \"calc(100vw - 80px)\" : \"calc(100% - 80px)\",\n                                                            maxWidth: isMobile ? \"320px\" : \"500px\",\n                                                            minHeight: isMobile ? \"90px\" : \"80px\" // Minimum height to accommodate wrapped text\n                                                        },\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + `${isMobile ? 'py-3 mt-3' : 'py-3'} px-4 rounded-[8px] bg-[#F6F6F6] text-left hover:bg-[#D6EDFF] transition-all duration-200 flex-shrink-0 touch-manipulation`,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + `${isMobile ? 'text-[16px]' : 'text-[16px]'} font-[600] text-black mb-1 leading-snug break-words word-wrap overflow-wrap-anywhere`,\n                                                                children: card.question\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                lineNumber: 1267,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + `${isMobile ? 'text-[14px]' : 'text-[14px]'} text-gray-500 leading-snug break-words word-wrap overflow-wrap-anywhere`,\n                                                                children: card.subtitle\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                lineNumber: 1270,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, cardIndex, true, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1254,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1242,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1241,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handlePrevSlide,\n                                            disabled: isAtStart,\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + `hidden md:flex absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-10 w-8 h-8 border rounded-full items-center justify-center shadow-md transition-all duration-200 touch-manipulation z-10 ${isAtStart ? 'bg-gray-100 border-gray-300 cursor-not-allowed' : 'bg-white border-gray-200 hover:bg-gray-50 cursor-pointer'}`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronLeft, {\n                                                className: `w-3 h-3 ${isAtStart ? 'text-gray-400' : 'text-gray-600'}`\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1288,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1279,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleNextSlide,\n                                            disabled: isAtEnd(),\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + `hidden md:flex absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-10 w-8 h-8 border rounded-full items-center justify-center shadow-md transition-all duration-200 touch-manipulation z-10 ${isAtEnd() ? 'bg-gray-100 border-gray-300 cursor-not-allowed' : 'bg-white border-gray-200 hover:bg-gray-50 cursor-pointer'}`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronRight, {\n                                                className: `w-3 h-3 ${isAtEnd() ? 'text-gray-400' : 'text-gray-600'}`\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1299,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1290,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1223,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"200ms\" : \"0ms\",\n                                        position: \"relative\",\n                                        zIndex: 5,\n                                        paddingBottom: \"env(safe-area-inset-bottom)\"\n                                    },\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + `px-4 bg-white transition-all duration-500 ${showInitialUI ? \"opacity-100 translate-y-0\" : \"translate-y-4\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"relative\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"relative w-full max-w-[890px] mx-auto\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        ref: messages.length === 0 ? inputRef : null,\n                                                        value: message,\n                                                        onChange: handleInputChange,\n                                                        onKeyDown: handleKeyPress,\n                                                        placeholder: \"Ask anything\",\n                                                        rows: 1,\n                                                        style: {\n                                                            boxSizing: \"border-box\",\n                                                            maxHeight: \"250px\",\n                                                            fontSize: \"16px\",\n                                                            zIndex: 1001,\n                                                            overscrollBehaviorY: \"contain\",\n                                                            position: \"relative\"\n                                                        },\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"w-full min-h-[104px] pt-3 px-4 pb-10 pr-12 text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base resize-none overflow-y-auto reset-height\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1317,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        disabled: message.trim().length === 0,\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + `absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation ${message.trim().length === 0 ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"}`,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                                            className: \"w-4 h-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 1343,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1334,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1316,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1315,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-center py-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"This chat is powered by\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-black\",\n                                                        children: \"Driply.me\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1350,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1348,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1347,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1304,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 1219,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 1107,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"69ac67aa0147cd65\",\n                children: '.loading-dot{-webkit-animation:loading-dots 1.4s infinite ease-in-out both;-moz-animation:loading-dots 1.4s infinite ease-in-out both;-o-animation:loading-dots 1.4s infinite ease-in-out both;animation:loading-dots 1.4s infinite ease-in-out both}.loading-dot:nth-child(1){-webkit-animation-delay:-.32s;-moz-animation-delay:-.32s;-o-animation-delay:-.32s;animation-delay:-.32s}.loading-dot:nth-child(2){-webkit-animation-delay:-.16s;-moz-animation-delay:-.16s;-o-animation-delay:-.16s;animation-delay:-.16s}.loading-dot:nth-child(3){-webkit-animation-delay:0s;-moz-animation-delay:0s;-o-animation-delay:0s;animation-delay:0s}@-webkit-keyframes loading-dots{0%,80%,100%{-webkit-transform:scale(1);transform:scale(1);opacity:1}40%{-webkit-transform:scale(1.5);transform:scale(1.5);opacity:.7}}@-moz-keyframes loading-dots{0%,80%,100%{-moz-transform:scale(1);transform:scale(1);opacity:1}40%{-moz-transform:scale(1.5);transform:scale(1.5);opacity:.7}}@-o-keyframes loading-dots{0%,80%,100%{-o-transform:scale(1);transform:scale(1);opacity:1}40%{-o-transform:scale(1.5);transform:scale(1.5);opacity:.7}}@keyframes loading-dots{0%,80%,100%{-webkit-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1);opacity:1}40%{-webkit-transform:scale(1.5);-moz-transform:scale(1.5);-o-transform:scale(1.5);transform:scale(1.5);opacity:.7}}.hide-scrollbar{-ms-overflow-style:none!important;scrollbar-width:none!important}.hide-scrollbar::-webkit-scrollbar{display:none!important}@media only screen and (max-width:1023px){.lg\\\\\\\\.jsx-69ac67aa0147cd65:hidden .absolute.bottom-0.jsx-69ac67aa0147cd65{position:fixed!important;bottom:env(safe-area-inset-bottom,0);left:0;right:0;width:100%;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;z-index:1000;background-color:white}textarea.jsx-69ac67aa0147cd65{-webkit-user-select:auto!important;-moz-user-select:auto!important;-ms-user-select:auto!important;user-select:auto!important;-webkit-appearance:none;-moz-appearance:none;-ms-appearance:none;appearance:none;-ms-scroll-chaining:none;overscroll-behavior:none;line-height:24px;max-height:250px!important;position:relative!important;-webkit-transform:translatez(0);-moz-transform:translatez(0);-ms-transform:translatez(0);-o-transform:translatez(0);transform:translatez(0);-webkit-backface-visibility:hidden;-moz-backface-visibility:hidden;backface-visibility:hidden;-webkit-perspective:1000;-moz-perspective:1000;perspective:1000;z-index:1001!important;background-color:white}.lg\\\\\\\\.jsx-69ac67aa0147cd65:hidden form.jsx-69ac67aa0147cd65{position:relative!important;z-index:1001;background-color:white}button[type=\"submit\"].jsx-69ac67aa0147cd65{position:absolute!important;right:12px;bottom:12px;z-index:1002!important;-webkit-tap-highlight-color:transparent;-ms-touch-action:-ms-manipulation;-ms-touch-action:manipulation;touch-action:manipulation;-webkit-transform:translatez(0);-moz-transform:translatez(0);-ms-transform:translatez(0);-o-transform:translatez(0);transform:translatez(0)}.lg\\\\\\\\.jsx-69ac67aa0147cd65:hidden .bg-white.jsx-69ac67aa0147cd65{background-color:white!important}}@media only screen and (min-width:1024px){textarea.jsx-69ac67aa0147cd65{max-height:250px!important;overflow-y:auto!important;-ms-scroll-chaining:none;overscroll-behavior:contain}.reset-height.jsx-69ac67aa0147cd65{min-height:104px!important;max-height:250px!important;overflow-y:auto!important}}.fixed-bottom.jsx-69ac67aa0147cd65{position:fixed!important;bottom:0;left:0;right:0;background:white}'\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n        lineNumber: 925,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatInterface);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ChatInterface.jsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ChatInterfaceWrapper.jsx":
/*!*************************************************!*\
  !*** ./src/components/ChatInterfaceWrapper.jsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatInterfaceWrapper)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ChatInterface__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ChatInterface */ \"(ssr)/./src/components/ChatInterface.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n/**\r\n * Client-side wrapper for ChatInterface component\r\n * This separates client components from server components to fix metadata issues\r\n * Following Next.js App Router best practices\r\n */ function ChatInterfaceWrapper({ slug, query }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatInterface__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        slug: slug,\n        query: query\n    }, void 0, false, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterfaceWrapper.jsx\",\n        lineNumber: 12,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9DaGF0SW50ZXJmYWNlV3JhcHBlci5qc3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUUwQjtBQUNrQjtBQUU1Qzs7OztDQUlDLEdBQ2MsU0FBU0UscUJBQXFCLEVBQUVDLElBQUksRUFBRUMsS0FBSyxFQUFFO0lBQzFELHFCQUFPLDhEQUFDSCxzREFBYUE7UUFBQ0UsTUFBTUE7UUFBTUMsT0FBT0E7Ozs7OztBQUMzQyIsInNvdXJjZXMiOlsiRDpcXExhcHRvcCBkYXRhXFxEUklQTFktQ0hBVFxcY2hhdC1uZXh0XFxzcmNcXGNvbXBvbmVudHNcXENoYXRJbnRlcmZhY2VXcmFwcGVyLmpzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcclxuXHJcbmltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCBDaGF0SW50ZXJmYWNlIGZyb20gJy4vQ2hhdEludGVyZmFjZSc7XHJcblxyXG4vKipcclxuICogQ2xpZW50LXNpZGUgd3JhcHBlciBmb3IgQ2hhdEludGVyZmFjZSBjb21wb25lbnRcclxuICogVGhpcyBzZXBhcmF0ZXMgY2xpZW50IGNvbXBvbmVudHMgZnJvbSBzZXJ2ZXIgY29tcG9uZW50cyB0byBmaXggbWV0YWRhdGEgaXNzdWVzXHJcbiAqIEZvbGxvd2luZyBOZXh0LmpzIEFwcCBSb3V0ZXIgYmVzdCBwcmFjdGljZXNcclxuICovXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIENoYXRJbnRlcmZhY2VXcmFwcGVyKHsgc2x1ZywgcXVlcnkgfSkge1xyXG4gIHJldHVybiA8Q2hhdEludGVyZmFjZSBzbHVnPXtzbHVnfSBxdWVyeT17cXVlcnl9IC8+O1xyXG59XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkNoYXRJbnRlcmZhY2UiLCJDaGF0SW50ZXJmYWNlV3JhcHBlciIsInNsdWciLCJxdWVyeSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ChatInterfaceWrapper.jsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ClientWrapper.jsx":
/*!******************************************!*\
  !*** ./src/components/ClientWrapper.jsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClientWrapper)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Navbar */ \"(ssr)/./src/components/Navbar.jsx\");\n/* harmony import */ var _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/ChatContext */ \"(ssr)/./src/contexts/ChatContext.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n/**\r\n * Client wrapper component that contains all client-side logic\r\n * This separates client components from server components to fix metadata issues\r\n * Following Next.js App Router best practices\r\n */ function ClientWrapper({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_ChatContext__WEBPACK_IMPORTED_MODULE_3__.ChatProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ClientWrapper.jsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ClientWrapper.jsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9DbGllbnRXcmFwcGVyLmpzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUUwQjtBQUNJO0FBQ3lCO0FBRXZEOzs7O0NBSUMsR0FDYyxTQUFTRyxjQUFjLEVBQUVDLFFBQVEsRUFBRTtJQUNoRCxxQkFDRSw4REFBQ0YsK0RBQVlBOzswQkFDWCw4REFBQ0QsK0NBQU1BOzs7OztZQUNORzs7Ozs7OztBQUdQIiwic291cmNlcyI6WyJEOlxcTGFwdG9wIGRhdGFcXERSSVBMWS1DSEFUXFxjaGF0LW5leHRcXHNyY1xcY29tcG9uZW50c1xcQ2xpZW50V3JhcHBlci5qc3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XHJcblxyXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgTmF2YmFyIGZyb20gXCIuL05hdmJhclwiO1xyXG5pbXBvcnQgeyBDaGF0UHJvdmlkZXIgfSBmcm9tIFwiLi4vY29udGV4dHMvQ2hhdENvbnRleHRcIjtcclxuXHJcbi8qKlxyXG4gKiBDbGllbnQgd3JhcHBlciBjb21wb25lbnQgdGhhdCBjb250YWlucyBhbGwgY2xpZW50LXNpZGUgbG9naWNcclxuICogVGhpcyBzZXBhcmF0ZXMgY2xpZW50IGNvbXBvbmVudHMgZnJvbSBzZXJ2ZXIgY29tcG9uZW50cyB0byBmaXggbWV0YWRhdGEgaXNzdWVzXHJcbiAqIEZvbGxvd2luZyBOZXh0LmpzIEFwcCBSb3V0ZXIgYmVzdCBwcmFjdGljZXNcclxuICovXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIENsaWVudFdyYXBwZXIoeyBjaGlsZHJlbiB9KSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxDaGF0UHJvdmlkZXI+XHJcbiAgICAgIDxOYXZiYXIgLz5cclxuICAgICAge2NoaWxkcmVufVxyXG4gICAgPC9DaGF0UHJvdmlkZXI+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJOYXZiYXIiLCJDaGF0UHJvdmlkZXIiLCJDbGllbnRXcmFwcGVyIiwiY2hpbGRyZW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ClientWrapper.jsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Navbar.jsx":
/*!***********************************!*\
  !*** ./src/components/Navbar.jsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_FiEdit_react_icons_fi__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FiEdit!=!react-icons/fi */ \"(ssr)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _assets_images_logo_png__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../assets/images/logo.png */ \"(ssr)/./src/assets/images/logo.png\");\n/* harmony import */ var _barrel_optimize_names_HiUserCircle_react_icons_hi2__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=HiUserCircle!=!react-icons/hi2 */ \"(ssr)/./node_modules/react-icons/hi2/index.mjs\");\n/* harmony import */ var _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../contexts/ChatContext */ \"(ssr)/./src/contexts/ChatContext.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nconst Navbar = ()=>{\n    const [businessName, setBusinessName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Get context values\n    const { metadata, hasMetadata } = (0,_contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__.useChatContext)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            // First, try to get business name from context\n            if (hasMetadata() && metadata.businessName) {\n                setBusinessName(metadata.businessName);\n                return;\n            }\n            // Fallback to localStorage and event listeners\n            const checkBusinessName = {\n                \"Navbar.useEffect.checkBusinessName\": ()=>{\n                    const storedName = localStorage.getItem(\"BusinessName\") || \"Driply\";\n                    if (storedName && storedName !== \"undefined\" && storedName !== \"null\") {\n                        setBusinessName(storedName);\n                        return true;\n                    }\n                    return false;\n                }\n            }[\"Navbar.useEffect.checkBusinessName\"];\n            if (checkBusinessName()) {\n                return;\n            }\n            const handleBusinessNameLoaded = {\n                \"Navbar.useEffect.handleBusinessNameLoaded\": (event)=>{\n                    if (event.detail && event.detail.businessName) {\n                        setBusinessName(event.detail.businessName);\n                    }\n                }\n            }[\"Navbar.useEffect.handleBusinessNameLoaded\"];\n            window.addEventListener(\"businessNameLoaded\", handleBusinessNameLoaded);\n            const interval = setInterval({\n                \"Navbar.useEffect.interval\": ()=>{\n                    if (checkBusinessName()) {\n                        clearInterval(interval);\n                    }\n                }\n            }[\"Navbar.useEffect.interval\"], 100);\n            const timeout = setTimeout({\n                \"Navbar.useEffect.timeout\": ()=>{\n                    clearInterval(interval);\n                }\n            }[\"Navbar.useEffect.timeout\"], 5000);\n            return ({\n                \"Navbar.useEffect\": ()=>{\n                    window.removeEventListener(\"businessNameLoaded\", handleBusinessNameLoaded);\n                    clearInterval(interval);\n                    clearTimeout(timeout);\n                }\n            })[\"Navbar.useEffect\"];\n        }\n    }[\"Navbar.useEffect\"], [\n        metadata,\n        hasMetadata\n    ]);\n    const handleNewConversation = ()=>{\n        localStorage.removeItem(\"userID\");\n        localStorage.removeItem(\"customerName_userId\");\n        localStorage.removeItem(\"BusinessName\");\n        localStorage.removeItem(\"userId\");\n        window.location.reload();\n    };\n    const handleHomeClick = (e)=>{\n        e.preventDefault();\n        window.location.reload();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-white fixed top-0 left-0 right-0 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-between items-center h-15 px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 \",\n                    children: [\n                        metadata.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                src: metadata.image,\n                                alt: \"user-image\",\n                                className: \"h-10 w-10 hover:border hover:border-gray-400 rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\Navbar.jsx\",\n                                lineNumber: 85,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiUserCircle_react_icons_hi2__WEBPACK_IMPORTED_MODULE_6__.HiUserCircle, {\n                            className: \"text-black text-xl w-10 h-10 rounded-full object-cover hover:border hover:border-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\Navbar.jsx\",\n                            lineNumber: 92,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            onClick: handleHomeClick,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-[17px] font-[500] text-black \",\n                                children: businessName\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\Navbar.jsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\Navbar.jsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\Navbar.jsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleNewConversation,\n                    className: \"px-4 py-2 rounded-lg cursor-pointer\",\n                    title: \"Start New Conversation\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit_react_icons_fi__WEBPACK_IMPORTED_MODULE_7__.FiEdit, {\n                        className: \"text-black text-xl\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\Navbar.jsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\Navbar.jsx\",\n                    lineNumber: 100,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\Navbar.jsx\",\n            lineNumber: 81,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\Navbar.jsx\",\n        lineNumber: 80,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Navbar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Navbar.jsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/ChatContext.jsx":
/*!**************************************!*\
  !*** ./src/contexts/ChatContext.jsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatProvider: () => (/* binding */ ChatProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useChatContext: () => (/* binding */ useChatContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useChatContext,ChatProvider,default auto */ \n\n// Create the context\nconst ChatContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\n// Custom hook to use the chat context\nconst useChatContext = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ChatContext);\n    if (!context) {\n        throw new Error('useChatContext must be used within a ChatProvider');\n    }\n    return context;\n};\n// Chat Provider component\nconst ChatProvider = ({ children })=>{\n    console.log(\"ChatProvider\", children);\n    const [metadata, setMetadata] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: null,\n        keywords: null,\n        description: null,\n        image: null\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [settingData, setSettingData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        customerName: null,\n        businessName: null,\n        chatDesignSettings: null,\n        details: null,\n        firstSentence: null,\n        isActive: null,\n        metaData: null,\n        suggestedTopics: null\n    });\n    // Function that update setting response data\n    const updateSettingData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[updateSettingData]\": (newSettingData)=>{\n            setSettingData({\n                \"ChatProvider.useCallback[updateSettingData]\": (prevSettingData)=>({\n                        ...prevSettingData,\n                        ...newSettingData\n                    })\n            }[\"ChatProvider.useCallback[updateSettingData]\"]);\n        }\n    }[\"ChatProvider.useCallback[updateSettingData]\"], []);\n    // Function to update metadata\n    const updateMetadata = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[updateMetadata]\": (newMetadata)=>{\n            setMetadata({\n                \"ChatProvider.useCallback[updateMetadata]\": (prevMetadata)=>({\n                        ...prevMetadata,\n                        ...newMetadata\n                    })\n            }[\"ChatProvider.useCallback[updateMetadata]\"]);\n        }\n    }[\"ChatProvider.useCallback[updateMetadata]\"], []);\n    // Function to clear metadata\n    const clearMetadata = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[clearMetadata]\": ()=>{\n            setMetadata({\n                title: null,\n                keywords: null,\n                description: null,\n                image: null\n            });\n        }\n    }[\"ChatProvider.useCallback[clearMetadata]\"], []);\n    // Function to set loading state\n    const setLoadingState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[setLoadingState]\": (isLoading)=>{\n            setLoading(isLoading);\n        }\n    }[\"ChatProvider.useCallback[setLoadingState]\"], []);\n    // Function to set error state\n    const setErrorState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[setErrorState]\": (errorMessage)=>{\n            setError(errorMessage);\n        }\n    }[\"ChatProvider.useCallback[setErrorState]\"], []);\n    // Function to clear error\n    const clearError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[clearError]\": ()=>{\n            setError(null);\n        }\n    }[\"ChatProvider.useCallback[clearError]\"], []);\n    const value = {\n        metadata,\n        loading,\n        error,\n        updateMetadata,\n        clearMetadata,\n        setLoadingState,\n        setErrorState,\n        clearError,\n        settingData,\n        updateSettingData,\n        // Helper getters for easy access\n        getTitle: ()=>metadata.title,\n        getKeywords: ()=>metadata.keywords,\n        getDescription: ()=>metadata.description,\n        getImage: ()=>metadata.image,\n        hasMetadata: ()=>metadata.title !== null || metadata.keywords !== null || metadata.description !== null\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\contexts\\\\ChatContext.jsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatContext);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/ChatContext.jsx\n");

/***/ }),

/***/ "(ssr)/./src/utils/config.js":
/*!*****************************!*\
  !*** ./src/utils/config.js ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_ENDPOINTS: () => (/* binding */ API_ENDPOINTS),\n/* harmony export */   EXTERNAL_API_BASE_URL: () => (/* binding */ EXTERNAL_API_BASE_URL),\n/* harmony export */   EXTERNAL_API_ENDPOINTS: () => (/* binding */ EXTERNAL_API_ENDPOINTS),\n/* harmony export */   getExternalApiUrl: () => (/* binding */ getExternalApiUrl),\n/* harmony export */   getSSEUrl: () => (/* binding */ getSSEUrl)\n/* harmony export */ });\nconst EXTERNAL_API_BASE_URL = \"https://api-develop.driply.me/\" || 0;\nconst getExternalApiUrl = ()=>{\n    const url = EXTERNAL_API_BASE_URL;\n    return url.endsWith('/') ? url : `${url}/`;\n};\nconst API_ENDPOINTS = {\n    CHAT_INIT: '/api/chat/init',\n    CHAT_SEND: '/api/chat/send',\n    CHAT_MESSAGES: '/api/chat/messages',\n    CHAT_SETTINGS: '/api/chat/settings'\n};\nconst getSSEUrl = (userId)=>{\n    return `${getExternalApiUrl()}chat-pusher/chat?stream=${userId}`;\n};\nconst EXTERNAL_API_ENDPOINTS = {\n    CHAT_INIT: `${getExternalApiUrl()}api/chat/init`,\n    CHAT_SEND: `${getExternalApiUrl()}api/chat/message`,\n    CHAT_MESSAGES: `${getExternalApiUrl()}api/chat/messages`,\n    CHAT_SETTINGS: `${getExternalApiUrl()}api/chat/settings`\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/config.js\n");

/***/ }),

/***/ "(ssr)/./src/utils/constants.js":
/*!********************************!*\
  !*** ./src/utils/constants.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APP_NAME: () => (/* binding */ APP_NAME),\n/* harmony export */   SUGGESTION_CARDS: () => (/* binding */ SUGGESTION_CARDS)\n/* harmony export */ });\nconst APP_NAME = 'DRIPLY';\nconst SUGGESTION_CARDS = [\n    {\n        title: \"Design a schema\"\n    },\n    {\n        title: \"Explain airplane\"\n    },\n    {\n        title: \"Create a work plan\"\n    },\n    {\n        title: \"Write a recipe\"\n    },\n    {\n        title: \"Plan a budget\"\n    },\n    {\n        title: \"Learn JavaScript \"\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvdXRpbHMvY29uc3RhbnRzLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQ08sTUFBTUEsV0FBVyxTQUFRO0FBR3pCLE1BQU1DLG1CQUFtQjtJQUM5QjtRQUNFQyxPQUFPO0lBQ1Q7SUFDQTtRQUNFQSxPQUFPO0lBQ1Q7SUFDQTtRQUNFQSxPQUFPO0lBQ1Q7SUFDQTtRQUNFQSxPQUFPO0lBQ1Q7SUFDQTtRQUNFQSxPQUFPO0lBQ1Q7SUFDQTtRQUNFQSxPQUFPO0lBQ1Q7Q0FDRCIsInNvdXJjZXMiOlsiRDpcXExhcHRvcCBkYXRhXFxEUklQTFktQ0hBVFxcY2hhdC1uZXh0XFxzcmNcXHV0aWxzXFxjb25zdGFudHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXHJcbmV4cG9ydCBjb25zdCBBUFBfTkFNRSA9ICdEUklQTFknXHJcblxyXG5cclxuZXhwb3J0IGNvbnN0IFNVR0dFU1RJT05fQ0FSRFMgPSBbXHJcbiAge1xyXG4gICAgdGl0bGU6IFwiRGVzaWduIGEgc2NoZW1hXCIsXHJcbiAgfSxcclxuICB7XHJcbiAgICB0aXRsZTogXCJFeHBsYWluIGFpcnBsYW5lXCIsXHJcbiAgfSxcclxuICB7XHJcbiAgICB0aXRsZTogXCJDcmVhdGUgYSB3b3JrIHBsYW5cIixcclxuICB9LFxyXG4gIHtcclxuICAgIHRpdGxlOiBcIldyaXRlIGEgcmVjaXBlXCIsXHJcbiAgfSxcclxuICB7XHJcbiAgICB0aXRsZTogXCJQbGFuIGEgYnVkZ2V0XCIsXHJcbiAgfSxcclxuICB7XHJcbiAgICB0aXRsZTogXCJMZWFybiBKYXZhU2NyaXB0IFwiLFxyXG4gIH1cclxuXVxyXG5cclxuXHJcbiJdLCJuYW1lcyI6WyJBUFBfTkFNRSIsIlNVR0dFU1RJT05fQ0FSRFMiLCJ0aXRsZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/constants.js\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "node:os":
/*!**************************!*\
  !*** external "node:os" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:os");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:tty":
/*!***************************!*\
  !*** external "node:tty" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:tty");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/axios","vendor-chunks/asynckit","vendor-chunks/math-intrinsics","vendor-chunks/react-icons","vendor-chunks/es-errors","vendor-chunks/form-data","vendor-chunks/call-bind-apply-helpers","vendor-chunks/debug","vendor-chunks/get-proto","vendor-chunks/styled-jsx","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/follow-redirects","vendor-chunks/supports-color","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/get-intrinsic","vendor-chunks/es-set-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/delayed-stream","vendor-chunks/combined-stream"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Bslug%5D%2Fpage&page=%2F%5Bslug%5D%2Fpage&appPaths=%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2F%5Bslug%5D%2Fpage.js&appDir=D%3A%5CLaptop%20data%5CDRIPLY-CHAT%5Cchat-next%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CLaptop%20data%5CDRIPLY-CHAT%5Cchat-next&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();