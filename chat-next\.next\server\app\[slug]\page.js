/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/[slug]/page";
exports.ids = ["app/[slug]/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Bslug%5D%2Fpage&page=%2F%5Bslug%5D%2Fpage&appPaths=%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2F%5Bslug%5D%2Fpage.js&appDir=D%3A%5CLaptop%20data%5CDRIPLY-CHAT%5Cchat-next%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CLaptop%20data%5CDRIPLY-CHAT%5Cchat-next&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Bslug%5D%2Fpage&page=%2F%5Bslug%5D%2Fpage&appPaths=%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2F%5Bslug%5D%2Fpage.js&appDir=D%3A%5CLaptop%20data%5CDRIPLY-CHAT%5Cchat-next%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CLaptop%20data%5CDRIPLY-CHAT%5Cchat-next&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.js */ \"(rsc)/./src/app/layout.js\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[slug]/page.js */ \"(rsc)/./src/app/[slug]/page.js\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '[slug]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\app\\\\[slug]\\\\page.js\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\app\\\\layout.js\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\app\\\\[slug]\\\\page.js\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/[slug]/page\",\n        pathname: \"/[slug]\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Bslug%5D%2Fpage&page=%2F%5Bslug%5D%2Fpage&appPaths=%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2F%5Bslug%5D%2Fpage.js&appDir=D%3A%5CLaptop%20data%5CDRIPLY-CHAT%5Cchat-next%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CLaptop%20data%5CDRIPLY-CHAT%5Cchat-next&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Wix_Madefor_Text%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-wix-madefor-text%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22wixMadeforText%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CClientWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Wix_Madefor_Text%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-wix-madefor-text%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22wixMadeforText%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CClientWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ClientWrapper.jsx */ \"(rsc)/./src/components/ClientWrapper.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Wix_Madefor_Text%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-wix-madefor-text%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22wixMadeforText%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CClientWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CChatInterfaceWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CChatInterfaceWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ChatInterfaceWrapper.jsx */ \"(rsc)/./src/components/ChatInterfaceWrapper.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNMYXB0b3AlMjBkYXRhJTVDJTVDRFJJUExZLUNIQVQlNUMlNUNjaGF0LW5leHQlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDQ2hhdEludGVyZmFjZVdyYXBwZXIuanN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOExBQW9KIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiRDpcXFxcTGFwdG9wIGRhdGFcXFxcRFJJUExZLUNIQVRcXFxcY2hhdC1uZXh0XFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXENoYXRJbnRlcmZhY2VXcmFwcGVyLmpzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CChatInterfaceWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxMYXB0b3AgZGF0YVxcRFJJUExZLUNIQVRcXGNoYXQtbmV4dFxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/[slug]/page.js":
/*!********************************!*\
  !*** ./src/app/[slug]/page.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   generateMetadata: () => (/* binding */ generateMetadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ChatInterfaceWrapper__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/ChatInterfaceWrapper */ \"(rsc)/./src/components/ChatInterfaceWrapper.jsx\");\n\n\n\n// Dynamic metadata generation using your API response data\nasync function generateMetadata({ params }) {\n    const { slug } = await params;\n    // Default fallback metadata\n    let title = `Chat with ${slug} - Driply`;\n    let description = `Start a conversation with ${slug} on Driply platform`;\n    let keywords = [\n        'chat',\n        slug,\n        'driply',\n        'ai',\n        'conversation'\n    ];\n    let businessName = 'Driply';\n    let image = '/og-image.jpg';\n    try {\n        // Fetch the same data that your ChatContext uses\n        const response = await fetch(`${\"https://api-develop.driply.me/\" || 0}/api/chat/settings?customerName=${slug}`, {\n            method: 'GET',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            // Cache for better performance\n            next: {\n                revalidate: 300\n            }\n        });\n        if (response.ok) {\n            const data = await response.json();\n            console.log('[Metadata] API Response:', data);\n            if (data && data.customerName) {\n                // Extract data from your API response structure\n                businessName = data.businessName || 'Driply';\n                const metaData = data.metaData || {};\n                // Use the metadata from your API response\n                title = metaData.title || `${businessName} Chat - ${slug}`;\n                description = metaData.description || `Chat with ${businessName} - Powered by Driply`;\n                // Handle keywords - convert string to array if needed\n                if (metaData.keywords) {\n                    keywords = typeof metaData.keywords === 'string' ? metaData.keywords.split(',').map((k)=>k.trim()) : Array.isArray(metaData.keywords) ? metaData.keywords : [\n                        metaData.keywords\n                    ];\n                } else {\n                    keywords = [\n                        'chat',\n                        slug,\n                        businessName.toLowerCase(),\n                        'driply',\n                        'ai',\n                        'conversation'\n                    ];\n                }\n                // Use custom image if provided\n                if (metaData.image && metaData.image.trim()) {\n                    image = metaData.image;\n                }\n                console.log('[Metadata] Using dynamic metadata:', {\n                    title,\n                    description,\n                    keywords,\n                    businessName,\n                    image\n                });\n            }\n        } else {\n            console.warn('[Metadata] API request failed, using fallback metadata');\n        }\n    } catch (error) {\n        console.warn('[Metadata] Error fetching dynamic metadata:', error.message);\n    // Will use fallback metadata\n    }\n    // Return the complete metadata object\n    return {\n        title: title,\n        description: description,\n        keywords: keywords,\n        openGraph: {\n            title: title,\n            description: description,\n            type: 'website',\n            url: `https://yourdomain.com/${slug}`,\n            siteName: businessName,\n            images: [\n                {\n                    url: image,\n                    width: 1200,\n                    height: 630,\n                    alt: `${businessName} Chat - ${slug}`\n                }\n            ],\n            locale: 'en_US'\n        },\n        twitter: {\n            card: 'summary_large_image',\n            title: title,\n            description: description,\n            images: [\n                image\n            ]\n        },\n        robots: {\n            index: true,\n            follow: true\n        },\n        authors: [\n            {\n                name: businessName\n            }\n        ],\n        creator: businessName,\n        publisher: 'Driply'\n    };\n}\n// Server component - no \"use client\" directive\nconst page = async ({ params, searchParams })=>{\n    const { slug } = await params;\n    const queryParams = await searchParams;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatInterfaceWrapper__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            slug: slug,\n            query: queryParams\n        }, void 0, false, {\n            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\app\\\\[slug]\\\\page.js\",\n            lineNumber: 110,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\app\\\\[slug]\\\\page.js\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (page);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/[slug]/page.js\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"f57a3c65339e\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcTGFwdG9wIGRhdGFcXERSSVBMWS1DSEFUXFxjaGF0LW5leHRcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImY1N2EzYzY1MzM5ZVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.js":
/*!***************************!*\
  !*** ./src/app/layout.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.js\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.js\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_js_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.js\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.js\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_js_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Wix_Madefor_Text_arguments_variable_font_wix_madefor_text_subsets_latin_variableName_wixMadeforText___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.js\",\"import\":\"Wix_Madefor_Text\",\"arguments\":[{\"variable\":\"--font-wix-madefor-text\",\"subsets\":[\"latin\"]}],\"variableName\":\"wixMadeforText\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.js\\\",\\\"import\\\":\\\"Wix_Madefor_Text\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-wix-madefor-text\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"wixMadeforText\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Wix_Madefor_Text_arguments_variable_font_wix_madefor_text_subsets_latin_variableName_wixMadeforText___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_js_import_Wix_Madefor_Text_arguments_variable_font_wix_madefor_text_subsets_latin_variableName_wixMadeforText___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_ClientWrapper__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/ClientWrapper */ \"(rsc)/./src/components/ClientWrapper.jsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: {\n        default: \"Driply - AI Chat Platform\",\n        template: \"%s | Driply\"\n    },\n    description: \"Driply is an advanced AI chat platform that provides intelligent conversations and seamless user experience.\",\n    keywords: [\n        \"AI\",\n        \"chat\",\n        \"conversation\",\n        \"driply\",\n        \"artificial intelligence\"\n    ],\n    authors: [\n        {\n            name: \"Driply Team\"\n        }\n    ],\n    creator: \"Driply\",\n    publisher: \"Driply\",\n    robots: {\n        index: true,\n        follow: true\n    },\n    openGraph: {\n        type: \"website\",\n        locale: \"en_US\",\n        url: \"https://yourdomain.com\",\n        siteName: \"Driply\",\n        title: \"Driply - AI Chat Platform\",\n        description: \"Driply is an advanced AI chat platform that provides intelligent conversations and seamless user experience.\",\n        images: [\n            {\n                url: \"/og-image.jpg\",\n                width: 1200,\n                height: 630,\n                alt: \"Driply AI Chat Platform\"\n            }\n        ]\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"Driply - AI Chat Platform\",\n        description: \"Driply is an advanced AI chat platform that provides intelligent conversations and seamless user experience.\",\n        images: [\n            \"/og-image.jpg\"\n        ]\n    },\n    viewport: \"width=device-width, initial-scale=1\",\n    themeColor: \"#000000\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_js_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default().variable)} ${(next_font_google_target_css_path_src_app_layout_js_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default().variable)} ${(next_font_google_target_css_path_src_app_layout_js_import_Wix_Madefor_Text_arguments_variable_font_wix_madefor_text_subsets_latin_variableName_wixMadeforText___WEBPACK_IMPORTED_MODULE_5___default().variable)} antialiased`,\n            suppressHydrationWarning: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientWrapper__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\app\\\\layout.js\",\n                lineNumber: 71,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\app\\\\layout.js\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\app\\\\layout.js\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.js\n");

/***/ }),

/***/ "(rsc)/./src/components/ChatInterfaceWrapper.jsx":
/*!*************************************************!*\
  !*** ./src/components/ChatInterfaceWrapper.jsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterfaceWrapper.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Laptop data\\DRIPLY-CHAT\\chat-next\\src\\components\\ChatInterfaceWrapper.jsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/ClientWrapper.jsx":
/*!******************************************!*\
  !*** ./src/components/ClientWrapper.jsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ClientWrapper.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Laptop data\\DRIPLY-CHAT\\chat-next\\src\\components\\ClientWrapper.jsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNMYXB0b3AlMjBkYXRhJTVDJTVDRFJJUExZLUNIQVQlNUMlNUNjaGF0LW5leHQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjbGllbnQtcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDTGFwdG9wJTIwZGF0YSU1QyU1Q0RSSVBMWS1DSEFUJTVDJTVDY2hhdC1uZXh0JTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDY2xpZW50LXNlZ21lbnQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q0xhcHRvcCUyMGRhdGElNUMlNUNEUklQTFktQ0hBVCU1QyU1Q2NoYXQtbmV4dCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNMYXB0b3AlMjBkYXRhJTVDJTVDRFJJUExZLUNIQVQlNUMlNUNjaGF0LW5leHQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNodHRwLWFjY2Vzcy1mYWxsYmFjayU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNMYXB0b3AlMjBkYXRhJTVDJTVDRFJJUExZLUNIQVQlNUMlNUNjaGF0LW5leHQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNsYXlvdXQtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNMYXB0b3AlMjBkYXRhJTVDJTVDRFJJUExZLUNIQVQlNUMlNUNjaGF0LW5leHQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q2FzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNMYXB0b3AlMjBkYXRhJTVDJTVDRFJJUExZLUNIQVQlNUMlNUNjaGF0LW5leHQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q21ldGFkYXRhLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNMYXB0b3AlMjBkYXRhJTVDJTVDRFJJUExZLUNIQVQlNUMlNUNjaGF0LW5leHQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvT0FBMEk7QUFDMUk7QUFDQSwwT0FBNkk7QUFDN0k7QUFDQSwwT0FBNkk7QUFDN0k7QUFDQSxvUkFBbUs7QUFDbks7QUFDQSx3T0FBNEk7QUFDNUk7QUFDQSw0UEFBdUo7QUFDdko7QUFDQSxrUUFBMEo7QUFDMUo7QUFDQSxzUUFBMkoiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXExhcHRvcCBkYXRhXFxcXERSSVBMWS1DSEFUXFxcXGNoYXQtbmV4dFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGNsaWVudC1wYWdlLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxMYXB0b3AgZGF0YVxcXFxEUklQTFktQ0hBVFxcXFxjaGF0LW5leHRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxjbGllbnQtc2VnbWVudC5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcTGFwdG9wIGRhdGFcXFxcRFJJUExZLUNIQVRcXFxcY2hhdC1uZXh0XFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXExhcHRvcCBkYXRhXFxcXERSSVBMWS1DSEFUXFxcXGNoYXQtbmV4dFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGh0dHAtYWNjZXNzLWZhbGxiYWNrXFxcXGVycm9yLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxMYXB0b3AgZGF0YVxcXFxEUklQTFktQ0hBVFxcXFxjaGF0LW5leHRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxsYXlvdXQtcm91dGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxMYXB0b3AgZGF0YVxcXFxEUklQTFktQ0hBVFxcXFxjaGF0LW5leHRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxtZXRhZGF0YVxcXFxhc3luYy1tZXRhZGF0YS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcTGFwdG9wIGRhdGFcXFxcRFJJUExZLUNIQVRcXFxcY2hhdC1uZXh0XFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbWV0YWRhdGFcXFxcbWV0YWRhdGEtYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXExhcHRvcCBkYXRhXFxcXERSSVBMWS1DSEFUXFxcXGNoYXQtbmV4dFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXHJlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Wix_Madefor_Text%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-wix-madefor-text%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22wixMadeforText%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CClientWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Wix_Madefor_Text%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-wix-madefor-text%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22wixMadeforText%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CClientWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ClientWrapper.jsx */ \"(ssr)/./src/components/ClientWrapper.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Wix_Madefor_Text%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-wix-madefor-text%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22wixMadeforText%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CClientWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CChatInterfaceWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CChatInterfaceWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ChatInterfaceWrapper.jsx */ \"(ssr)/./src/components/ChatInterfaceWrapper.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNMYXB0b3AlMjBkYXRhJTVDJTVDRFJJUExZLUNIQVQlNUMlNUNjaGF0LW5leHQlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDQ2hhdEludGVyZmFjZVdyYXBwZXIuanN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOExBQW9KIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiRDpcXFxcTGFwdG9wIGRhdGFcXFxcRFJJUExZLUNIQVRcXFxcY2hhdC1uZXh0XFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXENoYXRJbnRlcmZhY2VXcmFwcGVyLmpzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CChatInterfaceWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/assets/images/logo.png":
/*!************************************!*\
  !*** ./src/assets/images/logo.png ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/logo.425de58c.png\",\"height\":188,\"width\":188,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Flogo.425de58c.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXNzZXRzL2ltYWdlcy9sb2dvLnBuZyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsQ0FBQyw0TEFBNEwiLCJzb3VyY2VzIjpbIkQ6XFxMYXB0b3AgZGF0YVxcRFJJUExZLUNIQVRcXGNoYXQtbmV4dFxcc3JjXFxhc3NldHNcXGltYWdlc1xcbG9nby5wbmciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2xvZ28uNDI1ZGU1OGMucG5nXCIsXCJoZWlnaHRcIjoxODgsXCJ3aWR0aFwiOjE4OCxcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZsb2dvLjQyNWRlNThjLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo4fTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/assets/images/logo.png\n");

/***/ }),

/***/ "(ssr)/./src/components/ChatInterface.jsx":
/*!******************************************!*\
  !*** ./src/components/ChatInterface.jsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(ssr)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/constants */ \"(ssr)/./src/utils/constants.js\");\n/* harmony import */ var _utils_config__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/config */ \"(ssr)/./src/utils/config.js\");\n/* harmony import */ var _barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FaArrowUp,FaChevronLeft,FaChevronRight!=!react-icons/fa6 */ \"(ssr)/./node_modules/react-icons/fa6/index.mjs\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../contexts/ChatContext */ \"(ssr)/./src/contexts/ChatContext.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nconst ChatInterface = ({ slug, query })=>{\n    // Context API\n    const { metadata, updateMetadata, setLoadingState, setErrorState, clearError, getCustomerName, getBusinessName, hasMetadata, loading: contextLoading, error: contextError } = (0,_contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__.useChatContext)();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [currentSlide, setCurrentSlide] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [touchStart, setTouchStart] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [touchEnd, setTouchEnd] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isTablet, setIsTablet] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showInitialUI, setShowInitialUI] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isBotTyping, setIsBotTyping] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isBotThinking, setIsBotThinking] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [sseConnection, setSseConnection] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const carouselRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const desktopContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const mobileContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const tabletContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // Detect iOS for targeted fixes\n    const isIOS = ()=>{\n        if (false) {}\n        return false;\n    };\n    const username = slug;\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (username && \"undefined\" !== \"undefined\") {}\n        }\n    }[\"ChatInterface.useEffect\"], [\n        username\n    ]);\n    const fetchCustomer = async (customerName)=>{\n        try {\n            setLoadingState(true);\n            clearError();\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(`${_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_SETTINGS}?customerName=${customerName}`);\n            if (response.data && response.data.customerName) {\n                const metadataFromResponse = response.data.metaData;\n                console.log('Storing metadata in context:', metadataFromResponse);\n                updateMetadata(metadataFromResponse);\n                // Keep existing localStorage for backward compatibility\n                localStorage.setItem(\"customerName_userId\", response.data.customerName);\n                localStorage.setItem(\"BusinessName\", response.data.businessName);\n                window.dispatchEvent(new CustomEvent('businessNameLoaded', {\n                    detail: {\n                        businessName: response.data.businessName\n                    }\n                }));\n                const existingUserId = localStorage.getItem(\"userID\");\n                if (existingUserId) {\n                    await fetchExistingMessages(existingUserId);\n                } else {\n                    await initializeChatSession();\n                }\n            } else {\n                await initializeChatSession();\n            }\n        } catch (error) {\n            console.error('Error fetching customer data:', error);\n            setErrorState('Failed to load customer settings');\n            // Fallback metadata\n            updateMetadata({\n                customerName: customerName,\n                businessName: \"Driply\"\n            });\n            localStorage.setItem(\"BusinessName\", \"Driply\");\n            window.dispatchEvent(new CustomEvent('businessNameLoaded', {\n                detail: {\n                    businessName: \"Driply\"\n                }\n            }));\n            await initializeChatSession();\n        } finally{\n            setLoadingState(false);\n        }\n    };\n    const fetchExistingMessages = async (userId)=>{\n        console.log(\"metadata : \", metadata);\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(`${_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_MESSAGES}?userId=${userId}`);\n            if (response.data && response.data.length > 0) {\n                setMessages(response.data.map((msg)=>({\n                        text: msg.message,\n                        timestamp: new Date(msg.createdAt).getTime(),\n                        type: msg.type,\n                        source: msg.source\n                    })));\n            }\n            connectToSSE(userId);\n        } catch (error) {\n            await initializeChatSession();\n        }\n    };\n    const initializeChatSession = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_INIT, {\n                customerName: username\n            });\n            if (response.data && response.data.userId) {\n                localStorage.setItem(\"userId\", response.data.userId);\n                localStorage.setItem(\"userID\", response.data._id);\n                await fetchExistingMessages(response.data._id);\n            }\n        } catch (error) {\n            if (!localStorage.getItem(\"BusinessName\")) {\n                localStorage.setItem(\"BusinessName\", \"Driply\");\n                window.dispatchEvent(new CustomEvent('businessNameLoaded', {\n                    detail: {\n                        businessName: \"Driply\"\n                    }\n                }));\n            }\n        }\n    };\n    const connectToSSE = (userId)=>{\n        try {\n            if (sseConnection) {\n                sseConnection.close();\n            }\n            const sseUrl = (0,_utils_config__WEBPACK_IMPORTED_MODULE_4__.getSSEUrl)(userId);\n            const eventSource = new EventSource(sseUrl);\n            setSseConnection(eventSource);\n            eventSource.addEventListener(\"message\", ({ data })=>{\n                try {\n                    const contents = JSON.parse(data);\n                    handleSSEMessage(contents);\n                } catch (error) {\n                // Handle parsing error silently\n                }\n            });\n            eventSource.addEventListener(\"error\", ()=>{\n            // Handle connection error silently\n            });\n        } catch (error) {\n        // Handle SSE connection error silently\n        }\n    };\n    const handleSSEMessage = (data)=>{\n        const subtype = data.subType || data.subtype || data.type || \"UNKNOWN\";\n        const content = data.content || \"\";\n        const message = data.message || data.text || \"\";\n        if ([\n            \"TYPING\",\n            \"THINKING\",\n            \"BEHAVIOUR_MESSAGE\"\n        ].includes(subtype)) {\n            switch(subtype){\n                case \"TYPING\":\n                    setIsBotTyping(true);\n                    setIsBotThinking(false);\n                    break;\n                case \"THINKING\":\n                    setIsBotThinking(true);\n                    setIsBotTyping(false);\n                    break;\n                case \"BEHAVIOUR_MESSAGE\":\n                    setIsBotTyping(false);\n                    setIsBotThinking(false);\n                    break;\n            }\n            return;\n        }\n        const displayText = message || content;\n        if (!displayText) return;\n        const displayType = [\n            \"TEXT\",\n            \"MESSAGE\",\n            \"DATA_MESSAGE\"\n        ].includes(subtype) ? subtype : \"UNKNOWN\";\n        setMessages((prev)=>[\n                ...prev,\n                {\n                    text: displayText,\n                    timestamp: Date.now(),\n                    type: displayType,\n                    source: \"BOT\"\n                }\n            ]);\n        setIsBotTyping(false);\n        setIsBotThinking(false);\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (message.trim()) {\n            const userMessage = message.trim();\n            setMessages((prev)=>[\n                    ...prev,\n                    {\n                        text: userMessage,\n                        timestamp: Date.now(),\n                        type: \"TEXT\",\n                        source: \"USER\"\n                    }\n                ]);\n            setMessage(\"\");\n            setIsTyping(false);\n            // Trigger scroll to show the new message immediately\n            setTimeout(()=>scrollToOptimalPosition(), 50);\n            // Show loading dots immediately after sending message\n            setIsBotThinking(true);\n            setIsBotTyping(false);\n            // Reset textarea\n            if (inputRef.current) {\n                inputRef.current.style.height = \"104px\";\n                inputRef.current.scrollTop = 0;\n                inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n                inputRef.current.classList.add(\"reset-height\");\n            }\n            try {\n                const userId = localStorage.getItem(\"userID\");\n                if (userId) {\n                    await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_SEND, {\n                        userId: userId,\n                        message: userMessage,\n                        \"typs\": \"TEXT\",\n                        \"source\": \"USER\",\n                        \"isTest\": query.isTest === '1' ? true : false\n                    });\n                }\n            } catch (error) {\n                // Hide loading dots on error\n                setIsBotThinking(false);\n                setIsBotTyping(false);\n                setMessages((prev)=>[\n                        ...prev,\n                        {\n                            text: \"Sorry, there was an error sending your message. Please try again.\",\n                            timestamp: Date.now(),\n                            type: \"TEXT\",\n                            source: \"BOT\"\n                        }\n                    ]);\n            }\n        }\n    };\n    const handleTextareaResize = (textarea)=>{\n        if (textarea) {\n            textarea.style.height = \"104px\";\n            const scrollHeight = textarea.scrollHeight;\n            if (isIOS()) {\n                if (scrollHeight > 48) {\n                    textarea.style.overflowY = \"auto\";\n                } else {\n                    textarea.style.overflowY = \"hidden\";\n                }\n            } else {\n                const newHeight = Math.min(250, Math.max(104, scrollHeight));\n                textarea.style.height = `${newHeight}px`;\n                textarea.style.overflowY = scrollHeight > newHeight ? \"auto\" : \"hidden\";\n            }\n        }\n    };\n    const handleInputChange = (e)=>{\n        const value = e.target.value;\n        setMessage(value);\n        handleTextareaResize(e.target);\n        if (messages.length === 0) {\n            setIsTyping(value.length > 0);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length > 0 && inputRef.current && !message) {\n                const timer = setTimeout({\n                    \"ChatInterface.useEffect.timer\": ()=>{\n                        if (inputRef.current) {\n                            inputRef.current.style.height = \"104px\";\n                            inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n                            inputRef.current.classList.add(\"reset-height\");\n                        }\n                    }\n                }[\"ChatInterface.useEffect.timer\"], 50);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                })[\"ChatInterface.useEffect\"];\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages,\n        message\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length > 0) {\n                scrollToOptimalPosition();\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages,\n        isBotTyping,\n        isBotThinking\n    ]);\n    const scrollToOptimalPosition = ()=>{\n        setTimeout(()=>{\n            // Get the appropriate container based on screen size\n            const screenWidth = window.innerWidth;\n            let container;\n            if (screenWidth >= 1024) {\n                container = desktopContainerRef.current;\n            } else if (screenWidth >= 768) {\n                container = tabletContainerRef.current;\n            } else {\n                container = mobileContainerRef.current;\n            }\n            if (!container) return;\n            // ChatGPT-style positioning: Show latest messages in optimal reading position\n            const containerHeight = container.clientHeight;\n            const scrollHeight = container.scrollHeight;\n            // If content fits in viewport, don't scroll\n            if (scrollHeight <= containerHeight) {\n                return;\n            }\n            // Find the last message element to position it optimally\n            const messageElements = container.querySelectorAll('.message-item');\n            if (messageElements.length > 0) {\n                const lastMessage = messageElements[messageElements.length - 1];\n                const messageRect = lastMessage.getBoundingClientRect();\n                const containerRect = container.getBoundingClientRect();\n                // Calculate where we want the message to appear (25% from top of viewport)\n                const optimalPosition = containerRect.top + containerHeight * 0.25;\n                const currentMessageTop = messageRect.top;\n                // Calculate scroll adjustment needed\n                const scrollAdjustment = currentMessageTop - optimalPosition;\n                const newScrollTop = container.scrollTop + scrollAdjustment;\n                container.scrollTo({\n                    top: Math.max(0, newScrollTop),\n                    behavior: 'smooth'\n                });\n            } else {\n                // Fallback: scroll to bottom with some padding\n                container.scrollTo({\n                    top: scrollHeight - containerHeight + 100,\n                    behavior: 'smooth'\n                });\n            }\n        }, 150);\n    };\n    const renderMessages = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                messages.map((msg, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `message-item flex ${msg.source === 'USER' ? 'justify-end' : 'justify-start'}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `text-[16px] font-[400] rounded-3xl max-w-xs lg:max-w-lg break-words hyphens-auto ${msg.source === 'USER' ? 'bg-gray-100' : 'bg-white'} ${isMobile ? 'rounded-[15px] px-3 py-2' : 'px-4 py-3'}`,\n                            style: {\n                                overflowWrap: \"break-word\",\n                                wordBreak: \"break-word\",\n                                maxWidth: isMobile ? \"90%\" : undefined\n                            },\n                            children: msg.text\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 402,\n                            columnNumber: 13\n                        }, undefined)\n                    }, `${msg.timestamp}-${index}`, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 398,\n                        columnNumber: 11\n                    }, undefined)),\n                (isBotTyping || isBotThinking) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"message-item flex justify-start\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `bg-white ${isMobile ? 'px-3 py-2 rounded-[15px]' : 'px-4 py-3 rounded-3xl'}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-black rounded-full loading-dot\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 424,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-black rounded-full loading-dot\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 425,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-black rounded-full loading-dot\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 426,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 423,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 422,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                    lineNumber: 421,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-20\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                    lineNumber: 433,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n            lineNumber: 396,\n            columnNumber: 7\n        }, undefined);\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSubmit(e);\n        }\n    };\n    const handleSuggestionClick = async (cardTitle)=>{\n        setMessages((prev)=>[\n                ...prev,\n                {\n                    text: cardTitle,\n                    timestamp: Date.now(),\n                    type: \"TEXT\",\n                    source: \"USER\"\n                }\n            ]);\n        setMessage(\"\");\n        setIsTyping(false);\n        // Trigger scroll to show the new message immediately\n        setTimeout(()=>scrollToOptimalPosition(), 50);\n        // Show loading dots immediately after sending message\n        setIsBotThinking(true);\n        setIsBotTyping(false);\n        if (inputRef.current) {\n            inputRef.current.style.height = \"104px\";\n            inputRef.current.scrollTop = 0;\n            inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n            inputRef.current.classList.add(\"reset-height\");\n        }\n        try {\n            const userId = localStorage.getItem(\"userID\");\n            if (userId) {\n                await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_SEND, {\n                    userId: userId,\n                    message: cardTitle,\n                    customerName: username\n                });\n            }\n        } catch (error) {\n            // Hide loading dots on error\n            setIsBotThinking(false);\n            setIsBotTyping(false);\n            setMessages((prev)=>[\n                    ...prev,\n                    {\n                        text: \"Sorry, there was an error sending your message. Please try again.\",\n                        timestamp: Date.now(),\n                        type: \"TEXT\",\n                        source: \"BOT\"\n                    }\n                ]);\n        }\n    };\n    const getMaxSlides = ()=>{\n        if (isMobile) return _utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.length;\n        return Math.ceil(_utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.length / 2);\n    };\n    const nextSlide = ()=>{\n        if (isMobile) {\n            setCurrentSlide((prev)=>prev < _utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.length - 1 ? prev + 1 : prev);\n        } else {\n            const maxSlides = getMaxSlides();\n            setCurrentSlide((prev)=>(prev + 1) % maxSlides);\n        }\n    };\n    const prevSlide = ()=>{\n        if (isMobile) {\n            setCurrentSlide((prev)=>prev > 0 ? prev - 1 : prev);\n        } else {\n            const maxSlides = getMaxSlides();\n            setCurrentSlide((prev)=>(prev - 1 + maxSlides) % maxSlides);\n        }\n    };\n    const handleNextSlide = ()=>{\n        nextSlide();\n    };\n    const handlePrevSlide = ()=>{\n        prevSlide();\n    };\n    const handleTouchStart = (e)=>{\n        setTouchStart(e.targetTouches[0].clientX);\n    };\n    const handleTouchMove = (e)=>{\n        setTouchEnd(e.targetTouches[0].clientX);\n    };\n    const handleTouchEnd = ()=>{\n        if (!touchStart || !touchEnd) return;\n        const swipeDistance = touchStart - touchEnd;\n        const minSwipeDistance = 50;\n        if (swipeDistance > minSwipeDistance) {\n            nextSlide();\n        }\n        if (swipeDistance < -minSwipeDistance) {\n            prevSlide();\n        }\n        setTouchStart(0);\n        setTouchEnd(0);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length === 0) {\n                setShowInitialUI(false);\n                const timer = setTimeout({\n                    \"ChatInterface.useEffect.timer\": ()=>setShowInitialUI(true)\n                }[\"ChatInterface.useEffect.timer\"], 60);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                })[\"ChatInterface.useEffect\"];\n            } else {\n                setShowInitialUI(false);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages.length\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const checkScreenSize = {\n                \"ChatInterface.useEffect.checkScreenSize\": ()=>{\n                    if (false) {}\n                }\n            }[\"ChatInterface.useEffect.checkScreenSize\"];\n            checkScreenSize();\n            const handleResize = {\n                \"ChatInterface.useEffect.handleResize\": ()=>{\n                    setCurrentSlide(0);\n                    checkScreenSize();\n                }\n            }[\"ChatInterface.useEffect.handleResize\"];\n            if (false) {}\n        }\n    }[\"ChatInterface.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (inputRef.current && messages.length === 0) {\n                const shouldAutoFocus = window.innerWidth >= 768;\n                if (shouldAutoFocus) {\n                    const timer = setTimeout({\n                        \"ChatInterface.useEffect.timer\": ()=>{\n                            inputRef.current?.focus();\n                        }\n                    }[\"ChatInterface.useEffect.timer\"], 100);\n                    return ({\n                        \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                    })[\"ChatInterface.useEffect\"];\n                }\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages.length\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            return ({\n                \"ChatInterface.useEffect\": ()=>{\n                    if (sseConnection) {\n                        sseConnection.close();\n                    }\n                }\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], [\n        sseConnection\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"bg-white flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"hidden lg:flex flex-1 flex-col px-4 \",\n                children: messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"flex flex-1 items-center justify-center min-h-[calc(100vh-64px)] mt-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"flex flex-col items-center w-[768px] justify-center mx-auto \",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    transitionDelay: showInitialUI ? \"40ms\" : \"0ms\"\n                                },\n                                className: \"jsx-4bbd75c2920167f7\" + \" \" + `text-4xl text-gray-900 mb-6 text-center transition-all duration-700 ${showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"}`,\n                                children: \"How can I help you?\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 613,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                style: {\n                                    transitionDelay: showInitialUI ? \"120ms\" : \"0ms\"\n                                },\n                                className: \"jsx-4bbd75c2920167f7\" + \" \" + `relative w-full mb-6 flex flex-col items-center transition-all duration-500 ${showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        ref: messages.length === 0 ? inputRef : null,\n                                        value: message,\n                                        onChange: handleInputChange,\n                                        onKeyDown: handleKeyPress,\n                                        placeholder: \"Ask anything\",\n                                        autoFocus: messages.length === 0,\n                                        rows: 1,\n                                        style: {\n                                            boxSizing: \"border-box\",\n                                            zIndex: 1001,\n                                            position: \"relative\"\n                                        },\n                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"pt-3 px-4 lg:p-4 pb-16 pr-12 w-full min-h-[104px] max-h-[250px] overflow-y-auto hide-scrollbar text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base shadow-md resize-none reset-height\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 632,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: message.trim().length === 0,\n                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + `absolute right-3 bottom-3 w-9 h-9 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation ${message.trim().length === 0 ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                            className: \"w-4 h-4 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 655,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 647,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 624,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    transitionDelay: showInitialUI ? \"200ms\" : \"0ms\",\n                                    zIndex: 10\n                                },\n                                className: \"jsx-4bbd75c2920167f7\" + \" \" + `relative w-full max-w-2xl transition-all duration-500 ease-in-out ${isTyping ? \"opacity-0 pointer-events-none\" : showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: carouselRef,\n                                            style: {\n                                                transform: `translateX(-${currentSlide * 100}%)`\n                                            },\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"flex transition-transform duration-300 ease-in-out\",\n                                            children: Array.from({\n                                                length: Math.ceil(_utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.length / 2)\n                                            }).map((_, slideIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"w-full flex-shrink-0 flex gap-2 justify-center\",\n                                                    children: _utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.slice(slideIndex * 2, slideIndex * 2 + 2).map((card, cardIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleSuggestionClick(card.title),\n                                                            style: {\n                                                                width: \"fit-content\"\n                                                            },\n                                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"py-2 px-3 rounded-[8px] bg-[#f6f6f6] text-left group transition-all duration-200 touch-manipulation\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-[16px] font-[600] text-black mb-0.5 leading-tight whitespace-nowrap\",\n                                                                    children: card.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                    lineNumber: 693,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-[16px] text-gray-500 leading-tight whitespace-nowrap\",\n                                                                    children: card.subtitle\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                    lineNumber: 696,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, slideIndex * 2 + cardIndex, true, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 687,\n                                                            columnNumber: 27\n                                                        }, undefined))\n                                                }, slideIndex, false, {\n                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                    lineNumber: 679,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 671,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 670,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handlePrevSlide,\n                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"absolute left-28 top-1/2 transform -translate-y-1/2 -translate-x-6 w-8 h-8 bg-white border border-gray-200 rounded-full flex items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronLeft, {\n                                            className: \"w-3 h-3 text-gray-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 709,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 705,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleNextSlide,\n                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"absolute right-28 top-1/2 transform -translate-y-1/2 translate-x-6 w-8 h-8 bg-white border border-gray-200 rounded-full flex items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronRight, {\n                                            className: \"w-3 h-3 text-gray-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 715,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 711,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 658,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 612,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                    lineNumber: 611,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: desktopContainerRef,\n                            style: {\n                                overscrollBehavior: \"contain\",\n                                height: \"calc(100vh - 60px - 180px)\",\n                                marginTop: \"60px\" // exact navbar height\n                            },\n                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"flex-1 overflow-y-auto hide-scrollbar\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"w-full lg:w-[768px] mx-auto px-4 py-4\",\n                                children: [\n                                    renderMessages(),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: messagesEndRef,\n                                        className: \"jsx-4bbd75c2920167f7\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 733,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 731,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 722,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                zIndex: 1000,\n                                marginTop: \"20px\"\n                            },\n                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"fixed bottom-0 left-0 right-0 p-4 mt-5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSubmit,\n                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"relative w-full lg:w-[768px] mx-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            ref: messages.length > 0 ? inputRef : null,\n                                            value: message,\n                                            onChange: handleInputChange,\n                                            onKeyDown: handleKeyPress,\n                                            placeholder: \"Ask anything\",\n                                            rows: 1,\n                                            style: {\n                                                boxSizing: \"border-box\",\n                                                zIndex: 1001,\n                                                position: \"relative\"\n                                            },\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"pt-3 px-4 md:px-5 pb-10 pr-12 w-full min-h-[104px] max-h-[250px] overflow-y-auto text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base shadow-lg resize-none reset-height hide-scrollbar\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 745,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: message.trim().length === 0,\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + `absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation ${message.trim().length === 0 ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"}`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                                className: \"w-4 h-4 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 767,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 759,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 741,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-center mt-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-xs text-gray-500\",\n                                        children: [\n                                            \"This chat is powered by\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-black\",\n                                                children: \"Driply.me\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 773,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 771,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 770,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 737,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 609,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"lg:hidden overflow-hidden fixed inset-0 flex flex-col  mt-10 \",\n                children: [\n                    messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"flex flex-col items-center pt-15 pb-4 px-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"hidden md:flex lg:hidden flex-col items-center\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 786,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 785,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    paddingBottom: \"260px\"\n                                },\n                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"flex flex-col items-center justify-center flex-1 px-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"40ms\" : \"0ms\"\n                                    },\n                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + `text-2xl md:text-4xl text-gray-900 text-center leading-relaxed transition-all duration-700 ${showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"}`,\n                                    children: \"How can I help you?\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 792,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 788,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: mobileContainerRef,\n                                style: {\n                                    overscrollBehavior: \"contain\",\n                                    height: \"calc(100vh - 60px - 280px)\",\n                                    marginTop: \"60px\",\n                                    paddingBottom: \"20px\"\n                                },\n                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"md:hidden flex-1 overflow-y-auto hide-scrollbar\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"w-full max-w-[803px] mx-auto px-4 pb-6 pt-12\",\n                                    children: [\n                                        renderMessages(),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: messagesEndRef,\n                                            className: \"jsx-4bbd75c2920167f7\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 819,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 817,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 807,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: tabletContainerRef,\n                                style: {\n                                    overscrollBehavior: \"contain\",\n                                    height: \"calc(100vh - 60px - 200px)\",\n                                    marginTop: \"60px\",\n                                    paddingBottom: \"20px\"\n                                },\n                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"hidden md:block lg:hidden flex-1 overflow-y-auto hide-scrollbar\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"w-full max-w-[803px] mx-auto px-4 pb-6 pt-12\",\n                                    children: [\n                                        renderMessages(),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: messagesEndRef,\n                                            className: \"jsx-4bbd75c2920167f7\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 836,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 834,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 824,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    minHeight: \"160px\",\n                                    zIndex: 1000,\n                                    paddingBottom: \"env(safe-area-inset-bottom, 0)\",\n                                    transform: \"translateZ(0)\",\n                                    backfaceVisibility: \"hidden\"\n                                },\n                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"fixed bottom-0 left-0 right-0 bg-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        transform: \"translateZ(0)\"\n                                    },\n                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"px-4 py-4 bg-white\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            style: {\n                                                zIndex: 1001\n                                            },\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"relative bg-white\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    transform: \"translateZ(0)\"\n                                                },\n                                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"relative w-full max-w-[890px] mx-auto bg-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        ref: messages.length > 0 ? inputRef : null,\n                                                        value: message,\n                                                        onChange: handleInputChange,\n                                                        onKeyDown: handleKeyPress,\n                                                        placeholder: \"Ask anything\",\n                                                        rows: 1,\n                                                        style: {\n                                                            boxSizing: \"border-box\",\n                                                            maxHeight: \"250px\",\n                                                            zIndex: 1001,\n                                                            overscrollBehavior: \"none\",\n                                                            position: \"relative\"\n                                                        },\n                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"w-full min-h-[104px] pt-3 px-4 pb-10 pr-12 text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base resize-none overflow-y-auto reset-height\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 864,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        disabled: message.trim().length === 0,\n                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + `absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation ${message.trim().length === 0 ? \"bg-[#E0E0E0] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"}`,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                                            className: \"w-4 h-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 888,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 880,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 860,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 855,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-center py-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"This chat is powered by\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-black\",\n                                                        children: \"Driply.me\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 895,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 893,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 892,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 851,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 841,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"absolute bottom-0 left-0 right-0 bg-white\",\n                        children: messages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"120ms\" : \"0ms\",\n                                        position: \"relative\",\n                                        zIndex: 10,\n                                        backgroundColor: \"white\",\n                                        paddingBottom: \"10px\"\n                                    },\n                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + `${isMobile ? \"px-0\" : \"px-4\"} pt-2 pb-2 transition-all duration-500 ease-in-out ${isTyping ? \"opacity-0 pointer-events-none\" : showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"overflow-hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                ref: carouselRef,\n                                                style: {\n                                                    transform: `translateX(-${currentSlide * (isMobile ? 50 : 100)}%)`,\n                                                    paddingLeft: isMobile ? \"1rem\" : \"0\"\n                                                },\n                                                onTouchStart: handleTouchStart,\n                                                onTouchMove: handleTouchMove,\n                                                onTouchEnd: handleTouchEnd,\n                                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"flex transition-transform duration-300 ease-in-out\",\n                                                children: isMobile ? (()=>{\n                                                    const circularCards = [\n                                                        ..._utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS,\n                                                        ..._utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS\n                                                    ];\n                                                    return circularCards.map((card, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"flex-shrink-0 mt-3 mr-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleSuggestionClick(card.title),\n                                                                style: {\n                                                                    width: \"fit-content\"\n                                                                },\n                                                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"py-3 px-2.5 rounded-[8px] bg-[#F6F6F6] text-left hover:bg-[#D6EDFF] transition-all duration-200 inline-block touch-manipulation\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-[16px] font-[600] text-black mb-0.5 leading-tight whitespace-nowrap overflow-hidden text-ellipsis\",\n                                                                        children: card.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                        lineNumber: 955,\n                                                                        columnNumber: 31\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-[16px] text-gray-500 leading-tight whitespace-nowrap overflow-hidden text-ellipsis\",\n                                                                        children: card.subtitle\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                        lineNumber: 958,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                lineNumber: 948,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        }, `${index}-${card.title}`, false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 944,\n                                                            columnNumber: 27\n                                                        }, undefined));\n                                                })() : Array.from({\n                                                    length: Math.ceil(_utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.length / 2)\n                                                }).map((_, slideIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"w-full flex-shrink-0 flex gap-2 justify-center\",\n                                                        children: _utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.slice(slideIndex * 2, slideIndex * 2 + 2).map((card, cardIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleSuggestionClick(card.title),\n                                                                style: {\n                                                                    width: \"fit-content\"\n                                                                },\n                                                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"py-2 px-2.5 rounded-[8px] bg-[#F6F6F6] text-left group hover:bg-[#D6EDFF] transition-all duration-200 inline-block touch-manipulation\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-[14px] font-[600] text-black mb-0.5 leading-tight whitespace-nowrap\",\n                                                                        children: card.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                        lineNumber: 984,\n                                                                        columnNumber: 31\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-[16px] text-gray-500 leading-tight whitespace-nowrap\",\n                                                                        children: card.subtitle\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                        lineNumber: 987,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                ]\n                                                            }, slideIndex * 2 + cardIndex, true, {\n                                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                lineNumber: 976,\n                                                                columnNumber: 29\n                                                            }, undefined))\n                                                    }, slideIndex, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 968,\n                                                        columnNumber: 25\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 925,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 924,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handlePrevSlide,\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"hidden md:block absolute left-28 top-1/2 transform -translate-y-1/2 -translate-x-6 w-8 h-8 bg-white border border-gray-200 rounded-full items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronLeft, {\n                                                className: \"w-3 h-3 text-gray-600  ms-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1002,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 998,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleNextSlide,\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"hidden md:block absolute right-28 top-1/2 transform -translate-y-1/2 translate-x-6 w-8 h-8 bg-white border border-gray-200 rounded-full items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronRight, {\n                                                className: \"w-3 h-3 text-gray-600 ms-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1008,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1004,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 908,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"200ms\" : \"0ms\",\n                                        position: \"relative\",\n                                        zIndex: 5,\n                                        paddingBottom: \"env(safe-area-inset-bottom)\"\n                                    },\n                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + `px-4 bg-white transition-all duration-500 ${showInitialUI ? \"opacity-100 translate-y-0\" : \"translate-y-4\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"relative\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"relative w-full max-w-[890px] mx-auto\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        ref: messages.length === 0 ? inputRef : null,\n                                                        value: message,\n                                                        onChange: handleInputChange,\n                                                        onKeyDown: handleKeyPress,\n                                                        placeholder: \"Ask anything\",\n                                                        rows: 1,\n                                                        style: {\n                                                            boxSizing: \"border-box\",\n                                                            maxHeight: \"250px\",\n                                                            fontSize: \"16px\",\n                                                            zIndex: 1001,\n                                                            overscrollBehaviorY: \"contain\",\n                                                            position: \"relative\"\n                                                        },\n                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"w-full min-h-[104px] pt-3 px-4 pb-10 pr-12 text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base resize-none overflow-y-auto reset-height\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1025,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        disabled: message.trim().length === 0,\n                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + `absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation ${message.trim().length === 0 ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"}`,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                                            className: \"w-4 h-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 1050,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1042,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1024,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1023,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-center py-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"This chat is powered by\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-black\",\n                                                        children: \"Driply.me\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1057,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1055,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1054,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1013,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 904,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 782,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"4bbd75c2920167f7\",\n                children: '.loading-dot{-webkit-animation:loading-dots 1.4s infinite ease-in-out both;-moz-animation:loading-dots 1.4s infinite ease-in-out both;-o-animation:loading-dots 1.4s infinite ease-in-out both;animation:loading-dots 1.4s infinite ease-in-out both}.loading-dot:nth-child(1){-webkit-animation-delay:-.32s;-moz-animation-delay:-.32s;-o-animation-delay:-.32s;animation-delay:-.32s}.loading-dot:nth-child(2){-webkit-animation-delay:-.16s;-moz-animation-delay:-.16s;-o-animation-delay:-.16s;animation-delay:-.16s}.loading-dot:nth-child(3){-webkit-animation-delay:0s;-moz-animation-delay:0s;-o-animation-delay:0s;animation-delay:0s}@-webkit-keyframes loading-dots{0%,80%,100%{-webkit-transform:scale(1);transform:scale(1);opacity:1}40%{-webkit-transform:scale(1.5);transform:scale(1.5);opacity:.7}}@-moz-keyframes loading-dots{0%,80%,100%{-moz-transform:scale(1);transform:scale(1);opacity:1}40%{-moz-transform:scale(1.5);transform:scale(1.5);opacity:.7}}@-o-keyframes loading-dots{0%,80%,100%{-o-transform:scale(1);transform:scale(1);opacity:1}40%{-o-transform:scale(1.5);transform:scale(1.5);opacity:.7}}@keyframes loading-dots{0%,80%,100%{-webkit-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1);opacity:1}40%{-webkit-transform:scale(1.5);-moz-transform:scale(1.5);-o-transform:scale(1.5);transform:scale(1.5);opacity:.7}}.hide-scrollbar{-ms-overflow-style:none!important;scrollbar-width:none!important}.hide-scrollbar::-webkit-scrollbar{display:none!important}@media only screen and (max-width:1023px){.lg\\\\\\\\.jsx-4bbd75c2920167f7:hidden .absolute.bottom-0.jsx-4bbd75c2920167f7{position:fixed!important;bottom:env(safe-area-inset-bottom,0);left:0;right:0;width:100%;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;z-index:1000;background-color:white}textarea.jsx-4bbd75c2920167f7{-webkit-user-select:auto!important;-moz-user-select:auto!important;-ms-user-select:auto!important;user-select:auto!important;-webkit-appearance:none;-moz-appearance:none;-ms-appearance:none;appearance:none;-ms-scroll-chaining:none;overscroll-behavior:none;line-height:24px;max-height:250px!important;position:relative!important;-webkit-transform:translatez(0);-moz-transform:translatez(0);-ms-transform:translatez(0);-o-transform:translatez(0);transform:translatez(0);-webkit-backface-visibility:hidden;-moz-backface-visibility:hidden;backface-visibility:hidden;-webkit-perspective:1000;-moz-perspective:1000;perspective:1000;z-index:1001!important;background-color:white}.lg\\\\\\\\.jsx-4bbd75c2920167f7:hidden form.jsx-4bbd75c2920167f7{position:relative!important;z-index:1001;background-color:white}button[type=\"submit\"].jsx-4bbd75c2920167f7{position:absolute!important;right:12px;bottom:12px;z-index:1002!important;-webkit-tap-highlight-color:transparent;-ms-touch-action:-ms-manipulation;-ms-touch-action:manipulation;touch-action:manipulation;-webkit-transform:translatez(0);-moz-transform:translatez(0);-ms-transform:translatez(0);-o-transform:translatez(0);transform:translatez(0)}.lg\\\\\\\\.jsx-4bbd75c2920167f7:hidden .bg-white.jsx-4bbd75c2920167f7{background-color:white!important}}@media only screen and (min-width:1024px){textarea.jsx-4bbd75c2920167f7{max-height:250px!important;overflow-y:auto!important;-ms-scroll-chaining:none;overscroll-behavior:contain}.reset-height.jsx-4bbd75c2920167f7{min-height:104px!important;max-height:250px!important;overflow-y:auto!important}}.fixed-bottom.jsx-4bbd75c2920167f7{position:fixed!important;bottom:0;left:0;right:0;background:white}'\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n        lineNumber: 607,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatInterface);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ChatInterface.jsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ChatInterfaceWrapper.jsx":
/*!*************************************************!*\
  !*** ./src/components/ChatInterfaceWrapper.jsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatInterfaceWrapper)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ChatInterface__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ChatInterface */ \"(ssr)/./src/components/ChatInterface.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n/**\r\n * Client-side wrapper for ChatInterface component\r\n * This separates client components from server components to fix metadata issues\r\n * Following Next.js App Router best practices\r\n */ function ChatInterfaceWrapper({ slug, query }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatInterface__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        slug: slug,\n        query: query\n    }, void 0, false, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterfaceWrapper.jsx\",\n        lineNumber: 12,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9DaGF0SW50ZXJmYWNlV3JhcHBlci5qc3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUUwQjtBQUNrQjtBQUU1Qzs7OztDQUlDLEdBQ2MsU0FBU0UscUJBQXFCLEVBQUVDLElBQUksRUFBRUMsS0FBSyxFQUFFO0lBQzFELHFCQUFPLDhEQUFDSCxzREFBYUE7UUFBQ0UsTUFBTUE7UUFBTUMsT0FBT0E7Ozs7OztBQUMzQyIsInNvdXJjZXMiOlsiRDpcXExhcHRvcCBkYXRhXFxEUklQTFktQ0hBVFxcY2hhdC1uZXh0XFxzcmNcXGNvbXBvbmVudHNcXENoYXRJbnRlcmZhY2VXcmFwcGVyLmpzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcclxuXHJcbmltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCBDaGF0SW50ZXJmYWNlIGZyb20gJy4vQ2hhdEludGVyZmFjZSc7XHJcblxyXG4vKipcclxuICogQ2xpZW50LXNpZGUgd3JhcHBlciBmb3IgQ2hhdEludGVyZmFjZSBjb21wb25lbnRcclxuICogVGhpcyBzZXBhcmF0ZXMgY2xpZW50IGNvbXBvbmVudHMgZnJvbSBzZXJ2ZXIgY29tcG9uZW50cyB0byBmaXggbWV0YWRhdGEgaXNzdWVzXHJcbiAqIEZvbGxvd2luZyBOZXh0LmpzIEFwcCBSb3V0ZXIgYmVzdCBwcmFjdGljZXNcclxuICovXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIENoYXRJbnRlcmZhY2VXcmFwcGVyKHsgc2x1ZywgcXVlcnkgfSkge1xyXG4gIHJldHVybiA8Q2hhdEludGVyZmFjZSBzbHVnPXtzbHVnfSBxdWVyeT17cXVlcnl9IC8+O1xyXG59XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkNoYXRJbnRlcmZhY2UiLCJDaGF0SW50ZXJmYWNlV3JhcHBlciIsInNsdWciLCJxdWVyeSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ChatInterfaceWrapper.jsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ClientWrapper.jsx":
/*!******************************************!*\
  !*** ./src/components/ClientWrapper.jsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClientWrapper)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Navbar */ \"(ssr)/./src/components/Navbar.jsx\");\n/* harmony import */ var _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/ChatContext */ \"(ssr)/./src/contexts/ChatContext.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n/**\r\n * Client wrapper component that contains all client-side logic\r\n * This separates client components from server components to fix metadata issues\r\n * Following Next.js App Router best practices\r\n */ function ClientWrapper({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_ChatContext__WEBPACK_IMPORTED_MODULE_3__.ChatProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ClientWrapper.jsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ClientWrapper.jsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9DbGllbnRXcmFwcGVyLmpzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUUwQjtBQUNJO0FBQ3lCO0FBRXZEOzs7O0NBSUMsR0FDYyxTQUFTRyxjQUFjLEVBQUVDLFFBQVEsRUFBRTtJQUNoRCxxQkFDRSw4REFBQ0YsK0RBQVlBOzswQkFDWCw4REFBQ0QsK0NBQU1BOzs7OztZQUNORzs7Ozs7OztBQUdQIiwic291cmNlcyI6WyJEOlxcTGFwdG9wIGRhdGFcXERSSVBMWS1DSEFUXFxjaGF0LW5leHRcXHNyY1xcY29tcG9uZW50c1xcQ2xpZW50V3JhcHBlci5qc3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XHJcblxyXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgTmF2YmFyIGZyb20gXCIuL05hdmJhclwiO1xyXG5pbXBvcnQgeyBDaGF0UHJvdmlkZXIgfSBmcm9tIFwiLi4vY29udGV4dHMvQ2hhdENvbnRleHRcIjtcclxuXHJcbi8qKlxyXG4gKiBDbGllbnQgd3JhcHBlciBjb21wb25lbnQgdGhhdCBjb250YWlucyBhbGwgY2xpZW50LXNpZGUgbG9naWNcclxuICogVGhpcyBzZXBhcmF0ZXMgY2xpZW50IGNvbXBvbmVudHMgZnJvbSBzZXJ2ZXIgY29tcG9uZW50cyB0byBmaXggbWV0YWRhdGEgaXNzdWVzXHJcbiAqIEZvbGxvd2luZyBOZXh0LmpzIEFwcCBSb3V0ZXIgYmVzdCBwcmFjdGljZXNcclxuICovXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIENsaWVudFdyYXBwZXIoeyBjaGlsZHJlbiB9KSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxDaGF0UHJvdmlkZXI+XHJcbiAgICAgIDxOYXZiYXIgLz5cclxuICAgICAge2NoaWxkcmVufVxyXG4gICAgPC9DaGF0UHJvdmlkZXI+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJOYXZiYXIiLCJDaGF0UHJvdmlkZXIiLCJDbGllbnRXcmFwcGVyIiwiY2hpbGRyZW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ClientWrapper.jsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Navbar.jsx":
/*!***********************************!*\
  !*** ./src/components/Navbar.jsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_FiEdit_react_icons_fi__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FiEdit!=!react-icons/fi */ \"(ssr)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _assets_images_logo_png__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../assets/images/logo.png */ \"(ssr)/./src/assets/images/logo.png\");\n/* harmony import */ var _barrel_optimize_names_HiUserCircle_react_icons_hi2__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=HiUserCircle!=!react-icons/hi2 */ \"(ssr)/./node_modules/react-icons/hi2/index.mjs\");\n/* harmony import */ var _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../contexts/ChatContext */ \"(ssr)/./src/contexts/ChatContext.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nconst Navbar = ()=>{\n    const [businessName, setBusinessName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Get context values\n    const { metadata, hasMetadata } = (0,_contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__.useChatContext)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            // First, try to get business name from context\n            if (hasMetadata() && metadata.businessName) {\n                setBusinessName(metadata.businessName);\n                return;\n            }\n            // Fallback to localStorage and event listeners\n            const checkBusinessName = {\n                \"Navbar.useEffect.checkBusinessName\": ()=>{\n                    const storedName = localStorage.getItem(\"BusinessName\") || \"Driply\";\n                    if (storedName && storedName !== \"undefined\" && storedName !== \"null\") {\n                        setBusinessName(storedName);\n                        return true;\n                    }\n                    return false;\n                }\n            }[\"Navbar.useEffect.checkBusinessName\"];\n            if (checkBusinessName()) {\n                return;\n            }\n            const handleBusinessNameLoaded = {\n                \"Navbar.useEffect.handleBusinessNameLoaded\": (event)=>{\n                    if (event.detail && event.detail.businessName) {\n                        setBusinessName(event.detail.businessName);\n                    }\n                }\n            }[\"Navbar.useEffect.handleBusinessNameLoaded\"];\n            window.addEventListener(\"businessNameLoaded\", handleBusinessNameLoaded);\n            const interval = setInterval({\n                \"Navbar.useEffect.interval\": ()=>{\n                    if (checkBusinessName()) {\n                        clearInterval(interval);\n                    }\n                }\n            }[\"Navbar.useEffect.interval\"], 100);\n            const timeout = setTimeout({\n                \"Navbar.useEffect.timeout\": ()=>{\n                    clearInterval(interval);\n                }\n            }[\"Navbar.useEffect.timeout\"], 5000);\n            return ({\n                \"Navbar.useEffect\": ()=>{\n                    window.removeEventListener(\"businessNameLoaded\", handleBusinessNameLoaded);\n                    clearInterval(interval);\n                    clearTimeout(timeout);\n                }\n            })[\"Navbar.useEffect\"];\n        }\n    }[\"Navbar.useEffect\"], [\n        metadata,\n        hasMetadata\n    ]);\n    const handleNewConversation = ()=>{\n        localStorage.removeItem(\"userID\");\n        localStorage.removeItem(\"customerName_userId\");\n        localStorage.removeItem(\"BusinessName\");\n        localStorage.removeItem(\"userId\");\n        window.location.reload();\n    };\n    const handleHomeClick = (e)=>{\n        e.preventDefault();\n        window.location.reload();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-white fixed top-0 left-0 right-0 z-50\",\n        style: {\n            height: \"60px\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-between items-center h-full px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 \",\n                    children: [\n                        metadata.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                src: metadata.image,\n                                alt: \"user-image\",\n                                className: \"h-10 w-10 hover:border hover:border-gray-400 rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\Navbar.jsx\",\n                                lineNumber: 85,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiUserCircle_react_icons_hi2__WEBPACK_IMPORTED_MODULE_6__.HiUserCircle, {\n                            className: \"text-black text-xl w-10 h-10 rounded-full object-cover hover:border hover:border-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\Navbar.jsx\",\n                            lineNumber: 92,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            onClick: handleHomeClick,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-[17px] font-[500] text-black \",\n                                children: businessName\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\Navbar.jsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\Navbar.jsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\Navbar.jsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleNewConversation,\n                    className: \"px-4 py-2 rounded-lg cursor-pointer\",\n                    title: \"Start New Conversation\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit_react_icons_fi__WEBPACK_IMPORTED_MODULE_7__.FiEdit, {\n                        className: \"text-black text-xl\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\Navbar.jsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\Navbar.jsx\",\n                    lineNumber: 100,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\Navbar.jsx\",\n            lineNumber: 81,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\Navbar.jsx\",\n        lineNumber: 80,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Navbar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Navbar.jsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/ChatContext.jsx":
/*!**************************************!*\
  !*** ./src/contexts/ChatContext.jsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatProvider: () => (/* binding */ ChatProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useChatContext: () => (/* binding */ useChatContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useChatContext,ChatProvider,default auto */ \n\n// Create the context\nconst ChatContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\n// Custom hook to use the chat context\nconst useChatContext = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ChatContext);\n    if (!context) {\n        throw new Error('useChatContext must be used within a ChatProvider');\n    }\n    return context;\n};\n// Chat Provider component\nconst ChatProvider = ({ children })=>{\n    console.log(\"ChatProvider\", children);\n    const [metadata, setMetadata] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: null,\n        keywords: null,\n        description: null,\n        image: null\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Function to update metadata\n    const updateMetadata = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[updateMetadata]\": (newMetadata)=>{\n            setMetadata({\n                \"ChatProvider.useCallback[updateMetadata]\": (prevMetadata)=>({\n                        ...prevMetadata,\n                        ...newMetadata\n                    })\n            }[\"ChatProvider.useCallback[updateMetadata]\"]);\n        }\n    }[\"ChatProvider.useCallback[updateMetadata]\"], []);\n    // Function to clear metadata\n    const clearMetadata = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[clearMetadata]\": ()=>{\n            setMetadata({\n                title: null,\n                keywords: null,\n                description: null,\n                image: null\n            });\n        }\n    }[\"ChatProvider.useCallback[clearMetadata]\"], []);\n    // Function to set loading state\n    const setLoadingState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[setLoadingState]\": (isLoading)=>{\n            setLoading(isLoading);\n        }\n    }[\"ChatProvider.useCallback[setLoadingState]\"], []);\n    // Function to set error state\n    const setErrorState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[setErrorState]\": (errorMessage)=>{\n            setError(errorMessage);\n        }\n    }[\"ChatProvider.useCallback[setErrorState]\"], []);\n    // Function to clear error\n    const clearError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[clearError]\": ()=>{\n            setError(null);\n        }\n    }[\"ChatProvider.useCallback[clearError]\"], []);\n    const value = {\n        metadata,\n        loading,\n        error,\n        updateMetadata,\n        clearMetadata,\n        setLoadingState,\n        setErrorState,\n        clearError,\n        // Helper getters for easy access\n        getTitle: ()=>metadata.title,\n        getKeywords: ()=>metadata.keywords,\n        getDescription: ()=>metadata.description,\n        getImage: ()=>metadata.image,\n        hasMetadata: ()=>metadata.title !== null || metadata.keywords !== null || metadata.description !== null\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\contexts\\\\ChatContext.jsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatContext);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/ChatContext.jsx\n");

/***/ }),

/***/ "(ssr)/./src/utils/config.js":
/*!*****************************!*\
  !*** ./src/utils/config.js ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_ENDPOINTS: () => (/* binding */ API_ENDPOINTS),\n/* harmony export */   EXTERNAL_API_BASE_URL: () => (/* binding */ EXTERNAL_API_BASE_URL),\n/* harmony export */   EXTERNAL_API_ENDPOINTS: () => (/* binding */ EXTERNAL_API_ENDPOINTS),\n/* harmony export */   getExternalApiUrl: () => (/* binding */ getExternalApiUrl),\n/* harmony export */   getSSEUrl: () => (/* binding */ getSSEUrl)\n/* harmony export */ });\nconst EXTERNAL_API_BASE_URL = \"https://api-develop.driply.me/\" || 0;\nconst getExternalApiUrl = ()=>{\n    const url = EXTERNAL_API_BASE_URL;\n    return url.endsWith('/') ? url : `${url}/`;\n};\nconst API_ENDPOINTS = {\n    CHAT_INIT: '/api/chat/init',\n    CHAT_SEND: '/api/chat/send',\n    CHAT_MESSAGES: '/api/chat/messages',\n    CHAT_SETTINGS: '/api/chat/settings'\n};\nconst getSSEUrl = (userId)=>{\n    return `${getExternalApiUrl()}chat-pusher/chat?stream=${userId}`;\n};\nconst EXTERNAL_API_ENDPOINTS = {\n    CHAT_INIT: `${getExternalApiUrl()}api/chat/init`,\n    CHAT_SEND: `${getExternalApiUrl()}api/chat/message`,\n    CHAT_MESSAGES: `${getExternalApiUrl()}api/chat/messages`,\n    CHAT_SETTINGS: `${getExternalApiUrl()}api/chat/settings`\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/config.js\n");

/***/ }),

/***/ "(ssr)/./src/utils/constants.js":
/*!********************************!*\
  !*** ./src/utils/constants.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APP_NAME: () => (/* binding */ APP_NAME),\n/* harmony export */   SUGGESTION_CARDS: () => (/* binding */ SUGGESTION_CARDS)\n/* harmony export */ });\nconst APP_NAME = 'DRIPLY';\nconst SUGGESTION_CARDS = [\n    {\n        title: \"Design a schema\",\n        subtitle: \"for an online merch store\"\n    },\n    {\n        title: \"Explain airplane\",\n        subtitle: \"to someone 5 years old\"\n    },\n    {\n        title: \"Create a work plan\",\n        subtitle: \"for beginners at home\"\n    },\n    {\n        title: \"Write a recipe\",\n        subtitle: \"for chocolate chip cookies\"\n    },\n    {\n        title: \"Plan a budget\",\n        subtitle: \"for a weekend trip\"\n    },\n    {\n        title: \"Learn JavaScript \",\n        subtitle: \"basic concepts \"\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvdXRpbHMvY29uc3RhbnRzLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQ08sTUFBTUEsV0FBVyxTQUFRO0FBR3pCLE1BQU1DLG1CQUFtQjtJQUM5QjtRQUNFQyxPQUFPO1FBQ1BDLFVBQVU7SUFDWjtJQUNBO1FBQ0VELE9BQU87UUFDUEMsVUFBVTtJQUNaO0lBQ0E7UUFDRUQsT0FBTztRQUNQQyxVQUFVO0lBQ1o7SUFDQTtRQUNFRCxPQUFPO1FBQ1BDLFVBQVU7SUFDWjtJQUNBO1FBQ0VELE9BQU87UUFDUEMsVUFBVTtJQUNaO0lBQ0E7UUFDRUQsT0FBTztRQUNQQyxVQUFVO0lBQ1o7Q0FDRCIsInNvdXJjZXMiOlsiRDpcXExhcHRvcCBkYXRhXFxEUklQTFktQ0hBVFxcY2hhdC1uZXh0XFxzcmNcXHV0aWxzXFxjb25zdGFudHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXHJcbmV4cG9ydCBjb25zdCBBUFBfTkFNRSA9ICdEUklQTFknXHJcblxyXG5cclxuZXhwb3J0IGNvbnN0IFNVR0dFU1RJT05fQ0FSRFMgPSBbXHJcbiAge1xyXG4gICAgdGl0bGU6IFwiRGVzaWduIGEgc2NoZW1hXCIsXHJcbiAgICBzdWJ0aXRsZTogXCJmb3IgYW4gb25saW5lIG1lcmNoIHN0b3JlXCJcclxuICB9LFxyXG4gIHtcclxuICAgIHRpdGxlOiBcIkV4cGxhaW4gYWlycGxhbmVcIixcclxuICAgIHN1YnRpdGxlOiBcInRvIHNvbWVvbmUgNSB5ZWFycyBvbGRcIlxyXG4gIH0sXHJcbiAge1xyXG4gICAgdGl0bGU6IFwiQ3JlYXRlIGEgd29yayBwbGFuXCIsXHJcbiAgICBzdWJ0aXRsZTogXCJmb3IgYmVnaW5uZXJzIGF0IGhvbWVcIlxyXG4gIH0sXHJcbiAge1xyXG4gICAgdGl0bGU6IFwiV3JpdGUgYSByZWNpcGVcIixcclxuICAgIHN1YnRpdGxlOiBcImZvciBjaG9jb2xhdGUgY2hpcCBjb29raWVzXCJcclxuICB9LFxyXG4gIHtcclxuICAgIHRpdGxlOiBcIlBsYW4gYSBidWRnZXRcIixcclxuICAgIHN1YnRpdGxlOiBcImZvciBhIHdlZWtlbmQgdHJpcFwiXHJcbiAgfSxcclxuICB7XHJcbiAgICB0aXRsZTogXCJMZWFybiBKYXZhU2NyaXB0IFwiLFxyXG4gICAgc3VidGl0bGU6IFwiYmFzaWMgY29uY2VwdHMgXCJcclxuICB9XHJcbl1cclxuXHJcblxyXG4iXSwibmFtZXMiOlsiQVBQX05BTUUiLCJTVUdHRVNUSU9OX0NBUkRTIiwidGl0bGUiLCJzdWJ0aXRsZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/constants.js\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "node:os":
/*!**************************!*\
  !*** external "node:os" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:os");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:tty":
/*!***************************!*\
  !*** external "node:tty" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:tty");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/axios","vendor-chunks/asynckit","vendor-chunks/math-intrinsics","vendor-chunks/react-icons","vendor-chunks/es-errors","vendor-chunks/@swc","vendor-chunks/form-data","vendor-chunks/call-bind-apply-helpers","vendor-chunks/debug","vendor-chunks/get-proto","vendor-chunks/styled-jsx","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/follow-redirects","vendor-chunks/supports-color","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/get-intrinsic","vendor-chunks/es-set-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/delayed-stream","vendor-chunks/combined-stream"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Bslug%5D%2Fpage&page=%2F%5Bslug%5D%2Fpage&appPaths=%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2F%5Bslug%5D%2Fpage.js&appDir=D%3A%5CLaptop%20data%5CDRIPLY-CHAT%5Cchat-next%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CLaptop%20data%5CDRIPLY-CHAT%5Cchat-next&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();