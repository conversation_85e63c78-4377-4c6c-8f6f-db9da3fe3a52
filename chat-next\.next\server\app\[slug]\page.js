/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/[slug]/page";
exports.ids = ["app/[slug]/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Bslug%5D%2Fpage&page=%2F%5Bslug%5D%2Fpage&appPaths=%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2F%5Bslug%5D%2Fpage.js&appDir=D%3A%5CLaptop%20data%5CDRIPLY-CHAT%5Cchat-next%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CLaptop%20data%5CDRIPLY-CHAT%5Cchat-next&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Bslug%5D%2Fpage&page=%2F%5Bslug%5D%2Fpage&appPaths=%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2F%5Bslug%5D%2Fpage.js&appDir=D%3A%5CLaptop%20data%5CDRIPLY-CHAT%5Cchat-next%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CLaptop%20data%5CDRIPLY-CHAT%5Cchat-next&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.js */ \"(rsc)/./src/app/layout.js\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[slug]/page.js */ \"(rsc)/./src/app/[slug]/page.js\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '[slug]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\app\\\\[slug]\\\\page.js\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\app\\\\layout.js\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\app\\\\[slug]\\\\page.js\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/[slug]/page\",\n        pathname: \"/[slug]\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Bslug%5D%2Fpage&page=%2F%5Bslug%5D%2Fpage&appPaths=%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2F%5Bslug%5D%2Fpage.js&appDir=D%3A%5CLaptop%20data%5CDRIPLY-CHAT%5Cchat-next%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CLaptop%20data%5CDRIPLY-CHAT%5Cchat-next&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Wix_Madefor_Text%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-wix-madefor-text%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22wixMadeforText%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CClientWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Wix_Madefor_Text%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-wix-madefor-text%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22wixMadeforText%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CClientWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ClientWrapper.jsx */ \"(rsc)/./src/components/ClientWrapper.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Wix_Madefor_Text%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-wix-madefor-text%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22wixMadeforText%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CClientWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CChatInterfaceWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CChatInterfaceWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ChatInterfaceWrapper.jsx */ \"(rsc)/./src/components/ChatInterfaceWrapper.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNMYXB0b3AlMjBkYXRhJTVDJTVDRFJJUExZLUNIQVQlNUMlNUNjaGF0LW5leHQlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDQ2hhdEludGVyZmFjZVdyYXBwZXIuanN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOExBQW9KIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiRDpcXFxcTGFwdG9wIGRhdGFcXFxcRFJJUExZLUNIQVRcXFxcY2hhdC1uZXh0XFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXENoYXRJbnRlcmZhY2VXcmFwcGVyLmpzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CChatInterfaceWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxMYXB0b3AgZGF0YVxcRFJJUExZLUNIQVRcXGNoYXQtbmV4dFxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/[slug]/page.js":
/*!********************************!*\
  !*** ./src/app/[slug]/page.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   generateMetadata: () => (/* binding */ generateMetadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ChatInterfaceWrapper__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/ChatInterfaceWrapper */ \"(rsc)/./src/components/ChatInterfaceWrapper.jsx\");\n\n\n\n// Dynamic metadata generation using your API response data\nasync function generateMetadata({ params }) {\n    const { slug } = await params;\n    // Default fallback metadata\n    let title = `Chat with ${slug} - Driply`;\n    let description = `Start a conversation with ${slug} on Driply platform`;\n    let keywords = [\n        'chat',\n        slug,\n        'driply',\n        'ai',\n        'conversation'\n    ];\n    let businessName = 'Driply';\n    let image = '/og-image.jpg';\n    try {\n        // Fetch the same data that your ChatContext uses\n        const response = await fetch(`${\"https://api-develop.driply.me/\" || 0}/api/chat/settings?customerName=${slug}`, {\n            method: 'GET',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            // Cache for better performance\n            next: {\n                revalidate: 300\n            }\n        });\n        if (response.ok) {\n            const data = await response.json();\n            console.log('[Metadata] API Response:', data);\n            if (data && data.customerName) {\n                // Extract data from your API response structure\n                businessName = data.businessName || 'Driply';\n                const metaData = data.metaData || {};\n                // Use the metadata from your API response\n                title = metaData.title || `${businessName} Chat - ${slug}`;\n                description = metaData.description || `Chat with ${businessName} - Powered by Driply`;\n                // Handle keywords - convert string to array if needed\n                if (metaData.keywords) {\n                    keywords = typeof metaData.keywords === 'string' ? metaData.keywords.split(',').map((k)=>k.trim()) : Array.isArray(metaData.keywords) ? metaData.keywords : [\n                        metaData.keywords\n                    ];\n                } else {\n                    keywords = [\n                        'chat',\n                        slug,\n                        businessName.toLowerCase(),\n                        'driply',\n                        'ai',\n                        'conversation'\n                    ];\n                }\n                // Use custom image if provided\n                if (metaData.image && metaData.image.trim()) {\n                    image = metaData.image;\n                }\n                console.log('[Metadata] Using dynamic metadata:', {\n                    title,\n                    description,\n                    keywords,\n                    businessName,\n                    image\n                });\n            }\n        } else {\n            console.warn('[Metadata] API request failed, using fallback metadata');\n        }\n    } catch (error) {\n        console.warn('[Metadata] Error fetching dynamic metadata:', error.message);\n    // Will use fallback metadata\n    }\n    // Return the complete metadata object\n    return {\n        title: title,\n        description: description,\n        keywords: keywords,\n        openGraph: {\n            title: title,\n            description: description,\n            type: 'website',\n            url: `https://yourdomain.com/${slug}`,\n            siteName: businessName,\n            images: [\n                {\n                    url: image,\n                    width: 1200,\n                    height: 630,\n                    alt: `${businessName} Chat - ${slug}`\n                }\n            ],\n            locale: 'en_US'\n        },\n        twitter: {\n            card: 'summary_large_image',\n            title: title,\n            description: description,\n            images: [\n                image\n            ]\n        },\n        robots: {\n            index: true,\n            follow: true\n        },\n        authors: [\n            {\n                name: businessName\n            }\n        ],\n        creator: businessName,\n        publisher: 'Driply'\n    };\n}\n// Server component - no \"use client\" directive\nconst page = async ({ params, searchParams })=>{\n    const { slug } = await params;\n    const queryParams = await searchParams;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatInterfaceWrapper__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            slug: slug,\n            query: queryParams\n        }, void 0, false, {\n            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\app\\\\[slug]\\\\page.js\",\n            lineNumber: 110,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\app\\\\[slug]\\\\page.js\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (page);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/[slug]/page.js\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"f57a3c65339e\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcTGFwdG9wIGRhdGFcXERSSVBMWS1DSEFUXFxjaGF0LW5leHRcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImY1N2EzYzY1MzM5ZVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.js":
/*!***************************!*\
  !*** ./src/app/layout.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.js\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.js\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_js_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.js\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.js\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_js_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Wix_Madefor_Text_arguments_variable_font_wix_madefor_text_subsets_latin_variableName_wixMadeforText___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.js\",\"import\":\"Wix_Madefor_Text\",\"arguments\":[{\"variable\":\"--font-wix-madefor-text\",\"subsets\":[\"latin\"]}],\"variableName\":\"wixMadeforText\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.js\\\",\\\"import\\\":\\\"Wix_Madefor_Text\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-wix-madefor-text\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"wixMadeforText\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Wix_Madefor_Text_arguments_variable_font_wix_madefor_text_subsets_latin_variableName_wixMadeforText___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_js_import_Wix_Madefor_Text_arguments_variable_font_wix_madefor_text_subsets_latin_variableName_wixMadeforText___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_ClientWrapper__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/ClientWrapper */ \"(rsc)/./src/components/ClientWrapper.jsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: {\n        default: \"Driply - AI Chat Platform\",\n        template: \"%s | Driply\"\n    },\n    description: \"Driply is an advanced AI chat platform that provides intelligent conversations and seamless user experience.\",\n    keywords: [\n        \"AI\",\n        \"chat\",\n        \"conversation\",\n        \"driply\",\n        \"artificial intelligence\"\n    ],\n    authors: [\n        {\n            name: \"Driply Team\"\n        }\n    ],\n    creator: \"Driply\",\n    publisher: \"Driply\",\n    robots: {\n        index: true,\n        follow: true\n    },\n    openGraph: {\n        type: \"website\",\n        locale: \"en_US\",\n        url: \"https://yourdomain.com\",\n        siteName: \"Driply\",\n        title: \"Driply - AI Chat Platform\",\n        description: \"Driply is an advanced AI chat platform that provides intelligent conversations and seamless user experience.\",\n        images: [\n            {\n                url: \"/og-image.jpg\",\n                width: 1200,\n                height: 630,\n                alt: \"Driply AI Chat Platform\"\n            }\n        ]\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"Driply - AI Chat Platform\",\n        description: \"Driply is an advanced AI chat platform that provides intelligent conversations and seamless user experience.\",\n        images: [\n            \"/og-image.jpg\"\n        ]\n    },\n    viewport: \"width=device-width, initial-scale=1\",\n    themeColor: \"#000000\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_js_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default().variable)} ${(next_font_google_target_css_path_src_app_layout_js_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default().variable)} ${(next_font_google_target_css_path_src_app_layout_js_import_Wix_Madefor_Text_arguments_variable_font_wix_madefor_text_subsets_latin_variableName_wixMadeforText___WEBPACK_IMPORTED_MODULE_5___default().variable)} antialiased`,\n            suppressHydrationWarning: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientWrapper__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\app\\\\layout.js\",\n                lineNumber: 71,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\app\\\\layout.js\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\app\\\\layout.js\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.js\n");

/***/ }),

/***/ "(rsc)/./src/components/ChatInterfaceWrapper.jsx":
/*!*************************************************!*\
  !*** ./src/components/ChatInterfaceWrapper.jsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterfaceWrapper.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Laptop data\\DRIPLY-CHAT\\chat-next\\src\\components\\ChatInterfaceWrapper.jsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/ClientWrapper.jsx":
/*!******************************************!*\
  !*** ./src/components/ClientWrapper.jsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ClientWrapper.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Laptop data\\DRIPLY-CHAT\\chat-next\\src\\components\\ClientWrapper.jsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Wix_Madefor_Text%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-wix-madefor-text%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22wixMadeforText%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CClientWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Wix_Madefor_Text%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-wix-madefor-text%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22wixMadeforText%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CClientWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ClientWrapper.jsx */ \"(ssr)/./src/components/ClientWrapper.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Wix_Madefor_Text%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-wix-madefor-text%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22wixMadeforText%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CClientWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CChatInterfaceWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CChatInterfaceWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ChatInterfaceWrapper.jsx */ \"(ssr)/./src/components/ChatInterfaceWrapper.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNMYXB0b3AlMjBkYXRhJTVDJTVDRFJJUExZLUNIQVQlNUMlNUNjaGF0LW5leHQlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDQ2hhdEludGVyZmFjZVdyYXBwZXIuanN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOExBQW9KIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiRDpcXFxcTGFwdG9wIGRhdGFcXFxcRFJJUExZLUNIQVRcXFxcY2hhdC1uZXh0XFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXENoYXRJbnRlcmZhY2VXcmFwcGVyLmpzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLaptop%20data%5C%5CDRIPLY-CHAT%5C%5Cchat-next%5C%5Csrc%5C%5Ccomponents%5C%5CChatInterfaceWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/assets/images/logo.png":
/*!************************************!*\
  !*** ./src/assets/images/logo.png ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/logo.425de58c.png\",\"height\":188,\"width\":188,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Flogo.425de58c.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXNzZXRzL2ltYWdlcy9sb2dvLnBuZyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsQ0FBQyw0TEFBNEwiLCJzb3VyY2VzIjpbIkQ6XFxMYXB0b3AgZGF0YVxcRFJJUExZLUNIQVRcXGNoYXQtbmV4dFxcc3JjXFxhc3NldHNcXGltYWdlc1xcbG9nby5wbmciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2xvZ28uNDI1ZGU1OGMucG5nXCIsXCJoZWlnaHRcIjoxODgsXCJ3aWR0aFwiOjE4OCxcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZsb2dvLjQyNWRlNThjLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo4fTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/assets/images/logo.png\n");

/***/ }),

/***/ "(ssr)/./src/components/CarouselSection.jsx":
/*!********************************************!*\
  !*** ./src/components/CarouselSection.jsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _NavigationArrows__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./NavigationArrows */ \"(ssr)/./src/components/NavigationArrows.jsx\");\n/* harmony import */ var _LoadingIndicators__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./LoadingIndicators */ \"(ssr)/./src/components/LoadingIndicators.jsx\");\n/* harmony import */ var _utils_carouselUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/carouselUtils */ \"(ssr)/./src/utils/carouselUtils.js\");\n/* harmony import */ var _utils_deviceUtils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils/deviceUtils */ \"(ssr)/./src/utils/deviceUtils.js\");\n/**\n * CarouselSection Component\n * Handles suggestion cards carousel with loading states and navigation\n */ \n\n\n\n\n\nconst CarouselCard = ({ card, onClick, isMobile = false })=>{\n    const settings = (0,_utils_deviceUtils__WEBPACK_IMPORTED_MODULE_5__.getCarouselSettings)(isMobile);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: ()=>onClick(card?.question),\n        className: \"py-3 px-4 rounded-[8px] bg-[#f6f6f6] text-left group transition-all duration-200 touch-manipulation flex-shrink-0 hover:bg-[#e6e6e6]\",\n        style: {\n            width: settings.cardWidth,\n            minHeight: settings.minHeight\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-[16px] font-[600] text-black mb-1 leading-snug break-words word-wrap overflow-wrap-anywhere\",\n                children: card.question\n            }, void 0, false, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\CarouselSection.jsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-[14px] text-gray-500 leading-snug break-words word-wrap overflow-wrap-anywhere\",\n                children: card.subtitle\n            }, void 0, false, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\CarouselSection.jsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\CarouselSection.jsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, undefined);\n};\nconst CarouselContent = ({ settingData, currentSlide, onCardClick, carouselRef, onTouchStart, onTouchMove, onTouchEnd, isMobile = false })=>{\n    if (!(0,_utils_carouselUtils__WEBPACK_IMPORTED_MODULE_4__.areCardsLoaded)(settingData)) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LoadingIndicators__WEBPACK_IMPORTED_MODULE_3__.CarouselLoadingSpinner, {}, void 0, false, {\n            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\CarouselSection.jsx\",\n            lineNumber: 45,\n            columnNumber: 12\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: carouselRef,\n        className: \"flex gap-4 transition-transform duration-300 ease-in-out\",\n        style: {\n            transform: (0,_utils_carouselUtils__WEBPACK_IMPORTED_MODULE_4__.getCarouselTransform)(currentSlide)\n        },\n        onTouchStart: onTouchStart,\n        onTouchMove: onTouchMove,\n        onTouchEnd: onTouchEnd,\n        children: settingData.suggestedTopics.map((card, cardIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CarouselCard, {\n                card: card,\n                onClick: onCardClick,\n                isMobile: isMobile\n            }, cardIndex, false, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\CarouselSection.jsx\",\n                lineNumber: 60,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\CarouselSection.jsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, undefined);\n};\nconst CarouselSection = ({ settingData, currentSlide, onCardClick, carouselRef, onTouchStart, onTouchMove, onTouchEnd, onPrevSlide, onNextSlide, isAtStart, isAtEnd, isMobile = false, hideArrows = false })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"overflow-hidden px-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CarouselContent, {\n                settingData: settingData,\n                currentSlide: currentSlide,\n                onCardClick: onCardClick,\n                carouselRef: carouselRef,\n                onTouchStart: onTouchStart,\n                onTouchMove: onTouchMove,\n                onTouchEnd: onTouchEnd,\n                isMobile: isMobile\n            }, void 0, false, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\CarouselSection.jsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NavigationArrows__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                settingData: settingData,\n                onPrevSlide: onPrevSlide,\n                onNextSlide: onNextSlide,\n                isAtStart: isAtStart,\n                isAtEnd: isAtEnd,\n                isHidden: hideArrows\n            }, void 0, false, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\CarouselSection.jsx\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\CarouselSection.jsx\",\n        lineNumber: 87,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().memo(CarouselSection));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/CarouselSection.jsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ChatInterface.jsx":
/*!******************************************!*\
  !*** ./src/components/ChatInterface.jsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/config */ \"(ssr)/./src/utils/config.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/ChatContext */ \"(ssr)/./src/contexts/ChatContext.jsx\");\n/* harmony import */ var _hooks_useCarousel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../hooks/useCarousel */ \"(ssr)/./src/hooks/useCarousel.js\");\n/* harmony import */ var _hooks_useDeviceDetection__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../hooks/useDeviceDetection */ \"(ssr)/./src/hooks/useDeviceDetection.js\");\n/* harmony import */ var _hooks_useMessagePairing__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../hooks/useMessagePairing */ \"(ssr)/./src/hooks/useMessagePairing.js\");\n/* harmony import */ var _MessagePairs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./MessagePairs */ \"(ssr)/./src/components/MessagePairs.jsx\");\n/* harmony import */ var _CarouselSection__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./CarouselSection */ \"(ssr)/./src/components/CarouselSection.jsx\");\n/* harmony import */ var _InputSection__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./InputSection */ \"(ssr)/./src/components/InputSection.jsx\");\n/* harmony import */ var _LoadingIndicators__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./LoadingIndicators */ \"(ssr)/./src/components/LoadingIndicators.jsx\");\n/* harmony import */ var _styles_ChatInterface_css__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../styles/ChatInterface.css */ \"(ssr)/./src/styles/ChatInterface.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n// Import custom hooks\n\n\n\n// Import components\n\n\n\n\n// Import styles\n\nconst ChatInterface = ({ slug, query })=>{\n    // Context API\n    const { metadata, updateMetadata, setLoadingState, setErrorState, clearError, settingData, updateSettingData, getCustomerName, getBusinessName, hasMetadata, loading: contextLoading, error: contextError } = (0,_contexts_ChatContext__WEBPACK_IMPORTED_MODULE_3__.useChatContext)();\n    // Custom hooks\n    const carousel = (0,_hooks_useCarousel__WEBPACK_IMPORTED_MODULE_4__.useCarousel)(settingData);\n    const { isMobile, isTablet } = (0,_hooks_useDeviceDetection__WEBPACK_IMPORTED_MODULE_5__.useDeviceDetection)(carousel.resetSlide);\n    const messagePairing = (0,_hooks_useMessagePairing__WEBPACK_IMPORTED_MODULE_6__.useMessagePairing)();\n    // Local state\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showInitialUI, setShowInitialUI] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isBotTyping, setIsBotTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isBotThinking, setIsBotThinking] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sseConnection, setSseConnection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isFirstMessage, setIsFirstMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [shouldScrollToTop, setShouldScrollToTop] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Refs\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const carouselRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const desktopMessagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const mobileMessagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const tabletMessagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Debug: Log state updates when they actually happen\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (metadata && Object.keys(metadata).some({\n                \"ChatInterface.useEffect\": (key)=>metadata[key] !== null\n            }[\"ChatInterface.useEffect\"])) {\n                console.log(\"✅ METADATA STATE UPDATED:\", metadata);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        metadata\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (settingData && Object.keys(settingData).some({\n                \"ChatInterface.useEffect\": (key)=>settingData[key] !== null\n            }[\"ChatInterface.useEffect\"])) {\n                console.log(\"✅ SETTING DATA STATE UPDATED:\", settingData);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        settingData\n    ]);\n    // Cleanup pending queries that are too old (timeout mechanism)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const cleanup = setInterval({\n                \"ChatInterface.useEffect.cleanup\": ()=>{\n                    const now = Date.now();\n                    const TIMEOUT_MS = 60000; // 60 seconds timeout\n                    setPendingQueries({\n                        \"ChatInterface.useEffect.cleanup\": (prev)=>{\n                            const updated = new Map(prev);\n                            let hasChanges = false;\n                            for (const [queryId, queryData] of prev.entries()){\n                                if (now - queryData.sentAt > TIMEOUT_MS) {\n                                    updated.delete(queryId);\n                                    hasChanges = true;\n                                }\n                            }\n                            return hasChanges ? updated : prev;\n                        }\n                    }[\"ChatInterface.useEffect.cleanup\"]);\n                }\n            }[\"ChatInterface.useEffect.cleanup\"], 10000); // Check every 10 seconds\n            return ({\n                \"ChatInterface.useEffect\": ()=>clearInterval(cleanup)\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], []);\n    // Ensure carousel starts from first card on initial load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (settingData?.suggestedTopics?.length > 0) {\n                carousel.resetSlide(); // Always start from first card\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        settingData?.suggestedTopics?.length,\n        carousel\n    ]);\n    // Detect iOS for targeted fixes\n    const isIOS = ()=>{\n        if (false) {}\n        return false;\n    };\n    const username = slug;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (username && \"undefined\" !== \"undefined\") {}\n        }\n    }[\"ChatInterface.useEffect\"], [\n        username\n    ]);\n    const fetchCustomer = async (customerName)=>{\n        try {\n            setLoadingState(true);\n            clearError();\n            const response = await axios__WEBPACK_IMPORTED_MODULE_12__[\"default\"].get(`${_utils_config__WEBPACK_IMPORTED_MODULE_2__.API_ENDPOINTS.CHAT_SETTINGS}?customerName=${customerName}`);\n            // Extract data from axios response\n            const responseData = response.data;\n            const metadataFromResponse = responseData.metaData;\n            updateSettingData(responseData);\n            updateMetadata(metadataFromResponse);\n            if (responseData && responseData.customerName) {\n                //  localStorage for backward compatibility\n                localStorage.setItem(\"customerName_userId\", responseData.customerName);\n                localStorage.setItem(\"BusinessName\", responseData.businessName);\n                window.dispatchEvent(new CustomEvent(\"businessNameLoaded\", {\n                    detail: {\n                        businessName: responseData.businessName\n                    }\n                }));\n                const existingUserId = localStorage.getItem(\"userID\");\n                if (existingUserId) {\n                    await fetchExistingMessages(existingUserId);\n                } else {\n                    await initializeChatSession();\n                }\n            } else {\n                await initializeChatSession();\n            }\n        } catch (error) {\n            console.error(\"Error fetching customer data:\", error);\n            setErrorState(\"Failed to load customer settings\");\n            // Fallback metadata\n            updateMetadata({\n                customerName: customerName,\n                businessName: \"Driply\"\n            });\n            localStorage.setItem(\"BusinessName\", \"Driply\");\n            window.dispatchEvent(new CustomEvent(\"businessNameLoaded\", {\n                detail: {\n                    businessName: \"Driply\"\n                }\n            }));\n            await initializeChatSession();\n        } finally{\n            setLoadingState(false);\n        }\n    };\n    const fetchExistingMessages = async (userId)=>{\n        // console.log(\"fetchExistingMessages\");\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_12__[\"default\"].get(`${_utils_config__WEBPACK_IMPORTED_MODULE_2__.API_ENDPOINTS.CHAT_MESSAGES}?userId=${userId}`);\n            if (response.data && response.data.length > 0) {\n                // Load existing messages into the message pairing system\n                response.data.forEach((msg)=>{\n                    messagePairing.addMessage({\n                        text: msg.message,\n                        timestamp: new Date(msg.createdAt).getTime(),\n                        type: msg.type,\n                        source: msg.source\n                    });\n                });\n            }\n            connectToSSE(userId);\n        } catch (error) {\n            await initializeChatSession();\n        }\n    };\n    const initializeChatSession = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_12__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_2__.API_ENDPOINTS.CHAT_INIT, {\n                customerName: username\n            });\n            if (response.data && response.data.userId) {\n                localStorage.setItem(\"userId\", response.data.userId);\n                localStorage.setItem(\"userID\", response.data._id);\n                await fetchExistingMessages(response.data._id);\n            }\n        } catch (error) {\n            if (!localStorage.getItem(\"BusinessName\")) {\n                localStorage.setItem(\"BusinessName\", \"Driply\");\n                window.dispatchEvent(new CustomEvent(\"businessNameLoaded\", {\n                    detail: {\n                        businessName: \"Driply\"\n                    }\n                }));\n            }\n        }\n    };\n    const connectToSSE = (userId)=>{\n        try {\n            if (sseConnection) {\n                sseConnection.close();\n            }\n            const sseUrl = (0,_utils_config__WEBPACK_IMPORTED_MODULE_2__.getSSEUrl)(userId);\n            const eventSource = new EventSource(sseUrl);\n            setSseConnection(eventSource);\n            eventSource.addEventListener(\"message\", ({ data })=>{\n                try {\n                    const contents = JSON.parse(data);\n                    handleSSEMessage(contents);\n                } catch (error) {\n                // Handle parsing error silently\n                }\n            });\n            eventSource.addEventListener(\"error\", ()=>{\n            // Handle connection error silently\n            });\n        } catch (error) {\n        // Handle SSE connection error silently\n        }\n    };\n    const handleSSEMessage = (data)=>{\n        const subtype = data.subType || data.subtype || data.type || \"UNKNOWN\";\n        const content = data.content || \"\";\n        const message = data.message || data.text || \"\";\n        if ([\n            \"TYPING\",\n            \"THINKING\",\n            \"BEHAVIOUR_MESSAGE\"\n        ].includes(subtype)) {\n            switch(subtype){\n                case \"TYPING\":\n                    setIsBotTyping(true);\n                    setIsBotThinking(false);\n                    break;\n                case \"THINKING\":\n                    setIsBotThinking(true);\n                    setIsBotTyping(false);\n                    break;\n                case \"BEHAVIOUR_MESSAGE\":\n                    setIsBotTyping(false);\n                    setIsBotThinking(false);\n                    break;\n            }\n            return;\n        }\n        const displayText = message || content;\n        if (!displayText) return;\n        const displayType = [\n            \"TEXT\",\n            \"MESSAGE\",\n            \"DATA_MESSAGE\"\n        ].includes(subtype) ? subtype : \"UNKNOWN\";\n        // Enhanced message pairing logic\n        const responseToId = data.queryId || data.responseToId || data.correlationId;\n        let queryId = responseToId;\n        // If no explicit queryId, find the most recent pending query\n        if (!queryId) {\n            const pendingEntries = Array.from(messagePairing.pendingQueries.entries()).sort((a, b)=>b[1].timestamp - a[1].timestamp);\n            if (pendingEntries.length > 0) {\n                queryId = pendingEntries[0][0];\n            }\n        }\n        // Create bot message with proper pairing information\n        const botMessage = messagePairing.addBotMessage(displayText, queryId, false);\n        // The messagePairing hook handles pending query cleanup automatically\n        setIsBotTyping(false);\n        setIsBotThinking(false);\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (message.trim()) {\n            const userMessage = message.trim();\n            const isFirstMsg = messages.length === 0;\n            // Generate unique ID for this query\n            const queryId = messagePairing.createMessageId();\n            const userMessageObj = messagePairing.addUserMessage(userMessage, queryId);\n            // The messagePairing hook handles message state automatically\n            // Track this query as pending response\n            setPendingQueries((prev)=>new Map(prev).set(queryId, {\n                    id: queryId,\n                    message: userMessage,\n                    timestamp: userMessageObj.timestamp,\n                    sentAt: Date.now()\n                }));\n            setMessage(\"\");\n            setIsTyping(false);\n            // For first message, set flag to scroll to top\n            if (isFirstMsg) {\n                setShouldScrollToTop(true);\n                setIsFirstMessage(false);\n            }\n            // Show loading dots immediately after sending message\n            setIsBotThinking(true);\n            setIsBotTyping(false);\n            // Reset textarea\n            if (inputRef.current) {\n                inputRef.current.style.height = \"104px\";\n                inputRef.current.scrollTop = 0;\n                inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n                inputRef.current.classList.add(\"reset-height\");\n            }\n            try {\n                const userId = localStorage.getItem(\"userID\");\n                if (userId) {\n                    await axios__WEBPACK_IMPORTED_MODULE_12__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_2__.API_ENDPOINTS.CHAT_SEND, {\n                        userId: userId,\n                        message: userMessage,\n                        queryId: queryId,\n                        typs: \"TEXT\",\n                        source: \"USER\",\n                        isTest: query.isTest === \"1\" ? true : false\n                    });\n                }\n            } catch (error) {\n                // Hide loading dots on error\n                setIsBotThinking(false);\n                setIsBotTyping(false);\n                const errorMessage = messagePairing.addBotMessage(\"Sorry, there was an error sending your message. Please try again.\", queryId, true // isError = true\n                );\n                // The messagePairing hook handles message state automatically\n                // Remove from pending queries since we got an error\n                setPendingQueries((prev)=>{\n                    const updated = new Map(prev);\n                    updated.delete(queryId);\n                    return updated;\n                });\n            }\n        }\n    };\n    // Input handling is now managed by InputSection component\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messagePairing.messages.length > 0 && inputRef.current && !message) {\n                const timer = setTimeout({\n                    \"ChatInterface.useEffect.timer\": ()=>{\n                        if (inputRef.current) {\n                            inputRef.current.style.height = \"104px\";\n                            inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n                            inputRef.current.classList.add(\"reset-height\");\n                        }\n                    }\n                }[\"ChatInterface.useEffect.timer\"], 50);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                })[\"ChatInterface.useEffect\"];\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messagePairing.messages,\n        message\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messagePairing.messages.length > 0) {\n                handleChatGPTScroll();\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messagePairing.messages,\n        isBotTyping,\n        isBotThinking\n    ]);\n    const handleChatGPTScroll = ()=>{\n        setTimeout(()=>{\n            // Determine screen type and get appropriate container\n            const screenWidth = window.innerWidth;\n            const isDesktopLayout = screenWidth >= 1024;\n            const isTabletLayout = screenWidth >= 768 && screenWidth < 1024;\n            let container;\n            if (isDesktopLayout) {\n                container = desktopMessagesContainerRef.current;\n            } else if (isTabletLayout) {\n                container = tabletMessagesContainerRef.current;\n            } else {\n                container = mobileMessagesContainerRef.current;\n            }\n            if (!container) return;\n            if (shouldScrollToTop) {\n                // For first message, scroll to top to show the message there\n                container.scrollTo({\n                    top: 0,\n                    behavior: \"smooth\"\n                });\n                setShouldScrollToTop(false);\n            } else {\n                // Different scrolling behavior for each screen type\n                const messageElements = container.querySelectorAll(\".message-pair\");\n                if (messageElements.length > 0) {\n                    const lastMessagePair = messageElements[messageElements.length - 1];\n                    const containerHeight = container.clientHeight;\n                    const pairHeight = lastMessagePair.offsetHeight;\n                    const pairTop = lastMessagePair.offsetTop;\n                    if (isTabletLayout) {\n                        // Tablet-specific scrolling: Keep messages visible by scrolling to bottom of container\n                        // This ensures both user message and AI response remain visible\n                        const scrollPosition = Math.max(0, pairTop + pairHeight - containerHeight + 100);\n                        container.scrollTo({\n                            top: scrollPosition,\n                            behavior: \"smooth\"\n                        });\n                    } else {\n                        // Desktop and mobile: Center the message pair on screen\n                        const scrollPosition = pairTop - (containerHeight - pairHeight) / 2;\n                        container.scrollTo({\n                            top: Math.max(0, scrollPosition),\n                            behavior: \"smooth\"\n                        });\n                    }\n                }\n            }\n        }, 150);\n    };\n    const renderMessagePairs = ()=>{\n        // Use the enhanced message pairing logic from custom hook\n        const messagePairs = messagePairing.getMessagePairs();\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MessagePairs__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            messagePairs: messagePairs,\n            isBotTyping: isBotTyping,\n            isBotThinking: isBotThinking,\n            isMobile: isMobile\n        }, void 0, false, {\n            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n            lineNumber: 483,\n            columnNumber: 7\n        }, undefined);\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSubmit(e);\n        }\n    };\n    const handleSuggestionClick = async (cardTitle)=>{\n        const isFirstMsg = messagePairing.messages.length === 0;\n        // Generate unique ID for this suggestion query\n        const queryId = messagePairing.createMessageId();\n        const userMessageObj = messagePairing.addUserMessage(cardTitle, queryId);\n        // Mark as suggestion\n        userMessageObj.isSuggestion = true;\n        // The messagePairing hook handles message state and pending queries automatically\n        setMessage(\"\");\n        setIsTyping(false);\n        // For first message, set flag to scroll to top\n        if (isFirstMsg) {\n            setShouldScrollToTop(true);\n            setIsFirstMessage(false);\n        }\n        // Show loading dots immediately after sending message\n        setIsBotThinking(true);\n        setIsBotTyping(false);\n        if (inputRef.current) {\n            inputRef.current.style.height = \"104px\";\n            inputRef.current.scrollTop = 0;\n            inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n            inputRef.current.classList.add(\"reset-height\");\n        }\n        try {\n            const userId = localStorage.getItem(\"userID\");\n            if (userId) {\n                await axios__WEBPACK_IMPORTED_MODULE_12__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_2__.API_ENDPOINTS.CHAT_SEND, {\n                    userId: userId,\n                    message: cardTitle,\n                    queryId: queryId,\n                    customerName: username,\n                    isSuggestion: true\n                });\n            }\n        } catch (error) {\n            // Hide loading dots on error\n            setIsBotThinking(false);\n            setIsBotTyping(false);\n            const errorMessage = messagePairing.addBotMessage(\"Sorry, there was an error sending your message. Please try again.\", queryId, true // isError = true\n            );\n        // The messagePairing hook handles message state and pending queries automatically\n        }\n    };\n    const getMaxSlides = ()=>{\n        if (isMobile) return settingData?.suggestedTopics.length;\n        return Math.ceil(settingData?.suggestedTopics.length / 2);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messagePairing.messages.length === 0) {\n                setShowInitialUI(false);\n                const timer = setTimeout({\n                    \"ChatInterface.useEffect.timer\": ()=>setShowInitialUI(true)\n                }[\"ChatInterface.useEffect.timer\"], 60);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                })[\"ChatInterface.useEffect\"];\n            } else {\n                setShowInitialUI(false);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messagePairing.messages.length\n    ]);\n    // Screen size detection is now handled by useDeviceDetection hook\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (inputRef.current && messagePairing.messages.length === 0) {\n                const shouldAutoFocus = window.innerWidth >= 768;\n                if (shouldAutoFocus) {\n                    const timer = setTimeout({\n                        \"ChatInterface.useEffect.timer\": ()=>{\n                            inputRef.current?.focus();\n                        }\n                    }[\"ChatInterface.useEffect.timer\"], 100);\n                    return ({\n                        \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                    })[\"ChatInterface.useEffect\"];\n                }\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messagePairing.messages.length\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            return ({\n                \"ChatInterface.useEffect\": ()=>{\n                    if (sseConnection) {\n                        sseConnection.close();\n                    }\n                }\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], [\n        sseConnection\n    ]);\n    // Check if settingData has been populated with actual data\n    const hasSettingData = settingData && Object.keys(settingData).some((key)=>settingData[key] !== null);\n    // Show loading state while waiting for data\n    // if (!hasSettingData) {\n    //   return (\n    //     <div className=\"bg-white flex flex-col min-h-screen items-center justify-center\">\n    //       <div className=\"text-center\">\n    //         <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4\"></div>\n    //         <p className=\"text-gray-600\">Loading chat settings...</p>\n    //         {contextError && (\n    //           <p className=\"text-red-500 mt-2\">Error: {contextError}</p>\n    //         )}\n    //       </div>\n    //     </div>\n    //   );\n    // }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:flex flex-1 flex-col px-4 \",\n                children: messagePairing.messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-1 items-center justify-center min-h-[calc(100vh-64px)] mt-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center w-[768px] justify-center mx-auto \",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: `text-4xl text-gray-900 mb-6 text-center transition-opacity duration-500 ${showInitialUI ? \"opacity-100\" : \"opacity-0\"}`,\n                                style: {\n                                    transitionDelay: showInitialUI ? \"40ms\" : \"0ms\"\n                                },\n                                children: \"How can I help you?\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 621,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `relative w-full mb-6 flex flex-col items-center transition-all duration-500 ${showInitialUI ? \"opacity-100 \" : \"opacity-0\"}`,\n                                style: {\n                                    transitionDelay: showInitialUI ? \"80ms\" : \"0ms\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InputSection__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    message: message,\n                                    onMessageChange: setMessage,\n                                    onSubmit: handleSubmit,\n                                    isDisabled: false,\n                                    isMobile: isMobile,\n                                    placeholder: \"Ask anything\",\n                                    showPoweredBy: false\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 638,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 630,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `relative w-full max-w-2xl transition-all duration-200 ease-in-out ${isTyping ? \"opacity-100 pointer-events-none\" // TC1\n                                 : showInitialUI ? \"opacity-100 \" : \"opacity-0\"}`,\n                                style: {\n                                    transitionDelay: showInitialUI ? \"40ms\" : \"0ms\",\n                                    zIndex: 10\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CarouselSection__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    settingData: settingData,\n                                    currentSlide: carousel.currentSlide,\n                                    onCardClick: handleSuggestionClick,\n                                    carouselRef: carouselRef,\n                                    onTouchStart: carousel.handleTouchStart,\n                                    onTouchMove: carousel.handleTouchMove,\n                                    onTouchEnd: carousel.handleTouchEnd,\n                                    onPrevSlide: carousel.prevSlide,\n                                    onNextSlide: carousel.nextSlide,\n                                    isAtStart: carousel.isAtStart,\n                                    isAtEnd: carousel.isAtEnd,\n                                    isMobile: isMobile\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 662,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 649,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 620,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                    lineNumber: 619,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: desktopMessagesContainerRef,\n                            className: \"flex-1 overflow-y-auto max-h-[calc(100vh-200px)] mt-14\",\n                            style: {\n                                overscrollBehavior: \"contain\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full lg:w-[768px] mx-auto px-4 h-fit \",\n                                children: [\n                                    renderMessagePairs(),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: messagesEndRef\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 688,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 686,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 681,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed bottom-0 left-0 right-0 p-4 mt-5\",\n                            style: {\n                                zIndex: 1000,\n                                marginTop: \"20px\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InputSection__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                message: message,\n                                onMessageChange: setMessage,\n                                onSubmit: handleSubmit,\n                                isDisabled: false,\n                                isMobile: isMobile,\n                                placeholder: \"Ask anything\",\n                                showPoweredBy: true\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 696,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 692,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 617,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden overflow-hidden fixed inset-0 flex flex-col  mt-10 \",\n                children: [\n                    messagePairing.messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center pt-15 pb-4 px-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden md:flex lg:hidden flex-col items-center\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 715,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 714,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center justify-center flex-1 px-4\",\n                                style: {\n                                    paddingBottom: \"260px\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: `text-2xl md:text-4xl text-gray-900 text-center leading-relaxed transition-all duration-700 ${showInitialUI ? \"opacity-100 \" : \"opacity-0 \"}`,\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"40ms\" : \"0ms\"\n                                    },\n                                    children: \"How can I help you?\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 721,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 717,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: mobileMessagesContainerRef,\n                                className: \"md:hidden flex-1 overflow-y-auto max-h-[calc(100vh-190px)] hide-scrollbar\",\n                                style: {\n                                    overscrollBehavior: \"contain\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full max-w-[803px] mx-auto px-4  pt-12\",\n                                    children: [\n                                        renderMessagePairs(),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: messagesEndRef\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 743,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 741,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 736,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: tabletMessagesContainerRef,\n                                className: \"hidden md:block lg:hidden flex-1 overflow-y-auto max-h-[calc(100vh-100px)] pb-[140px] hide-scrollbar\",\n                                style: {\n                                    overscrollBehavior: \"contain\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full max-w-[803px] mx-auto px-4 pb-6 pt-12\",\n                                    children: [\n                                        renderMessagePairs(),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: messagesEndRef\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 755,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 753,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 748,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed bottom-0 left-0 right-0 bg-white\",\n                                style: {\n                                    minHeight: \"160px\",\n                                    zIndex: 1000,\n                                    paddingBottom: \"env(safe-area-inset-bottom, 0)\",\n                                    transform: \"translateZ(0)\",\n                                    backfaceVisibility: \"hidden\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InputSection__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    message: message,\n                                    onMessageChange: setMessage,\n                                    onSubmit: handleSubmit,\n                                    isDisabled: false,\n                                    isMobile: isMobile,\n                                    placeholder: \"Ask anything\",\n                                    showPoweredBy: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 769,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 759,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-0 left-0 right-0 bg-white\",\n                        children: messagePairing.messages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `${isMobile ? \"px-0\" : \"px-4\"} pt-2 pb-2 transition-all duration-500 ease-in-out ${isTyping ? \"opacity-0 pointer-events-none\" : showInitialUI ? \"opacity-100\" : \"opacity-0\"}`,\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"120ms\" : \"0ms\",\n                                        position: \"relative\",\n                                        zIndex: 10,\n                                        backgroundColor: \"white\",\n                                        paddingBottom: \"10px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"overflow-hidden px-4\",\n                                            children: !settingData?.suggestedTopics || settingData.suggestedTopics.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center items-center py-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                    lineNumber: 809,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 808,\n                                                columnNumber: 21\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                ref: carouselRef,\n                                                className: \"flex gap-4 transition-transform duration-300 ease-in-out\",\n                                                style: {\n                                                    transform: `translateX(-${carousel.currentSlide * 100}%)`\n                                                },\n                                                onTouchStart: carousel.handleTouchStart,\n                                                onTouchMove: carousel.handleTouchMove,\n                                                onTouchEnd: carousel.handleTouchEnd,\n                                                children: settingData.suggestedTopics.map((card, cardIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleSuggestionClick(card.question),\n                                                        className: `${isMobile ? \"py-3 mt-3\" : \"py-3\"} px-4 rounded-[8px] bg-[#F6F6F6] text-left hover:bg-[#D6EDFF] transition-all duration-200 flex-shrink-0 touch-manipulation`,\n                                                        style: {\n                                                            // Calculate width accounting for gap (gap-4 = 16px)\n                                                            width: \"calc(100% - 16px)\",\n                                                            minHeight: isMobile ? \"90px\" : \"80px\"\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: `${isMobile ? \"text-[16px]\" : \"text-[16px]\"} font-[600] text-black mb-1 leading-snug break-words word-wrap overflow-wrap-anywhere`,\n                                                                children: card.question\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                lineNumber: 835,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: `${isMobile ? \"text-[14px]\" : \"text-[14px]\"} text-gray-500 leading-snug break-words word-wrap overflow-wrap-anywhere`,\n                                                                children: card.subtitle\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                lineNumber: 842,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, cardIndex, true, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 823,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 812,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 805,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        settingData?.suggestedTopics && settingData.suggestedTopics.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handlePrevSlide,\n                                                    disabled: isAtStart,\n                                                    className: `hidden md:flex absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-10 w-8 h-8 border rounded-full items-center justify-center shadow-md transition-all duration-200 touch-manipulation z-10 ${isAtStart ? \"bg-gray-100 border-gray-300 cursor-not-allowed\" : \"bg-white border-gray-200 hover:bg-gray-50 cursor-pointer\"}`,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FaChevronLeft, {\n                                                        className: `w-3 h-3 ${isAtStart ? \"text-gray-400\" : \"text-gray-600\"}`\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 867,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                    lineNumber: 858,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleNextSlide,\n                                                    disabled: isAtEnd(),\n                                                    className: `hidden md:flex absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-10 w-8 h-8 border rounded-full items-center justify-center shadow-md transition-all duration-200 touch-manipulation z-10 ${isAtEnd() ? \"bg-gray-100 border-gray-300 cursor-not-allowed\" : \"bg-white border-gray-200 hover:bg-gray-50 cursor-pointer\"}`,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FaChevronRight, {\n                                                        className: `w-3 h-3 ${isAtEnd() ? \"text-gray-400\" : \"text-gray-600\"}`\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 882,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                    lineNumber: 873,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 787,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `px-4 bg-white transition-all duration-500 ${showInitialUI ? \"opacity-100 translate-y-0\" : \"translate-y-4\"}`,\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"200ms\" : \"0ms\",\n                                        position: \"relative\",\n                                        zIndex: 5,\n                                        paddingBottom: \"env(safe-area-inset-bottom)\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InputSection__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        message: message,\n                                        onMessageChange: setMessage,\n                                        onSubmit: handleSubmit,\n                                        isDisabled: false,\n                                        isMobile: isMobile,\n                                        placeholder: \"Ask anything\",\n                                        showPoweredBy: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 904,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 893,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 783,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 711,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n        lineNumber: 615,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatInterface);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ChatInterface.jsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ChatInterfaceWrapper.jsx":
/*!*************************************************!*\
  !*** ./src/components/ChatInterfaceWrapper.jsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatInterfaceWrapper)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ChatInterface__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ChatInterface */ \"(ssr)/./src/components/ChatInterface.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n/**\r\n * Client-side wrapper for ChatInterface component\r\n * This separates client components from server components to fix metadata issues\r\n * Following Next.js App Router best practices\r\n */ function ChatInterfaceWrapper({ slug, query }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatInterface__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        slug: slug,\n        query: query\n    }, void 0, false, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterfaceWrapper.jsx\",\n        lineNumber: 12,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9DaGF0SW50ZXJmYWNlV3JhcHBlci5qc3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUUwQjtBQUNrQjtBQUU1Qzs7OztDQUlDLEdBQ2MsU0FBU0UscUJBQXFCLEVBQUVDLElBQUksRUFBRUMsS0FBSyxFQUFFO0lBQzFELHFCQUFPLDhEQUFDSCxzREFBYUE7UUFBQ0UsTUFBTUE7UUFBTUMsT0FBT0E7Ozs7OztBQUMzQyIsInNvdXJjZXMiOlsiRDpcXExhcHRvcCBkYXRhXFxEUklQTFktQ0hBVFxcY2hhdC1uZXh0XFxzcmNcXGNvbXBvbmVudHNcXENoYXRJbnRlcmZhY2VXcmFwcGVyLmpzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcclxuXHJcbmltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCBDaGF0SW50ZXJmYWNlIGZyb20gJy4vQ2hhdEludGVyZmFjZSc7XHJcblxyXG4vKipcclxuICogQ2xpZW50LXNpZGUgd3JhcHBlciBmb3IgQ2hhdEludGVyZmFjZSBjb21wb25lbnRcclxuICogVGhpcyBzZXBhcmF0ZXMgY2xpZW50IGNvbXBvbmVudHMgZnJvbSBzZXJ2ZXIgY29tcG9uZW50cyB0byBmaXggbWV0YWRhdGEgaXNzdWVzXHJcbiAqIEZvbGxvd2luZyBOZXh0LmpzIEFwcCBSb3V0ZXIgYmVzdCBwcmFjdGljZXNcclxuICovXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIENoYXRJbnRlcmZhY2VXcmFwcGVyKHsgc2x1ZywgcXVlcnkgfSkge1xyXG4gIHJldHVybiA8Q2hhdEludGVyZmFjZSBzbHVnPXtzbHVnfSBxdWVyeT17cXVlcnl9IC8+O1xyXG59XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkNoYXRJbnRlcmZhY2UiLCJDaGF0SW50ZXJmYWNlV3JhcHBlciIsInNsdWciLCJxdWVyeSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ChatInterfaceWrapper.jsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ClientWrapper.jsx":
/*!******************************************!*\
  !*** ./src/components/ClientWrapper.jsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClientWrapper)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Navbar */ \"(ssr)/./src/components/Navbar.jsx\");\n/* harmony import */ var _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/ChatContext */ \"(ssr)/./src/contexts/ChatContext.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n/**\r\n * Client wrapper component that contains all client-side logic\r\n * This separates client components from server components to fix metadata issues\r\n * Following Next.js App Router best practices\r\n */ function ClientWrapper({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_ChatContext__WEBPACK_IMPORTED_MODULE_3__.ChatProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ClientWrapper.jsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ClientWrapper.jsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9DbGllbnRXcmFwcGVyLmpzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUUwQjtBQUNJO0FBQ3lCO0FBRXZEOzs7O0NBSUMsR0FDYyxTQUFTRyxjQUFjLEVBQUVDLFFBQVEsRUFBRTtJQUNoRCxxQkFDRSw4REFBQ0YsK0RBQVlBOzswQkFDWCw4REFBQ0QsK0NBQU1BOzs7OztZQUNORzs7Ozs7OztBQUdQIiwic291cmNlcyI6WyJEOlxcTGFwdG9wIGRhdGFcXERSSVBMWS1DSEFUXFxjaGF0LW5leHRcXHNyY1xcY29tcG9uZW50c1xcQ2xpZW50V3JhcHBlci5qc3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XHJcblxyXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgTmF2YmFyIGZyb20gXCIuL05hdmJhclwiO1xyXG5pbXBvcnQgeyBDaGF0UHJvdmlkZXIgfSBmcm9tIFwiLi4vY29udGV4dHMvQ2hhdENvbnRleHRcIjtcclxuXHJcbi8qKlxyXG4gKiBDbGllbnQgd3JhcHBlciBjb21wb25lbnQgdGhhdCBjb250YWlucyBhbGwgY2xpZW50LXNpZGUgbG9naWNcclxuICogVGhpcyBzZXBhcmF0ZXMgY2xpZW50IGNvbXBvbmVudHMgZnJvbSBzZXJ2ZXIgY29tcG9uZW50cyB0byBmaXggbWV0YWRhdGEgaXNzdWVzXHJcbiAqIEZvbGxvd2luZyBOZXh0LmpzIEFwcCBSb3V0ZXIgYmVzdCBwcmFjdGljZXNcclxuICovXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIENsaWVudFdyYXBwZXIoeyBjaGlsZHJlbiB9KSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxDaGF0UHJvdmlkZXI+XHJcbiAgICAgIDxOYXZiYXIgLz5cclxuICAgICAge2NoaWxkcmVufVxyXG4gICAgPC9DaGF0UHJvdmlkZXI+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJOYXZiYXIiLCJDaGF0UHJvdmlkZXIiLCJDbGllbnRXcmFwcGVyIiwiY2hpbGRyZW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ClientWrapper.jsx\n");

/***/ }),

/***/ "(ssr)/./src/components/InputSection.jsx":
/*!*****************************************!*\
  !*** ./src/components/InputSection.jsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FaArrowUp_react_icons_fa6__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FaArrowUp!=!react-icons/fa6 */ \"(ssr)/./node_modules/react-icons/fa6/index.mjs\");\n/* harmony import */ var _utils_messageUtils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/messageUtils */ \"(ssr)/./src/utils/messageUtils.js\");\n/* harmony import */ var _utils_deviceUtils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/deviceUtils */ \"(ssr)/./src/utils/deviceUtils.js\");\n/**\n * InputSection Component\n * Handles message input, form submission, and validation\n */ \n\n\n\n\nconst InputSection = ({ message, onMessageChange, onSubmit, isDisabled = false, isMobile = false, placeholder = \"Type your message...\", showPoweredBy = true })=>{\n    const settings = (0,_utils_deviceUtils__WEBPACK_IMPORTED_MODULE_3__.getInputSettings)(isMobile);\n    const isMessageValid = (0,_utils_messageUtils__WEBPACK_IMPORTED_MODULE_2__.validateMessage)(message);\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (isMessageValid && !isDisabled) {\n            onSubmit(message);\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleSubmit(e);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${isMobile ? 'px-4' : 'px-4'} bg-white transition-all duration-500`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                        value: message,\n                        onChange: (e)=>onMessageChange(e.target.value),\n                        onKeyPress: handleKeyPress,\n                        placeholder: placeholder,\n                        disabled: isDisabled,\n                        className: `w-full ${settings.padding} pr-12 border border-gray-300 ${settings.borderRadius} resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${settings.fontSize}`,\n                        style: {\n                            minHeight: \"48px\",\n                            maxHeight: \"120px\",\n                            overflowY: \"auto\"\n                        },\n                        rows: 1\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\InputSection.jsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"submit\",\n                        disabled: !isMessageValid || isDisabled,\n                        className: `absolute right-3 bottom-3 w-9 h-9 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation ${!isMessageValid || isDisabled ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_react_icons_fa6__WEBPACK_IMPORTED_MODULE_4__.FaArrowUp, {\n                            className: \"w-4 h-4 text-white\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\InputSection.jsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\InputSection.jsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\InputSection.jsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, undefined),\n            showPoweredBy && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xs text-gray-500\",\n                    children: [\n                        \"This chat is powered by\",\n                        \" \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            className: \"text-black\",\n                            children: \"Driply.me\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\InputSection.jsx\",\n                            lineNumber: 71,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\InputSection.jsx\",\n                    lineNumber: 69,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\InputSection.jsx\",\n                lineNumber: 68,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\InputSection.jsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().memo(InputSection));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/InputSection.jsx\n");

/***/ }),

/***/ "(ssr)/./src/components/LoadingIndicators.jsx":
/*!**********************************************!*\
  !*** ./src/components/LoadingIndicators.jsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CarouselLoadingSpinner: () => (/* binding */ CarouselLoadingSpinner),\n/* harmony export */   GlobalLoadingIndicator: () => (/* binding */ GlobalLoadingIndicator),\n/* harmony export */   MessagePairLoading: () => (/* binding */ MessagePairLoading),\n/* harmony export */   TypingDots: () => (/* binding */ TypingDots),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/**\n * LoadingIndicators Component\n * Handles various loading states including typing dots and carousel loading\n */ \n\n// Carousel loading spinner\nconst CarouselLoadingSpinner = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex justify-center items-center py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\LoadingIndicators.jsx\",\n            lineNumber: 11,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\LoadingIndicators.jsx\",\n        lineNumber: 10,\n        columnNumber: 3\n    }, undefined);\n// Typing dots animation for bot responses\nconst TypingDots = ({ isMobile = false })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `bg-white ${isMobile ? 'px-3 py-2 rounded-[15px]' : 'px-4 py-3 rounded-3xl'}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex space-x-2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-1 h-1 bg-black rounded-full loading-dot\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\LoadingIndicators.jsx\",\n                    lineNumber: 19,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-1 h-1 bg-black rounded-full loading-dot\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\LoadingIndicators.jsx\",\n                    lineNumber: 20,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-1 h-1 bg-black rounded-full loading-dot\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\LoadingIndicators.jsx\",\n                    lineNumber: 21,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\LoadingIndicators.jsx\",\n            lineNumber: 18,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\LoadingIndicators.jsx\",\n        lineNumber: 17,\n        columnNumber: 3\n    }, undefined);\n// Global loading indicator for current typing/thinking state\nconst GlobalLoadingIndicator = ({ isBotTyping, isBotThinking, isMobile = false })=>{\n    if (!isBotTyping && !isBotThinking) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex justify-start\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TypingDots, {\n            isMobile: isMobile\n        }, void 0, false, {\n            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\LoadingIndicators.jsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\LoadingIndicators.jsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, undefined);\n};\n// Loading state for message pairs\nconst MessagePairLoading = ({ isLastPair, isBotTyping, isBotThinking, isMobile = false })=>{\n    if (!isLastPair || !isBotTyping && !isBotThinking) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex justify-start\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TypingDots, {\n            isMobile: isMobile\n        }, void 0, false, {\n            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\LoadingIndicators.jsx\",\n            lineNumber: 52,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\LoadingIndicators.jsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, undefined);\n};\nconst LoadingIndicators = {\n    CarouselLoadingSpinner,\n    TypingDots,\n    GlobalLoadingIndicator,\n    MessagePairLoading\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LoadingIndicators);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/LoadingIndicators.jsx\n");

/***/ }),

/***/ "(ssr)/./src/components/MessagePairs.jsx":
/*!*****************************************!*\
  !*** ./src/components/MessagePairs.jsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _LoadingIndicators__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./LoadingIndicators */ \"(ssr)/./src/components/LoadingIndicators.jsx\");\n/* harmony import */ var _utils_deviceUtils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/deviceUtils */ \"(ssr)/./src/utils/deviceUtils.js\");\n/* harmony import */ var _utils_messageUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/messageUtils */ \"(ssr)/./src/utils/messageUtils.js\");\n/**\n * MessagePairs Component\n * Handles message rendering and pairing logic\n */ \n\n\n\n\nconst UserMessage = ({ message, isMobile = false })=>{\n    const settings = (0,_utils_deviceUtils__WEBPACK_IMPORTED_MODULE_3__.getMessageContainerSettings)(isMobile);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex justify-end\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-end space-x-2\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `text-[16px] font-[400] text-white bg-black ${settings.borderRadius} ${settings.padding}`,\n                style: {\n                    overflowWrap: \"break-word\",\n                    maxWidth: settings.maxWidth\n                },\n                children: [\n                    (0,_utils_messageUtils__WEBPACK_IMPORTED_MODULE_4__.getMessageDisplayText)(message),\n                    message.isSuggestion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs text-gray-300 ml-2\",\n                        children: \"\\uD83D\\uDCA1\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\MessagePairs.jsx\",\n                        lineNumber: 25,\n                        columnNumber: 36\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\MessagePairs.jsx\",\n                lineNumber: 17,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\MessagePairs.jsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\MessagePairs.jsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, undefined);\n};\nconst BotMessage = ({ message, isMobile = false })=>{\n    const settings = (0,_utils_deviceUtils__WEBPACK_IMPORTED_MODULE_3__.getMessageContainerSettings)(isMobile);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex justify-start\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `text-[16px] font-[400] rounded-3xl hyphens-auto bg-white ${settings.borderRadius} ${settings.padding}`,\n            style: {\n                overflowWrap: \"break-word\",\n                maxWidth: settings.maxWidth\n            },\n            children: [\n                (0,_utils_messageUtils__WEBPACK_IMPORTED_MODULE_4__.getMessageDisplayText)(message),\n                message.isError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-xs text-red-500 ml-2\",\n                    children: \"⚠️\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\MessagePairs.jsx\",\n                    lineNumber: 45,\n                    columnNumber: 29\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\MessagePairs.jsx\",\n            lineNumber: 37,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\MessagePairs.jsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, undefined);\n};\nconst MessagePair = ({ pair, index, totalPairs, isBotTyping, isBotThinking, isMobile = false })=>{\n    const isLastPair = index === totalPairs - 1;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"message-pair flex flex-col justify-start\",\n        \"data-pair-id\": pair.id,\n        \"data-is-complete\": pair.isComplete,\n        \"data-is-pending\": pair.isPending,\n        style: {\n            minHeight: isLastPair ? isMobile ? \"calc(100vh - 200px)\" // Mobile-specific height for newest message\n             : \"calc(100vh - 200px)\" // Desktop/tablet height for newest message\n             : \"auto\"\n        },\n        children: [\n            pair.user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UserMessage, {\n                    message: pair.user,\n                    isMobile: isMobile\n                }, void 0, false, {\n                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\MessagePairs.jsx\",\n                    lineNumber: 79,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\MessagePairs.jsx\",\n                lineNumber: 78,\n                columnNumber: 9\n            }, undefined),\n            pair.bot && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-start\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BotMessage, {\n                    message: pair.bot,\n                    isMobile: isMobile\n                }, void 0, false, {\n                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\MessagePairs.jsx\",\n                    lineNumber: 86,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\MessagePairs.jsx\",\n                lineNumber: 85,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LoadingIndicators__WEBPACK_IMPORTED_MODULE_2__.MessagePairLoading, {\n                isLastPair: isLastPair,\n                isBotTyping: isBotTyping,\n                isBotThinking: isBotThinking,\n                isMobile: isMobile\n            }, void 0, false, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\MessagePairs.jsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, `pair-${pair.id}`, true, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\MessagePairs.jsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, undefined);\n};\nconst MessagePairs = ({ messagePairs, isBotTyping, isBotThinking, isMobile = false })=>{\n    if (!messagePairs || messagePairs.length === 0) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"message-pairs-container\",\n        children: messagePairs.map((pair, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessagePair, {\n                pair: pair,\n                index: index,\n                totalPairs: messagePairs.length,\n                isBotTyping: isBotTyping,\n                isBotThinking: isBotThinking,\n                isMobile: isMobile\n            }, pair.id, false, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\MessagePairs.jsx\",\n                lineNumber: 114,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\MessagePairs.jsx\",\n        lineNumber: 112,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().memo(MessagePairs));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/MessagePairs.jsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Navbar.jsx":
/*!***********************************!*\
  !*** ./src/components/Navbar.jsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_FiEdit_react_icons_fi__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FiEdit!=!react-icons/fi */ \"(ssr)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _assets_images_logo_png__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../assets/images/logo.png */ \"(ssr)/./src/assets/images/logo.png\");\n/* harmony import */ var _barrel_optimize_names_HiUserCircle_react_icons_hi2__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=HiUserCircle!=!react-icons/hi2 */ \"(ssr)/./node_modules/react-icons/hi2/index.mjs\");\n/* harmony import */ var _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../contexts/ChatContext */ \"(ssr)/./src/contexts/ChatContext.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nconst Navbar = ()=>{\n    const [businessName, setBusinessName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Get context values\n    const { metadata, hasMetadata } = (0,_contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__.useChatContext)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            // First, try to get business name from context\n            if (hasMetadata() && metadata.businessName) {\n                setBusinessName(metadata.businessName);\n                return;\n            }\n            // Fallback to localStorage and event listeners\n            const checkBusinessName = {\n                \"Navbar.useEffect.checkBusinessName\": ()=>{\n                    const storedName = localStorage.getItem(\"BusinessName\") || \"Driply\";\n                    if (storedName && storedName !== \"undefined\" && storedName !== \"null\") {\n                        setBusinessName(storedName);\n                        return true;\n                    }\n                    return false;\n                }\n            }[\"Navbar.useEffect.checkBusinessName\"];\n            if (checkBusinessName()) {\n                return;\n            }\n            const handleBusinessNameLoaded = {\n                \"Navbar.useEffect.handleBusinessNameLoaded\": (event)=>{\n                    if (event.detail && event.detail.businessName) {\n                        setBusinessName(event.detail.businessName);\n                    }\n                }\n            }[\"Navbar.useEffect.handleBusinessNameLoaded\"];\n            window.addEventListener(\"businessNameLoaded\", handleBusinessNameLoaded);\n            const interval = setInterval({\n                \"Navbar.useEffect.interval\": ()=>{\n                    if (checkBusinessName()) {\n                        clearInterval(interval);\n                    }\n                }\n            }[\"Navbar.useEffect.interval\"], 100);\n            const timeout = setTimeout({\n                \"Navbar.useEffect.timeout\": ()=>{\n                    clearInterval(interval);\n                }\n            }[\"Navbar.useEffect.timeout\"], 5000);\n            return ({\n                \"Navbar.useEffect\": ()=>{\n                    window.removeEventListener(\"businessNameLoaded\", handleBusinessNameLoaded);\n                    clearInterval(interval);\n                    clearTimeout(timeout);\n                }\n            })[\"Navbar.useEffect\"];\n        }\n    }[\"Navbar.useEffect\"], [\n        metadata,\n        hasMetadata\n    ]);\n    const handleNewConversation = ()=>{\n        localStorage.removeItem(\"userID\");\n        localStorage.removeItem(\"customerName_userId\");\n        localStorage.removeItem(\"BusinessName\");\n        localStorage.removeItem(\"userId\");\n        window.location.reload();\n    };\n    const handleHomeClick = (e)=>{\n        e.preventDefault();\n        window.location.reload();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-white fixed top-0 left-0 right-0 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-between items-center h-15 px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 \",\n                    children: [\n                        metadata.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                src: metadata.image,\n                                alt: \"user-image\",\n                                className: \"h-10 w-10 hover:border hover:border-gray-400 rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\Navbar.jsx\",\n                                lineNumber: 85,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiUserCircle_react_icons_hi2__WEBPACK_IMPORTED_MODULE_6__.HiUserCircle, {\n                            className: \"text-black text-xl w-10 h-10 rounded-full object-cover hover:border hover:border-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\Navbar.jsx\",\n                            lineNumber: 92,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            onClick: handleHomeClick,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-[17px] font-[500] text-black \",\n                                children: businessName\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\Navbar.jsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\Navbar.jsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\Navbar.jsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleNewConversation,\n                    className: \"px-4 py-2 rounded-lg cursor-pointer\",\n                    title: \"Start New Conversation\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit_react_icons_fi__WEBPACK_IMPORTED_MODULE_7__.FiEdit, {\n                        className: \"text-black text-xl\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\Navbar.jsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\Navbar.jsx\",\n                    lineNumber: 100,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\Navbar.jsx\",\n            lineNumber: 81,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\Navbar.jsx\",\n        lineNumber: 80,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Navbar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Navbar.jsx\n");

/***/ }),

/***/ "(ssr)/./src/components/NavigationArrows.jsx":
/*!*********************************************!*\
  !*** ./src/components/NavigationArrows.jsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=FaChevronLeft,FaChevronRight!=!react-icons/fa6 */ \"(ssr)/./node_modules/react-icons/fa6/index.mjs\");\n/* harmony import */ var _utils_carouselUtils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/carouselUtils */ \"(ssr)/./src/utils/carouselUtils.js\");\n/**\n * NavigationArrows Component\n * Handles carousel navigation buttons with proper disabled states\n */ \n\n\n\nconst NavigationArrows = ({ settingData, onPrevSlide, onNextSlide, isAtStart, isAtEnd, isHidden = false // For mobile/tablet where arrows might be hidden\n })=>{\n    // Don't render arrows if data is not loaded or if explicitly hidden\n    if (!(0,_utils_carouselUtils__WEBPACK_IMPORTED_MODULE_2__.areCardsLoaded)(settingData) || isHidden) {\n        return null;\n    }\n    const baseButtonClasses = \"absolute top-1/2 transform -translate-y-1/2 w-8 h-8 border rounded-full flex items-center justify-center shadow-md transition-all duration-200 touch-manipulation z-10\";\n    const getButtonClasses = (isDisabled)=>{\n        return `${baseButtonClasses} ${isDisabled ? 'bg-gray-100 border-gray-300 cursor-not-allowed' : 'bg-white border-gray-200 hover:bg-gray-50 cursor-pointer'}`;\n    };\n    const getIconClasses = (isDisabled)=>{\n        return `w-3 h-3 ${isDisabled ? 'text-gray-400' : 'text-gray-600'}`;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onPrevSlide,\n                disabled: isAtStart,\n                className: `${getButtonClasses(isAtStart)} left-0 -translate-x-6`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_3__.FaChevronLeft, {\n                    className: getIconClasses(isAtStart)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\NavigationArrows.jsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\NavigationArrows.jsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onNextSlide,\n                disabled: isAtEnd,\n                className: `${getButtonClasses(isAtEnd)} right-0 translate-x-6`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_3__.FaChevronRight, {\n                    className: getIconClasses(isAtEnd)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\NavigationArrows.jsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\NavigationArrows.jsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().memo(NavigationArrows));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/NavigationArrows.jsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/ChatContext.jsx":
/*!**************************************!*\
  !*** ./src/contexts/ChatContext.jsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatProvider: () => (/* binding */ ChatProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useChatContext: () => (/* binding */ useChatContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useChatContext,ChatProvider,default auto */ \n\n// Create the context\nconst ChatContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\n// Custom hook to use the chat context\nconst useChatContext = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ChatContext);\n    if (!context) {\n        throw new Error('useChatContext must be used within a ChatProvider');\n    }\n    return context;\n};\n// Chat Provider component\nconst ChatProvider = ({ children })=>{\n    console.log(\"ChatProvider\", children);\n    const [metadata, setMetadata] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: null,\n        keywords: null,\n        description: null,\n        image: null\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [settingData, setSettingData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        customerName: null,\n        businessName: null,\n        chatDesignSettings: null,\n        details: null,\n        firstSentence: null,\n        isActive: null,\n        metaData: null,\n        suggestedTopics: null\n    });\n    // Function that update setting response data\n    const updateSettingData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[updateSettingData]\": (newSettingData)=>{\n            setSettingData({\n                \"ChatProvider.useCallback[updateSettingData]\": (prevSettingData)=>({\n                        ...prevSettingData,\n                        ...newSettingData\n                    })\n            }[\"ChatProvider.useCallback[updateSettingData]\"]);\n        }\n    }[\"ChatProvider.useCallback[updateSettingData]\"], []);\n    // Function to update metadata\n    const updateMetadata = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[updateMetadata]\": (newMetadata)=>{\n            setMetadata({\n                \"ChatProvider.useCallback[updateMetadata]\": (prevMetadata)=>({\n                        ...prevMetadata,\n                        ...newMetadata\n                    })\n            }[\"ChatProvider.useCallback[updateMetadata]\"]);\n        }\n    }[\"ChatProvider.useCallback[updateMetadata]\"], []);\n    // Function to clear metadata\n    const clearMetadata = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[clearMetadata]\": ()=>{\n            setMetadata({\n                title: null,\n                keywords: null,\n                description: null,\n                image: null\n            });\n        }\n    }[\"ChatProvider.useCallback[clearMetadata]\"], []);\n    // Function to set loading state\n    const setLoadingState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[setLoadingState]\": (isLoading)=>{\n            setLoading(isLoading);\n        }\n    }[\"ChatProvider.useCallback[setLoadingState]\"], []);\n    // Function to set error state\n    const setErrorState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[setErrorState]\": (errorMessage)=>{\n            setError(errorMessage);\n        }\n    }[\"ChatProvider.useCallback[setErrorState]\"], []);\n    // Function to clear error\n    const clearError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[clearError]\": ()=>{\n            setError(null);\n        }\n    }[\"ChatProvider.useCallback[clearError]\"], []);\n    const value = {\n        metadata,\n        loading,\n        error,\n        updateMetadata,\n        clearMetadata,\n        setLoadingState,\n        setErrorState,\n        clearError,\n        settingData,\n        updateSettingData,\n        // Helper getters for easy access\n        getTitle: ()=>metadata.title,\n        getKeywords: ()=>metadata.keywords,\n        getDescription: ()=>metadata.description,\n        getImage: ()=>metadata.image,\n        hasMetadata: ()=>metadata.title !== null || metadata.keywords !== null || metadata.description !== null\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\contexts\\\\ChatContext.jsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatContext);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/ChatContext.jsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useCarousel.js":
/*!**********************************!*\
  !*** ./src/hooks/useCarousel.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCarousel: () => (/* binding */ useCarousel)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _utils_carouselUtils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/carouselUtils */ \"(ssr)/./src/utils/carouselUtils.js\");\n/**\n * Custom hook for carousel functionality\n * Handles slide navigation, touch events, and boundary detection\n */ \n\nconst useCarousel = (settingData)=>{\n    const [currentSlide, setCurrentSlide] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const [touchStart, setTouchStart] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const [touchEnd, setTouchEnd] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const totalCards = (0,_utils_carouselUtils__WEBPACK_IMPORTED_MODULE_1__.getTotalCards)(settingData);\n    // Ensure carousel starts from first card on initial load\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useCarousel.useEffect\": ()=>{\n            if (settingData?.suggestedTopics?.length > 0) {\n                setCurrentSlide(0); // Always start from first card\n            }\n        }\n    }[\"useCarousel.useEffect\"], [\n        settingData?.suggestedTopics?.length\n    ]);\n    // Navigation functions\n    const nextSlide = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useCarousel.useCallback[nextSlide]\": ()=>{\n            setCurrentSlide({\n                \"useCarousel.useCallback[nextSlide]\": (prev)=>(0,_utils_carouselUtils__WEBPACK_IMPORTED_MODULE_1__.getNextSlide)(prev, totalCards)\n            }[\"useCarousel.useCallback[nextSlide]\"]);\n        }\n    }[\"useCarousel.useCallback[nextSlide]\"], [\n        totalCards\n    ]);\n    const prevSlide = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useCarousel.useCallback[prevSlide]\": ()=>{\n            setCurrentSlide({\n                \"useCarousel.useCallback[prevSlide]\": (prev)=>(0,_utils_carouselUtils__WEBPACK_IMPORTED_MODULE_1__.getPrevSlide)(prev)\n            }[\"useCarousel.useCallback[prevSlide]\"]);\n        }\n    }[\"useCarousel.useCallback[prevSlide]\"], []);\n    // Touch event handlers\n    const handleTouchStart = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useCarousel.useCallback[handleTouchStart]\": (e)=>{\n            setTouchStart(e.targetTouches[0].clientX);\n        }\n    }[\"useCarousel.useCallback[handleTouchStart]\"], []);\n    const handleTouchMove = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useCarousel.useCallback[handleTouchMove]\": (e)=>{\n            setTouchEnd(e.targetTouches[0].clientX);\n        }\n    }[\"useCarousel.useCallback[handleTouchMove]\"], []);\n    const handleTouchEnd = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useCarousel.useCallback[handleTouchEnd]\": ()=>{\n            if (!touchStart || !touchEnd) return;\n            const swipeDistance = (0,_utils_carouselUtils__WEBPACK_IMPORTED_MODULE_1__.calculateSwipeDistance)(touchStart, touchEnd);\n            const { isNext, isPrev } = (0,_utils_carouselUtils__WEBPACK_IMPORTED_MODULE_1__.getSwipeDirection)(swipeDistance);\n            if (isNext) {\n                nextSlide();\n            } else if (isPrev) {\n                prevSlide();\n            }\n            setTouchStart(0);\n            setTouchEnd(0);\n        }\n    }[\"useCarousel.useCallback[handleTouchEnd]\"], [\n        touchStart,\n        touchEnd,\n        nextSlide,\n        prevSlide\n    ]);\n    // Boundary checks\n    const isAtStartPosition = (0,_utils_carouselUtils__WEBPACK_IMPORTED_MODULE_1__.isAtStart)(currentSlide);\n    const isAtEndPosition = (0,_utils_carouselUtils__WEBPACK_IMPORTED_MODULE_1__.isAtEnd)(currentSlide, totalCards);\n    // Reset slide when screen resizes\n    const resetSlide = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useCarousel.useCallback[resetSlide]\": ()=>{\n            setCurrentSlide(0);\n        }\n    }[\"useCarousel.useCallback[resetSlide]\"], []);\n    return {\n        currentSlide,\n        setCurrentSlide,\n        nextSlide,\n        prevSlide,\n        handleTouchStart,\n        handleTouchMove,\n        handleTouchEnd,\n        isAtStart: isAtStartPosition,\n        isAtEnd: isAtEndPosition,\n        resetSlide,\n        totalCards\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useCarousel.js\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useDeviceDetection.js":
/*!*****************************************!*\
  !*** ./src/hooks/useDeviceDetection.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDeviceDetection: () => (/* binding */ useDeviceDetection)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _utils_deviceUtils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/deviceUtils */ \"(ssr)/./src/utils/deviceUtils.js\");\n/**\n * Custom hook for device detection and responsive behavior\n * Handles screen size changes and device-specific settings\n */ \n\nconst useDeviceDetection = (onResize)=>{\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [isTablet, setIsTablet] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const checkScreenSize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDeviceDetection.useCallback[checkScreenSize]\": ()=>{\n            const mobile = (0,_utils_deviceUtils__WEBPACK_IMPORTED_MODULE_1__.checkIsMobile)();\n            const tablet = (0,_utils_deviceUtils__WEBPACK_IMPORTED_MODULE_1__.checkIsTablet)();\n            setIsMobile(mobile);\n            setIsTablet(tablet);\n        }\n    }[\"useDeviceDetection.useCallback[checkScreenSize]\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useDeviceDetection.useEffect\": ()=>{\n            // Initial check\n            checkScreenSize();\n            // Create debounced resize handler\n            const handleResize = (0,_utils_deviceUtils__WEBPACK_IMPORTED_MODULE_1__.debounce)({\n                \"useDeviceDetection.useEffect.handleResize\": ()=>{\n                    if (onResize) {\n                        onResize(); // Call external resize handler (e.g., reset carousel)\n                    }\n                    checkScreenSize();\n                }\n            }[\"useDeviceDetection.useEffect.handleResize\"], 250);\n            // Add event listener\n            if (false) {}\n        }\n    }[\"useDeviceDetection.useEffect\"], [\n        checkScreenSize,\n        onResize\n    ]);\n    return {\n        isMobile,\n        isTablet,\n        isDesktop: !isMobile && !isTablet,\n        checkScreenSize\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useDeviceDetection.js\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useMessagePairing.js":
/*!****************************************!*\
  !*** ./src/hooks/useMessagePairing.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMessagePairing: () => (/* binding */ useMessagePairing)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _utils_messageUtils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/messageUtils */ \"(ssr)/./src/utils/messageUtils.js\");\n/**\n * Custom hook for message pairing logic\n * Handles message state, pairing, and pending query management\n */ \n\nconst useMessagePairing = ()=>{\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [messageSequence, setMessageSequence] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const [pendingQueries, setPendingQueries] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(new Map());\n    // Generate unique message ID\n    const createMessageId = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMessagePairing.useCallback[createMessageId]\": ()=>{\n            const sequence = messageSequence + 1;\n            setMessageSequence(sequence);\n            return (0,_utils_messageUtils__WEBPACK_IMPORTED_MODULE_1__.generateMessageId)(sequence);\n        }\n    }[\"useMessagePairing.useCallback[createMessageId]\"], [\n        messageSequence\n    ]);\n    // Add message to the list\n    const addMessage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMessagePairing.useCallback[addMessage]\": (message)=>{\n            setMessages({\n                \"useMessagePairing.useCallback[addMessage]\": (prev)=>[\n                        ...prev,\n                        message\n                    ]\n            }[\"useMessagePairing.useCallback[addMessage]\"]);\n        }\n    }[\"useMessagePairing.useCallback[addMessage]\"], []);\n    // Add user message and track as pending\n    const addUserMessage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMessagePairing.useCallback[addUserMessage]\": (messageText, queryId)=>{\n            const userMessage = {\n                id: queryId,\n                queryId: queryId,\n                text: messageText,\n                timestamp: Date.now(),\n                source: \"USER\",\n                isSuggestion: false\n            };\n            addMessage(userMessage);\n            // Track as pending query\n            setPendingQueries({\n                \"useMessagePairing.useCallback[addUserMessage]\": (prev)=>new Map(prev).set(queryId, {\n                        id: queryId,\n                        message: userMessage,\n                        timestamp: userMessage.timestamp,\n                        sentAt: Date.now()\n                    })\n            }[\"useMessagePairing.useCallback[addUserMessage]\"]);\n            return userMessage;\n        }\n    }[\"useMessagePairing.useCallback[addUserMessage]\"], [\n        addMessage\n    ]);\n    // Add bot message and remove from pending\n    const addBotMessage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMessagePairing.useCallback[addBotMessage]\": (messageText, queryId, isError = false)=>{\n            const botMessage = {\n                id: createMessageId(),\n                queryId: queryId,\n                responseToId: queryId,\n                text: messageText,\n                timestamp: Date.now(),\n                source: \"BOT\",\n                isError: isError\n            };\n            addMessage(botMessage);\n            // Remove from pending queries\n            setPendingQueries({\n                \"useMessagePairing.useCallback[addBotMessage]\": (prev)=>{\n                    const updated = new Map(prev);\n                    updated.delete(queryId);\n                    return updated;\n                }\n            }[\"useMessagePairing.useCallback[addBotMessage]\"]);\n            return botMessage;\n        }\n    }[\"useMessagePairing.useCallback[addBotMessage]\"], [\n        addMessage,\n        createMessageId\n    ]);\n    // Get message pairs for rendering\n    const getMessagePairs = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMessagePairing.useCallback[getMessagePairs]\": ()=>{\n            return (0,_utils_messageUtils__WEBPACK_IMPORTED_MODULE_1__.createMessagePairs)(messages);\n        }\n    }[\"useMessagePairing.useCallback[getMessagePairs]\"], [\n        messages\n    ]);\n    // Cleanup old pending queries (timeout mechanism)\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useMessagePairing.useEffect\": ()=>{\n            const cleanup = setInterval({\n                \"useMessagePairing.useEffect.cleanup\": ()=>{\n                    const now = Date.now();\n                    const TIMEOUT_MS = 60000; // 60 seconds\n                    setPendingQueries({\n                        \"useMessagePairing.useEffect.cleanup\": (prev)=>{\n                            const updated = new Map(prev);\n                            for (const [queryId, queryData] of prev.entries()){\n                                if (now - queryData.sentAt > TIMEOUT_MS) {\n                                    updated.delete(queryId);\n                                }\n                            }\n                            return updated;\n                        }\n                    }[\"useMessagePairing.useEffect.cleanup\"]);\n                }\n            }[\"useMessagePairing.useEffect.cleanup\"], 10000); // Check every 10 seconds\n            return ({\n                \"useMessagePairing.useEffect\": ()=>clearInterval(cleanup)\n            })[\"useMessagePairing.useEffect\"];\n        }\n    }[\"useMessagePairing.useEffect\"], []);\n    return {\n        messages,\n        setMessages,\n        addMessage,\n        addUserMessage,\n        addBotMessage,\n        getMessagePairs,\n        createMessageId,\n        pendingQueries,\n        messageSequence\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useMessagePairing.js\n");

/***/ }),

/***/ "(ssr)/./src/styles/ChatInterface.css":
/*!**************************************!*\
  !*** ./src/styles/ChatInterface.css ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"551475b0d845\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvc3R5bGVzL0NoYXRJbnRlcmZhY2UuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcTGFwdG9wIGRhdGFcXERSSVBMWS1DSEFUXFxjaGF0LW5leHRcXHNyY1xcc3R5bGVzXFxDaGF0SW50ZXJmYWNlLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjU1MTQ3NWIwZDg0NVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/styles/ChatInterface.css\n");

/***/ }),

/***/ "(ssr)/./src/utils/carouselUtils.js":
/*!************************************!*\
  !*** ./src/utils/carouselUtils.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   areCardsLoaded: () => (/* binding */ areCardsLoaded),\n/* harmony export */   calculateSwipeDistance: () => (/* binding */ calculateSwipeDistance),\n/* harmony export */   getCarouselTransform: () => (/* binding */ getCarouselTransform),\n/* harmony export */   getNextSlide: () => (/* binding */ getNextSlide),\n/* harmony export */   getPrevSlide: () => (/* binding */ getPrevSlide),\n/* harmony export */   getSwipeDirection: () => (/* binding */ getSwipeDirection),\n/* harmony export */   getTotalCards: () => (/* binding */ getTotalCards),\n/* harmony export */   isAtEnd: () => (/* binding */ isAtEnd),\n/* harmony export */   isAtStart: () => (/* binding */ isAtStart)\n/* harmony export */ });\n/**\n * Carousel utility functions for suggestion cards\n * Handles navigation logic and boundary detection\n */ /**\n * Calculates the next slide index\n * @param {number} currentSlide - Current slide index\n * @param {number} totalCards - Total number of cards\n * @returns {number} Next slide index\n */ const getNextSlide = (currentSlide, totalCards)=>{\n    if (totalCards === 0) return currentSlide;\n    // Fix: Prevent scrolling past the last card\n    const maxSlide = totalCards - 1; // Last card index (0-based)\n    const nextSlide = currentSlide + 1;\n    return nextSlide > maxSlide ? maxSlide : nextSlide;\n};\n/**\n * Calculates the previous slide index\n * @param {number} currentSlide - Current slide index\n * @returns {number} Previous slide index\n */ const getPrevSlide = (currentSlide)=>{\n    const prevSlide = currentSlide - 1;\n    return prevSlide < 0 ? 0 : prevSlide;\n};\n/**\n * Checks if carousel is at the start position\n * @param {number} currentSlide - Current slide index\n * @returns {boolean} True if at start\n */ const isAtStart = (currentSlide)=>{\n    return currentSlide === 0;\n};\n/**\n * Checks if carousel is at the end position\n * @param {number} currentSlide - Current slide index\n * @param {number} totalCards - Total number of cards\n * @returns {boolean} True if at end\n */ const isAtEnd = (currentSlide, totalCards)=>{\n    if (totalCards === 0) return true;\n    return currentSlide >= totalCards - 1;\n};\n/**\n * Calculates touch swipe distance\n * @param {number} touchStart - Touch start position\n * @param {number} touchEnd - Touch end position\n * @returns {number} Swipe distance (positive for right swipe, negative for left)\n */ const calculateSwipeDistance = (touchStart, touchEnd)=>{\n    if (!touchStart || !touchEnd) return 0;\n    return touchStart - touchEnd;\n};\n/**\n * Determines if swipe distance is sufficient for navigation\n * @param {number} swipeDistance - Calculated swipe distance\n * @param {number} minDistance - Minimum distance threshold (default: 50)\n * @returns {Object} Object with isNext and isPrev boolean flags\n */ const getSwipeDirection = (swipeDistance, minDistance = 50)=>{\n    return {\n        isNext: swipeDistance > minDistance,\n        isPrev: swipeDistance < -minDistance\n    };\n};\n/**\n * Gets carousel transform style for current slide\n * @param {number} currentSlide - Current slide index\n * @returns {string} CSS transform value\n */ const getCarouselTransform = (currentSlide)=>{\n    return `translateX(-${currentSlide * 100}%)`;\n};\n/**\n * Checks if cards data is loaded and valid\n * @param {Object} settingData - Settings data object\n * @returns {boolean} True if cards are loaded\n */ const areCardsLoaded = (settingData)=>{\n    return settingData?.suggestedTopics && settingData.suggestedTopics.length > 0;\n};\n/**\n * Gets the total number of cards\n * @param {Object} settingData - Settings data object\n * @returns {number} Total card count\n */ const getTotalCards = (settingData)=>{\n    return settingData?.suggestedTopics?.length || 0;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/carouselUtils.js\n");

/***/ }),

/***/ "(ssr)/./src/utils/config.js":
/*!*****************************!*\
  !*** ./src/utils/config.js ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_ENDPOINTS: () => (/* binding */ API_ENDPOINTS),\n/* harmony export */   EXTERNAL_API_BASE_URL: () => (/* binding */ EXTERNAL_API_BASE_URL),\n/* harmony export */   EXTERNAL_API_ENDPOINTS: () => (/* binding */ EXTERNAL_API_ENDPOINTS),\n/* harmony export */   getExternalApiUrl: () => (/* binding */ getExternalApiUrl),\n/* harmony export */   getSSEUrl: () => (/* binding */ getSSEUrl)\n/* harmony export */ });\nconst EXTERNAL_API_BASE_URL = \"https://api-develop.driply.me/\" || 0;\nconst getExternalApiUrl = ()=>{\n    const url = EXTERNAL_API_BASE_URL;\n    return url.endsWith('/') ? url : `${url}/`;\n};\nconst API_ENDPOINTS = {\n    CHAT_INIT: '/api/chat/init',\n    CHAT_SEND: '/api/chat/send',\n    CHAT_MESSAGES: '/api/chat/messages',\n    CHAT_SETTINGS: '/api/chat/settings'\n};\nconst getSSEUrl = (userId)=>{\n    return `${getExternalApiUrl()}chat-pusher/chat?stream=${userId}`;\n};\nconst EXTERNAL_API_ENDPOINTS = {\n    CHAT_INIT: `${getExternalApiUrl()}api/chat/init`,\n    CHAT_SEND: `${getExternalApiUrl()}api/chat/message`,\n    CHAT_MESSAGES: `${getExternalApiUrl()}api/chat/messages`,\n    CHAT_SETTINGS: `${getExternalApiUrl()}api/chat/settings`\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/config.js\n");

/***/ }),

/***/ "(ssr)/./src/utils/deviceUtils.js":
/*!**********************************!*\
  !*** ./src/utils/deviceUtils.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkIsDesktop: () => (/* binding */ checkIsDesktop),\n/* harmony export */   checkIsMobile: () => (/* binding */ checkIsMobile),\n/* harmony export */   checkIsTablet: () => (/* binding */ checkIsTablet),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   getCarouselSettings: () => (/* binding */ getCarouselSettings),\n/* harmony export */   getDeviceType: () => (/* binding */ getDeviceType),\n/* harmony export */   getInputSettings: () => (/* binding */ getInputSettings),\n/* harmony export */   getMessageContainerSettings: () => (/* binding */ getMessageContainerSettings)\n/* harmony export */ });\n/**\n * Device detection utilities for responsive behavior\n * Handles screen size detection and device-specific logic\n */ /**\n * Checks if the current device is mobile\n * @returns {boolean} True if mobile device\n */ const checkIsMobile = ()=>{\n    if (true) return false;\n    return window.innerWidth < 768;\n};\n/**\n * Checks if the current device is tablet\n * @returns {boolean} True if tablet device\n */ const checkIsTablet = ()=>{\n    if (true) return false;\n    return window.innerWidth >= 768 && window.innerWidth < 1024;\n};\n/**\n * Checks if the current device is desktop\n * @returns {boolean} True if desktop device\n */ const checkIsDesktop = ()=>{\n    if (true) return false;\n    return window.innerWidth >= 1024;\n};\n/**\n * Gets the current device type\n * @returns {string} Device type: 'mobile', 'tablet', or 'desktop'\n */ const getDeviceType = ()=>{\n    if (checkIsMobile()) return 'mobile';\n    if (checkIsTablet()) return 'tablet';\n    return 'desktop';\n};\n/**\n * Gets device-specific carousel settings\n * @param {boolean} isMobile - Is mobile device\n * @returns {Object} Carousel settings for device\n */ const getCarouselSettings = (isMobile)=>{\n    return {\n        cardWidth: isMobile ? \"calc(100% - 16px)\" : \"calc(100% - 16px)\",\n        minHeight: isMobile ? \"90px\" : \"80px\",\n        padding: isMobile ? \"px-4\" : \"px-4\",\n        gap: \"gap-4\"\n    };\n};\n/**\n * Gets device-specific message container settings\n * @param {boolean} isMobile - Is mobile device\n * @returns {Object} Container settings for device\n */ const getMessageContainerSettings = (isMobile)=>{\n    return {\n        maxWidth: isMobile ? \"90%\" : undefined,\n        padding: isMobile ? \"px-3 py-2\" : \"px-4 py-3\",\n        borderRadius: isMobile ? \"rounded-[15px]\" : \"rounded-3xl\"\n    };\n};\n/**\n * Gets device-specific input settings\n * @param {boolean} isMobile - Is mobile device\n * @returns {Object} Input settings for device\n */ const getInputSettings = (isMobile)=>{\n    return {\n        fontSize: isMobile ? \"text-base\" : \"text-base\",\n        padding: isMobile ? \"px-4 py-3\" : \"px-4 py-3\",\n        borderRadius: isMobile ? \"rounded-2xl\" : \"rounded-2xl\"\n    };\n};\n/**\n * Debounces a function call\n * @param {Function} func - Function to debounce\n * @param {number} wait - Wait time in milliseconds\n * @returns {Function} Debounced function\n */ const debounce = (func, wait)=>{\n    let timeout;\n    return function executedFunction(...args) {\n        const later = ()=>{\n            clearTimeout(timeout);\n            func(...args);\n        };\n        clearTimeout(timeout);\n        timeout = setTimeout(later, wait);\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/deviceUtils.js\n");

/***/ }),

/***/ "(ssr)/./src/utils/messageUtils.js":
/*!***********************************!*\
  !*** ./src/utils/messageUtils.js ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createMessagePairs: () => (/* binding */ createMessagePairs),\n/* harmony export */   formatMessageText: () => (/* binding */ formatMessageText),\n/* harmony export */   generateMessageId: () => (/* binding */ generateMessageId),\n/* harmony export */   getMessageDisplayText: () => (/* binding */ getMessageDisplayText),\n/* harmony export */   isSystemMessage: () => (/* binding */ isSystemMessage),\n/* harmony export */   validateMessage: () => (/* binding */ validateMessage)\n/* harmony export */ });\n/**\n * Message utility functions for chat interface\n * Handles message ID generation and message pairing logic\n */ /**\n * Generates a unique message ID with timestamp and random suffix\n * @param {number} sequence - Message sequence number\n * @returns {string} Unique message ID\n */ const generateMessageId = (sequence)=>{\n    return `msg_${Date.now()}_${sequence}_${Math.random().toString(36).substr(2, 9)}`;\n};\n/**\n * Creates message pairs from flat message array\n * Groups user messages with their corresponding bot responses\n * @param {Array} messages - Array of all messages\n * @returns {Array} Array of message pairs with user/bot structure\n */ const createMessagePairs = (messages)=>{\n    if (!messages || messages.length === 0) return [];\n    // Sort messages by timestamp to handle out-of-order arrivals\n    const sortedMessages = [\n        ...messages\n    ].sort((a, b)=>a.timestamp - b.timestamp);\n    // Group messages by queryId for proper pairing\n    const messageGroups = new Map();\n    sortedMessages.forEach((msg)=>{\n        if (msg.source === \"USER\") {\n            // User message starts a new conversation pair\n            const queryId = msg.queryId || msg.id || `fallback_${msg.timestamp}`;\n            messageGroups.set(queryId, {\n                user: msg,\n                bot: null,\n                timestamp: msg.timestamp\n            });\n        } else if (msg.source === \"BOT\") {\n            // Bot message should be paired with corresponding user message\n            const queryId = msg.queryId || msg.responseToId;\n            if (queryId && messageGroups.has(queryId)) {\n                messageGroups.get(queryId).bot = msg;\n            } else {\n                // Fallback: pair with most recent unpaired user message\n                const unpairedGroups = Array.from(messageGroups.entries()).filter(([_, group])=>!group.bot).sort((a, b)=>b[1].timestamp - a[1].timestamp);\n                if (unpairedGroups.length > 0) {\n                    const [fallbackQueryId] = unpairedGroups[0];\n                    messageGroups.get(fallbackQueryId).bot = msg;\n                } else {\n                    // Create orphaned bot message group\n                    const orphanId = `orphan_${msg.timestamp}`;\n                    messageGroups.set(orphanId, {\n                        user: null,\n                        bot: msg,\n                        timestamp: msg.timestamp\n                    });\n                }\n            }\n        }\n    });\n    // Convert to array and sort by timestamp\n    return Array.from(messageGroups.entries()).map(([queryId, group])=>({\n            id: queryId,\n            user: group.user,\n            bot: group.bot,\n            isComplete: !!(group.user && group.bot),\n            isPending: !!(group.user && !group.bot),\n            timestamp: group.timestamp\n        })).sort((a, b)=>a.timestamp - b.timestamp);\n};\n/**\n * Validates message content before sending\n * @param {string} message - Message text to validate\n * @returns {boolean} True if message is valid\n */ const validateMessage = (message)=>{\n    return message && message.trim().length > 0;\n};\n/**\n * Formats message for display\n * @param {string} text - Raw message text\n * @returns {string} Formatted message text\n */ const formatMessageText = (text)=>{\n    if (!text) return '';\n    return text.trim();\n};\n/**\n * Checks if a message is a system message\n * @param {Object} message - Message object\n * @returns {boolean} True if system message\n */ const isSystemMessage = (message)=>{\n    return message?.source === 'SYSTEM' || message?.type === 'system';\n};\n/**\n * Gets the display text from a message object\n * @param {Object} message - Message object\n * @returns {string} Display text\n */ const getMessageDisplayText = (message)=>{\n    return message?.text || message?.content || message?.message || '';\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/messageUtils.js\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "node:os":
/*!**************************!*\
  !*** external "node:os" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:os");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:tty":
/*!***************************!*\
  !*** external "node:tty" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:tty");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/axios","vendor-chunks/asynckit","vendor-chunks/math-intrinsics","vendor-chunks/react-icons","vendor-chunks/es-errors","vendor-chunks/form-data","vendor-chunks/call-bind-apply-helpers","vendor-chunks/debug","vendor-chunks/get-proto","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/follow-redirects","vendor-chunks/supports-color","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/get-intrinsic","vendor-chunks/es-set-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/delayed-stream","vendor-chunks/combined-stream"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Bslug%5D%2Fpage&page=%2F%5Bslug%5D%2Fpage&appPaths=%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2F%5Bslug%5D%2Fpage.js&appDir=D%3A%5CLaptop%20data%5CDRIPLY-CHAT%5Cchat-next%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CLaptop%20data%5CDRIPLY-CHAT%5Cchat-next&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();