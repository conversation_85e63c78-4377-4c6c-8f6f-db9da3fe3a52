"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[slug]/page",{

/***/ "(app-pages-browser)/./src/components/ChatInterface.jsx":
/*!******************************************!*\
  !*** ./src/components/ChatInterface.jsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/constants */ \"(app-pages-browser)/./src/utils/constants.js\");\n/* harmony import */ var _utils_config__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/config */ \"(app-pages-browser)/./src/utils/config.js\");\n/* harmony import */ var _barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FaArrowUp,FaChevronLeft,FaChevronRight!=!react-icons/fa6 */ \"(app-pages-browser)/./node_modules/react-icons/fa6/index.mjs\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../contexts/ChatContext */ \"(app-pages-browser)/./src/contexts/ChatContext.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst ChatInterface = (param)=>{\n    let { slug, query } = param;\n    _s();\n    // Context API\n    const { metadata, updateMetadata, setLoadingState, setErrorState, clearError, getCustomerName, getBusinessName, hasMetadata, loading: contextLoading, error: contextError } = (0,_contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__.useChatContext)();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [currentSlide, setCurrentSlide] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [touchStart, setTouchStart] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [touchEnd, setTouchEnd] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isTablet, setIsTablet] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showInitialUI, setShowInitialUI] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isBotTyping, setIsBotTyping] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isBotThinking, setIsBotThinking] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [sseConnection, setSseConnection] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isFirstMessage, setIsFirstMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [shouldScrollToTop, setShouldScrollToTop] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isAtBottom, setIsAtBottom] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [lastMessageCount, setLastMessageCount] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const carouselRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const desktopMessagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const mobileMessagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const tabletMessagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // Detect iOS for targeted fixes\n    const isIOS = ()=>{\n        if (true) {\n            return /iPhone|iPad|iPod/i.test(navigator.userAgent);\n        }\n        return false;\n    };\n    const username = slug;\n    // Utility function to get current container\n    const getCurrentContainer = ()=>{\n        const screenWidth = window.innerWidth;\n        const isDesktopLayout = screenWidth >= 1024;\n        const isTabletLayout = screenWidth >= 768 && screenWidth < 1024;\n        if (isDesktopLayout) {\n            return desktopMessagesContainerRef.current;\n        } else if (isTabletLayout) {\n            return tabletMessagesContainerRef.current;\n        } else {\n            return mobileMessagesContainerRef.current;\n        }\n    };\n    // Check if user is scrolled to bottom\n    const isScrolledToBottom = ()=>{\n        const container = getCurrentContainer();\n        if (!container) return true;\n        const threshold = 100; // pixels from bottom\n        return container.scrollTop + container.clientHeight >= container.scrollHeight - threshold;\n    };\n    // Dynamic margin calculation for message pairs\n    const getDynamicBottomMargin = (pairIndex, totalPairs)=>{\n        const isLastPair = pairIndex === totalPairs - 1;\n        const isFirstPair = pairIndex === 0;\n        // Rule 1: Only the current last pair gets positioning margin when at bottom\n        if (isLastPair && !isFirstPair && isAtBottom) {\n            return isMobile ? 'calc(70vh - 160px)' : 'calc(75vh - 120px)';\n        }\n        // Rule 2: When scrolled up or not last pair, no positioning margin\n        return '0';\n    };\n    // Get minimum height for message pairs\n    const getMinHeight = (isLastPair, isFirstPair)=>{\n        if (isLastPair && isFirstPair) {\n            // For the very first message, ensure it appears at optimal reading position\n            return isMobile ? 'calc(30vh - 60px)' : 'calc(25vh - 20px)';\n        }\n        return 'auto';\n    };\n    // Get top margin for message pairs\n    const getTopMargin = (isFirstPair)=>{\n        return isFirstPair ? isMobile ? '60px' : '80px' : '0';\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (username && \"object\" !== \"undefined\") {\n                fetchCustomer(username);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        username\n    ]);\n    const fetchCustomer = async (customerName)=>{\n        try {\n            setLoadingState(true);\n            clearError();\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"\".concat(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_SETTINGS, \"?customerName=\").concat(customerName));\n            if (response.data && response.data.customerName) {\n                const metadataFromResponse = response.data.metaData;\n                console.log('Storing metadata in context:', metadataFromResponse);\n                updateMetadata(metadataFromResponse);\n                // Keep existing localStorage for backward compatibility\n                localStorage.setItem(\"customerName_userId\", response.data.customerName);\n                localStorage.setItem(\"BusinessName\", response.data.businessName);\n                window.dispatchEvent(new CustomEvent('businessNameLoaded', {\n                    detail: {\n                        businessName: response.data.businessName\n                    }\n                }));\n                const existingUserId = localStorage.getItem(\"userID\");\n                if (existingUserId) {\n                    await fetchExistingMessages(existingUserId);\n                } else {\n                    await initializeChatSession();\n                }\n            } else {\n                await initializeChatSession();\n            }\n        } catch (error) {\n            console.error('Error fetching customer data:', error);\n            setErrorState('Failed to load customer settings');\n            // Fallback metadata\n            updateMetadata({\n                customerName: customerName,\n                businessName: \"Driply\"\n            });\n            localStorage.setItem(\"BusinessName\", \"Driply\");\n            window.dispatchEvent(new CustomEvent('businessNameLoaded', {\n                detail: {\n                    businessName: \"Driply\"\n                }\n            }));\n            await initializeChatSession();\n        } finally{\n            setLoadingState(false);\n        }\n    };\n    const fetchExistingMessages = async (userId)=>{\n        console.log(\"metadata : \", metadata);\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"\".concat(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_MESSAGES, \"?userId=\").concat(userId));\n            if (response.data && response.data.length > 0) {\n                setMessages(response.data.map((msg)=>({\n                        text: msg.message,\n                        timestamp: new Date(msg.createdAt).getTime(),\n                        type: msg.type,\n                        source: msg.source\n                    })));\n            }\n            connectToSSE(userId);\n        } catch (error) {\n            await initializeChatSession();\n        }\n    };\n    const initializeChatSession = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_INIT, {\n                customerName: username\n            });\n            if (response.data && response.data.userId) {\n                localStorage.setItem(\"userId\", response.data.userId);\n                localStorage.setItem(\"userID\", response.data._id);\n                await fetchExistingMessages(response.data._id);\n            }\n        } catch (error) {\n            if (!localStorage.getItem(\"BusinessName\")) {\n                localStorage.setItem(\"BusinessName\", \"Driply\");\n                window.dispatchEvent(new CustomEvent('businessNameLoaded', {\n                    detail: {\n                        businessName: \"Driply\"\n                    }\n                }));\n            }\n        }\n    };\n    const connectToSSE = (userId)=>{\n        try {\n            if (sseConnection) {\n                sseConnection.close();\n            }\n            const sseUrl = (0,_utils_config__WEBPACK_IMPORTED_MODULE_4__.getSSEUrl)(userId);\n            const eventSource = new EventSource(sseUrl);\n            setSseConnection(eventSource);\n            eventSource.addEventListener(\"message\", (param)=>{\n                let { data } = param;\n                try {\n                    const contents = JSON.parse(data);\n                    handleSSEMessage(contents);\n                } catch (error) {\n                // Handle parsing error silently\n                }\n            });\n            eventSource.addEventListener(\"error\", ()=>{\n            // Handle connection error silently\n            });\n        } catch (error) {\n        // Handle SSE connection error silently\n        }\n    };\n    const handleSSEMessage = (data)=>{\n        const subtype = data.subType || data.subtype || data.type || \"UNKNOWN\";\n        const content = data.content || \"\";\n        const message = data.message || data.text || \"\";\n        if ([\n            \"TYPING\",\n            \"THINKING\",\n            \"BEHAVIOUR_MESSAGE\"\n        ].includes(subtype)) {\n            switch(subtype){\n                case \"TYPING\":\n                    setIsBotTyping(true);\n                    setIsBotThinking(false);\n                    break;\n                case \"THINKING\":\n                    setIsBotThinking(true);\n                    setIsBotTyping(false);\n                    break;\n                case \"BEHAVIOUR_MESSAGE\":\n                    setIsBotTyping(false);\n                    setIsBotThinking(false);\n                    break;\n            }\n            return;\n        }\n        const displayText = message || content;\n        if (!displayText) return;\n        const displayType = [\n            \"TEXT\",\n            \"MESSAGE\",\n            \"DATA_MESSAGE\"\n        ].includes(subtype) ? subtype : \"UNKNOWN\";\n        setMessages((prev)=>[\n                ...prev,\n                {\n                    text: displayText,\n                    timestamp: Date.now(),\n                    type: displayType,\n                    source: \"BOT\"\n                }\n            ]);\n        setIsBotTyping(false);\n        setIsBotThinking(false);\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (message.trim()) {\n            const userMessage = message.trim();\n            const isFirstMsg = messages.length === 0;\n            setMessages((prev)=>[\n                    ...prev,\n                    {\n                        text: userMessage,\n                        timestamp: Date.now(),\n                        type: \"TEXT\",\n                        source: \"USER\"\n                    }\n                ]);\n            setMessage(\"\");\n            setIsTyping(false);\n            // For first message, set flag to scroll to top\n            if (isFirstMsg) {\n                setShouldScrollToTop(true);\n                setIsFirstMessage(false);\n            }\n            // Show loading dots immediately after sending message\n            setIsBotThinking(true);\n            setIsBotTyping(false);\n            // Reset textarea\n            if (inputRef.current) {\n                inputRef.current.style.height = \"104px\";\n                inputRef.current.scrollTop = 0;\n                inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n                inputRef.current.classList.add(\"reset-height\");\n            }\n            try {\n                const userId = localStorage.getItem(\"userID\");\n                if (userId) {\n                    await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_SEND, {\n                        userId: userId,\n                        message: userMessage,\n                        \"typs\": \"TEXT\",\n                        \"source\": \"USER\",\n                        \"isTest\": query.isTest === '1' ? true : false\n                    });\n                }\n            } catch (error) {\n                // Hide loading dots on error\n                setIsBotThinking(false);\n                setIsBotTyping(false);\n                setMessages((prev)=>[\n                        ...prev,\n                        {\n                            text: \"Sorry, there was an error sending your message. Please try again.\",\n                            timestamp: Date.now(),\n                            type: \"TEXT\",\n                            source: \"BOT\"\n                        }\n                    ]);\n            }\n        }\n    };\n    const handleTextareaResize = (textarea)=>{\n        if (textarea) {\n            textarea.style.height = \"104px\";\n            const scrollHeight = textarea.scrollHeight;\n            if (isIOS()) {\n                if (scrollHeight > 48) {\n                    textarea.style.overflowY = \"auto\";\n                } else {\n                    textarea.style.overflowY = \"hidden\";\n                }\n            } else {\n                const newHeight = Math.min(250, Math.max(104, scrollHeight));\n                textarea.style.height = \"\".concat(newHeight, \"px\");\n                textarea.style.overflowY = scrollHeight > newHeight ? \"auto\" : \"hidden\";\n            }\n        }\n    };\n    const handleInputChange = (e)=>{\n        const value = e.target.value;\n        setMessage(value);\n        handleTextareaResize(e.target);\n        if (messages.length === 0) {\n            setIsTyping(value.length > 0);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length > 0 && inputRef.current && !message) {\n                const timer = setTimeout({\n                    \"ChatInterface.useEffect.timer\": ()=>{\n                        if (inputRef.current) {\n                            inputRef.current.style.height = \"104px\";\n                            inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n                            inputRef.current.classList.add(\"reset-height\");\n                        }\n                    }\n                }[\"ChatInterface.useEffect.timer\"], 50);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                })[\"ChatInterface.useEffect\"];\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages,\n        message\n    ]);\n    // Handle scroll events to detect bottom position\n    const handleScroll = useCallback({\n        \"ChatInterface.useCallback[handleScroll]\": ()=>{\n            const atBottom = isScrolledToBottom();\n            if (atBottom !== isAtBottom) {\n                setIsAtBottom(atBottom);\n            }\n        }\n    }[\"ChatInterface.useCallback[handleScroll]\"], [\n        isAtBottom\n    ]);\n    // Track message count changes\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length !== lastMessageCount) {\n                setIsAtBottom(true); // New message = scroll to bottom\n                setLastMessageCount(messages.length);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages.length,\n        lastMessageCount\n    ]);\n    // Add scroll listeners to containers\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const containers = [\n                desktopMessagesContainerRef.current,\n                mobileMessagesContainerRef.current,\n                tabletMessagesContainerRef.current\n            ].filter(Boolean);\n            containers.forEach({\n                \"ChatInterface.useEffect\": (container)=>{\n                    container.addEventListener('scroll', handleScroll, {\n                        passive: true\n                    });\n                }\n            }[\"ChatInterface.useEffect\"]);\n            return ({\n                \"ChatInterface.useEffect\": ()=>{\n                    containers.forEach({\n                        \"ChatInterface.useEffect\": (container)=>{\n                            container.removeEventListener('scroll', handleScroll);\n                        }\n                    }[\"ChatInterface.useEffect\"]);\n                }\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], [\n        handleScroll\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length > 0) {\n                handleChatGPTScroll();\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages,\n        isBotTyping,\n        isBotThinking\n    ]);\n    const handleChatGPTScroll = ()=>{\n        setTimeout(()=>{\n            // Determine screen type and get appropriate container\n            const screenWidth = window.innerWidth;\n            const isDesktopLayout = screenWidth >= 1024;\n            const isTabletLayout = screenWidth >= 768 && screenWidth < 1024;\n            let container;\n            if (isDesktopLayout) {\n                container = desktopMessagesContainerRef.current;\n            } else if (isTabletLayout) {\n                container = tabletMessagesContainerRef.current;\n            } else {\n                container = mobileMessagesContainerRef.current;\n            }\n            if (!container) return;\n            const messageElements = container.querySelectorAll('.message-pair');\n            if (messageElements.length === 0) return;\n            // Get container dimensions and account for navbar offset\n            const containerHeight = container.clientHeight;\n            const containerRect = container.getBoundingClientRect();\n            const navbarHeight = 60; // h-15 = 60px\n            // Calculate the actual visible area accounting for navbar\n            const effectiveViewportTop = Math.max(0, navbarHeight - containerRect.top);\n            const lastMessagePair = messageElements[messageElements.length - 1];\n            if (shouldScrollToTop) {\n                // For first message, position at optimal reading position accounting for navbar\n                const targetPosition = containerHeight * (isMobile ? 0.3 : 0.25) + effectiveViewportTop;\n                const userMessageElement = lastMessagePair.querySelector('.flex.justify-end');\n                if (userMessageElement) {\n                    const userMessageTop = lastMessagePair.offsetTop + userMessageElement.offsetTop;\n                    const scrollPosition = Math.max(0, userMessageTop - targetPosition);\n                    container.scrollTo({\n                        top: scrollPosition,\n                        behavior: 'smooth'\n                    });\n                }\n                setShouldScrollToTop(false);\n            } else {\n                // For subsequent messages: Position at optimal reading position accounting for navbar\n                const targetPosition = containerHeight * (isMobile ? 0.25 : 0.2) + effectiveViewportTop;\n                const userMessageElement = lastMessagePair.querySelector('.flex.justify-end');\n                if (userMessageElement) {\n                    const userMessageTop = lastMessagePair.offsetTop + userMessageElement.offsetTop;\n                    const scrollPosition = Math.max(0, userMessageTop - targetPosition);\n                    requestAnimationFrame(()=>{\n                        container.scrollTo({\n                            top: scrollPosition,\n                            behavior: 'smooth'\n                        });\n                    });\n                }\n            }\n        }, 100); // Reduced timeout for more responsive feel\n    };\n    const renderMessagePairs = ()=>{\n        const pairs = [];\n        const userMessages = messages.filter((msg)=>msg.source === \"USER\");\n        const botMessages = messages.filter((msg)=>msg.source === \"BOT\");\n        // Create pairs of user and bot messages\n        for(let i = 0; i < userMessages.length; i++){\n            const userMsg = userMessages[i];\n            const botMsg = botMessages[i];\n            const isLastPair = i === userMessages.length - 1;\n            const isFirstPair = i === 0;\n            pairs.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"message-pair flex flex-col justify-start\",\n                style: {\n                    // Dynamic height based on content, with special handling for positioning\n                    minHeight: getMinHeight(isLastPair, isFirstPair),\n                    // Add top margin to account for navbar\n                    marginTop: getTopMargin(isFirstPair),\n                    paddingTop: isFirstPair ? '1rem' : '2rem',\n                    paddingBottom: isLastPair ? '2rem' : '1rem',\n                    // DYNAMIC MARGIN SYSTEM - only applies when at bottom and is last pair\n                    marginBottom: getDynamicBottomMargin(i, userMessages.length),\n                    // Smooth transition for margin changes\n                    transition: 'margin-bottom 0.3s ease-in-out'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-end mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-[16px] font-[400] rounded-3xl max-w-xs lg:max-w-lg break-words hyphens-auto bg-gray-100 \".concat(isMobile ? 'rounded-[15px] px-3 py-2' : 'px-4 py-3'),\n                            style: {\n                                overflowWrap: \"break-word\",\n                                wordBreak: \"break-word\",\n                                maxWidth: isMobile ? \"90%\" : undefined\n                            },\n                            children: userMsg.text\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 531,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 530,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-start\",\n                        children: botMsg ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-[16px] font-[400] rounded-3xl max-w-xs lg:max-w-lg break-words hyphens-auto bg-white \".concat(isMobile ? 'rounded-[15px] px-3 py-2' : 'px-4 py-3'),\n                            style: {\n                                overflowWrap: \"break-word\",\n                                wordBreak: \"break-word\",\n                                maxWidth: isMobile ? \"90%\" : undefined\n                            },\n                            children: botMsg.text\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 546,\n                            columnNumber: 15\n                        }, undefined) : // Show loading dots for the last pair if bot is thinking/typing\n                        i === userMessages.length - 1 && (isBotTyping || isBotThinking) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white \".concat(isMobile ? 'px-3 py-2 rounded-[15px]' : 'px-4 py-3 rounded-3xl'),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-black rounded-full loading-dot\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 561,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-black rounded-full loading-dot\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 562,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-black rounded-full loading-dot\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 563,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 560,\n                                columnNumber: 19\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 559,\n                            columnNumber: 17\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 544,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, \"pair-\".concat(i), true, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 513,\n                columnNumber: 9\n            }, undefined));\n        }\n        return pairs;\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSubmit(e);\n        }\n    };\n    const handleSuggestionClick = async (cardTitle)=>{\n        const isFirstMsg = messages.length === 0;\n        setMessages((prev)=>[\n                ...prev,\n                {\n                    text: cardTitle,\n                    timestamp: Date.now(),\n                    type: \"TEXT\",\n                    source: \"USER\"\n                }\n            ]);\n        setMessage(\"\");\n        setIsTyping(false);\n        // For first message, set flag to scroll to top\n        if (isFirstMsg) {\n            setShouldScrollToTop(true);\n            setIsFirstMessage(false);\n        }\n        // Show loading dots immediately after sending message\n        setIsBotThinking(true);\n        setIsBotTyping(false);\n        if (inputRef.current) {\n            inputRef.current.style.height = \"104px\";\n            inputRef.current.scrollTop = 0;\n            inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n            inputRef.current.classList.add(\"reset-height\");\n        }\n        try {\n            const userId = localStorage.getItem(\"userID\");\n            if (userId) {\n                await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_SEND, {\n                    userId: userId,\n                    message: cardTitle,\n                    customerName: username\n                });\n            }\n        } catch (error) {\n            // Hide loading dots on error\n            setIsBotThinking(false);\n            setIsBotTyping(false);\n            setMessages((prev)=>[\n                    ...prev,\n                    {\n                        text: \"Sorry, there was an error sending your message. Please try again.\",\n                        timestamp: Date.now(),\n                        type: \"TEXT\",\n                        source: \"BOT\"\n                    }\n                ]);\n        }\n    };\n    const getMaxSlides = ()=>{\n        if (isMobile) return _utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.length;\n        return Math.ceil(_utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.length / 2);\n    };\n    const nextSlide = ()=>{\n        if (isMobile) {\n            setCurrentSlide((prev)=>prev < _utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.length - 1 ? prev + 1 : prev);\n        } else {\n            const maxSlides = getMaxSlides();\n            setCurrentSlide((prev)=>(prev + 1) % maxSlides);\n        }\n    };\n    const prevSlide = ()=>{\n        if (isMobile) {\n            setCurrentSlide((prev)=>prev > 0 ? prev - 1 : prev);\n        } else {\n            const maxSlides = getMaxSlides();\n            setCurrentSlide((prev)=>(prev - 1 + maxSlides) % maxSlides);\n        }\n    };\n    const handleNextSlide = ()=>{\n        nextSlide();\n    };\n    const handlePrevSlide = ()=>{\n        prevSlide();\n    };\n    const handleTouchStart = (e)=>{\n        setTouchStart(e.targetTouches[0].clientX);\n    };\n    const handleTouchMove = (e)=>{\n        setTouchEnd(e.targetTouches[0].clientX);\n    };\n    const handleTouchEnd = ()=>{\n        if (!touchStart || !touchEnd) return;\n        const swipeDistance = touchStart - touchEnd;\n        const minSwipeDistance = 50;\n        if (swipeDistance > minSwipeDistance) {\n            nextSlide();\n        }\n        if (swipeDistance < -minSwipeDistance) {\n            prevSlide();\n        }\n        setTouchStart(0);\n        setTouchEnd(0);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length === 0) {\n                setShowInitialUI(false);\n                const timer = setTimeout({\n                    \"ChatInterface.useEffect.timer\": ()=>setShowInitialUI(true)\n                }[\"ChatInterface.useEffect.timer\"], 60);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                })[\"ChatInterface.useEffect\"];\n            } else {\n                setShowInitialUI(false);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages.length\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const checkScreenSize = {\n                \"ChatInterface.useEffect.checkScreenSize\": ()=>{\n                    if (true) {\n                        const width = window.innerWidth;\n                        setIsMobile(width < 768);\n                        setIsTablet(width >= 768 && width < 1024);\n                    }\n                }\n            }[\"ChatInterface.useEffect.checkScreenSize\"];\n            checkScreenSize();\n            const handleResize = {\n                \"ChatInterface.useEffect.handleResize\": ()=>{\n                    setCurrentSlide(0);\n                    checkScreenSize();\n                }\n            }[\"ChatInterface.useEffect.handleResize\"];\n            if (true) {\n                window.addEventListener(\"resize\", handleResize);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>window.removeEventListener(\"resize\", handleResize)\n                })[\"ChatInterface.useEffect\"];\n            }\n        }\n    }[\"ChatInterface.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (inputRef.current && messages.length === 0) {\n                const shouldAutoFocus = window.innerWidth >= 768;\n                if (shouldAutoFocus) {\n                    const timer = setTimeout({\n                        \"ChatInterface.useEffect.timer\": ()=>{\n                            var _inputRef_current;\n                            (_inputRef_current = inputRef.current) === null || _inputRef_current === void 0 ? void 0 : _inputRef_current.focus();\n                        }\n                    }[\"ChatInterface.useEffect.timer\"], 100);\n                    return ({\n                        \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                    })[\"ChatInterface.useEffect\"];\n                }\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages.length\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            return ({\n                \"ChatInterface.useEffect\": ()=>{\n                    if (sseConnection) {\n                        sseConnection.close();\n                    }\n                }\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], [\n        sseConnection\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-bfe26d46d13290ac\" + \" \" + \"bg-white flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-bfe26d46d13290ac\" + \" \" + \"hidden lg:flex flex-1 flex-col px-4 \",\n                children: messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-bfe26d46d13290ac\" + \" \" + \"flex flex-1 items-center justify-center min-h-[calc(100vh-64px)] mt-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-bfe26d46d13290ac\" + \" \" + \"flex flex-col items-center w-[768px] justify-center mx-auto \",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    transitionDelay: showInitialUI ? \"40ms\" : \"0ms\"\n                                },\n                                className: \"jsx-bfe26d46d13290ac\" + \" \" + \"text-4xl text-gray-900 mb-6 text-center transition-all duration-700 \".concat(showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                children: \"How can I help you?\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 756,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                style: {\n                                    transitionDelay: showInitialUI ? \"120ms\" : \"0ms\"\n                                },\n                                className: \"jsx-bfe26d46d13290ac\" + \" \" + \"relative w-full mb-6 flex flex-col items-center transition-all duration-500 \".concat(showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        ref: messages.length === 0 ? inputRef : null,\n                                        value: message,\n                                        onChange: handleInputChange,\n                                        onKeyDown: handleKeyPress,\n                                        placeholder: \"Ask anything\",\n                                        autoFocus: messages.length === 0,\n                                        rows: 1,\n                                        style: {\n                                            boxSizing: \"border-box\",\n                                            zIndex: 1001,\n                                            position: \"relative\"\n                                        },\n                                        className: \"jsx-bfe26d46d13290ac\" + \" \" + \"pt-3 px-4 lg:p-4 pb-16 pr-12 w-full min-h-[104px] max-h-[250px] overflow-y-auto hide-scrollbar text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base shadow-md resize-none reset-height\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 775,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: message.trim().length === 0,\n                                        className: \"jsx-bfe26d46d13290ac\" + \" \" + \"absolute right-3 bottom-3 w-9 h-9 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation \".concat(message.trim().length === 0 ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                            className: \"w-4 h-4 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 798,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 790,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 767,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    transitionDelay: showInitialUI ? \"200ms\" : \"0ms\",\n                                    zIndex: 10\n                                },\n                                className: \"jsx-bfe26d46d13290ac\" + \" \" + \"relative w-full max-w-2xl transition-all duration-500 ease-in-out \".concat(isTyping ? \"opacity-0 pointer-events-none\" : showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-bfe26d46d13290ac\" + \" \" + \"overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: carouselRef,\n                                            style: {\n                                                transform: \"translateX(-\".concat(currentSlide * 100, \"%)\")\n                                            },\n                                            className: \"jsx-bfe26d46d13290ac\" + \" \" + \"flex transition-transform duration-300 ease-in-out\",\n                                            children: Array.from({\n                                                length: Math.ceil(_utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.length / 2)\n                                            }).map((_, slideIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-bfe26d46d13290ac\" + \" \" + \"w-full flex-shrink-0 flex gap-2 justify-center\",\n                                                    children: _utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.slice(slideIndex * 2, slideIndex * 2 + 2).map((card, cardIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleSuggestionClick(card.title),\n                                                            style: {\n                                                                width: \"fit-content\"\n                                                            },\n                                                            className: \"jsx-bfe26d46d13290ac\" + \" \" + \"py-2 px-3 rounded-[8px] bg-[#f6f6f6] text-left group transition-all duration-200 touch-manipulation\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-bfe26d46d13290ac\" + \" \" + \"text-[16px] font-[600] text-black mb-0.5 leading-tight whitespace-nowrap\",\n                                                                    children: card.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                    lineNumber: 836,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-bfe26d46d13290ac\" + \" \" + \"text-[16px] text-gray-500 leading-tight whitespace-nowrap\",\n                                                                    children: card.subtitle\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                    lineNumber: 839,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, slideIndex * 2 + cardIndex, true, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 830,\n                                                            columnNumber: 27\n                                                        }, undefined))\n                                                }, slideIndex, false, {\n                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                    lineNumber: 822,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 814,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 813,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handlePrevSlide,\n                                        className: \"jsx-bfe26d46d13290ac\" + \" \" + \"absolute left-28 top-1/2 transform -translate-y-1/2 -translate-x-6 w-8 h-8 bg-white border border-gray-200 rounded-full flex items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronLeft, {\n                                            className: \"w-3 h-3 text-gray-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 852,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 848,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleNextSlide,\n                                        className: \"jsx-bfe26d46d13290ac\" + \" \" + \"absolute right-28 top-1/2 transform -translate-y-1/2 translate-x-6 w-8 h-8 bg-white border border-gray-200 rounded-full flex items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronRight, {\n                                            className: \"w-3 h-3 text-gray-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 858,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 854,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 801,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 755,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                    lineNumber: 754,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: desktopMessagesContainerRef,\n                            style: {\n                                overscrollBehavior: \"contain\",\n                                maxHeight: \"calc(100vh - 200px)\",\n                                minHeight: \"calc(100vh - 200px)\",\n                                marginTop: \"60px\" // Exact navbar height (h-15 = 60px)\n                            },\n                            className: \"jsx-bfe26d46d13290ac\" + \" \" + \"flex-1 overflow-y-auto hide-scrollbar\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-bfe26d46d13290ac\" + \" \" + \"w-full lg:w-[768px] mx-auto px-4\",\n                                children: [\n                                    renderMessagePairs(),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: messagesEndRef,\n                                        className: \"jsx-bfe26d46d13290ac\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 877,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 875,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 865,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                zIndex: 1000,\n                                marginTop: \"20px\"\n                            },\n                            className: \"jsx-bfe26d46d13290ac\" + \" \" + \"fixed bottom-0 left-0 right-0 p-4 mt-5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSubmit,\n                                    className: \"jsx-bfe26d46d13290ac\" + \" \" + \"relative w-full lg:w-[768px] mx-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            ref: messages.length > 0 ? inputRef : null,\n                                            value: message,\n                                            onChange: handleInputChange,\n                                            onKeyDown: handleKeyPress,\n                                            placeholder: \"Ask anything\",\n                                            rows: 1,\n                                            style: {\n                                                boxSizing: \"border-box\",\n                                                zIndex: 1001,\n                                                position: \"relative\"\n                                            },\n                                            className: \"jsx-bfe26d46d13290ac\" + \" \" + \"pt-3 px-4 md:px-5 pb-10 pr-12 w-full min-h-[104px] max-h-[250px] overflow-y-auto text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base shadow-lg resize-none reset-height hide-scrollbar\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 889,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: message.trim().length === 0,\n                                            className: \"jsx-bfe26d46d13290ac\" + \" \" + \"absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation \".concat(message.trim().length === 0 ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                                className: \"w-4 h-4 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 911,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 903,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 885,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-bfe26d46d13290ac\" + \" \" + \"text-center mt-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-bfe26d46d13290ac\" + \" \" + \"text-xs text-gray-500\",\n                                        children: [\n                                            \"This chat is powered by\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                className: \"jsx-bfe26d46d13290ac\" + \" \" + \"text-black\",\n                                                children: \"Driply.me\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 917,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 915,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 914,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 881,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 752,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-bfe26d46d13290ac\" + \" \" + \"lg:hidden overflow-hidden fixed inset-0 flex flex-col  mt-10 \",\n                children: [\n                    messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-bfe26d46d13290ac\" + \" \" + \"flex flex-col items-center pt-15 pb-4 px-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-bfe26d46d13290ac\" + \" \" + \"hidden md:flex lg:hidden flex-col items-center\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 930,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 929,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    paddingBottom: \"260px\"\n                                },\n                                className: \"jsx-bfe26d46d13290ac\" + \" \" + \"flex flex-col items-center justify-center flex-1 px-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"40ms\" : \"0ms\"\n                                    },\n                                    className: \"jsx-bfe26d46d13290ac\" + \" \" + \"text-2xl md:text-4xl text-gray-900 text-center leading-relaxed transition-all duration-700 \".concat(showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                    children: \"How can I help you?\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 936,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 932,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: mobileMessagesContainerRef,\n                                style: {\n                                    overscrollBehavior: \"contain\",\n                                    maxHeight: \"calc(100vh - 340px)\",\n                                    minHeight: \"calc(100vh - 340px)\",\n                                    marginTop: \"60px\" // Exact navbar height (h-15 = 60px)\n                                },\n                                className: \"jsx-bfe26d46d13290ac\" + \" \" + \"md:hidden flex-1 overflow-y-auto hide-scrollbar\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-bfe26d46d13290ac\" + \" \" + \"w-full max-w-[803px] mx-auto px-4 pt-4\",\n                                    children: [\n                                        renderMessagePairs(),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: messagesEndRef,\n                                            className: \"jsx-bfe26d46d13290ac\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 963,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 961,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 951,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: tabletMessagesContainerRef,\n                                style: {\n                                    overscrollBehavior: \"contain\",\n                                    maxHeight: \"calc(100vh - 200px)\",\n                                    minHeight: \"calc(100vh - 200px)\",\n                                    marginTop: \"60px\" // Exact navbar height (h-15 = 60px)\n                                },\n                                className: \"jsx-bfe26d46d13290ac\" + \" \" + \"hidden md:block lg:hidden flex-1 overflow-y-auto hide-scrollbar\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-bfe26d46d13290ac\" + \" \" + \"w-full max-w-[803px] mx-auto px-4 pt-4\",\n                                    children: [\n                                        renderMessagePairs(),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: messagesEndRef,\n                                            className: \"jsx-bfe26d46d13290ac\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 980,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 978,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 968,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    minHeight: \"160px\",\n                                    zIndex: 1000,\n                                    paddingBottom: \"env(safe-area-inset-bottom, 0)\",\n                                    transform: \"translateZ(0)\",\n                                    backfaceVisibility: \"hidden\"\n                                },\n                                className: \"jsx-bfe26d46d13290ac\" + \" \" + \"fixed bottom-0 left-0 right-0 bg-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        transform: \"translateZ(0)\"\n                                    },\n                                    className: \"jsx-bfe26d46d13290ac\" + \" \" + \"px-4 py-4 bg-white\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            style: {\n                                                zIndex: 1001\n                                            },\n                                            className: \"jsx-bfe26d46d13290ac\" + \" \" + \"relative bg-white\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    transform: \"translateZ(0)\"\n                                                },\n                                                className: \"jsx-bfe26d46d13290ac\" + \" \" + \"relative w-full max-w-[890px] mx-auto bg-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        ref: messages.length > 0 ? inputRef : null,\n                                                        value: message,\n                                                        onChange: handleInputChange,\n                                                        onKeyDown: handleKeyPress,\n                                                        placeholder: \"Ask anything\",\n                                                        rows: 1,\n                                                        style: {\n                                                            boxSizing: \"border-box\",\n                                                            maxHeight: \"250px\",\n                                                            zIndex: 1001,\n                                                            overscrollBehavior: \"none\",\n                                                            position: \"relative\"\n                                                        },\n                                                        className: \"jsx-bfe26d46d13290ac\" + \" \" + \"w-full min-h-[104px] pt-3 px-4 pb-10 pr-12 text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base resize-none overflow-y-auto reset-height\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1008,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        disabled: message.trim().length === 0,\n                                                        className: \"jsx-bfe26d46d13290ac\" + \" \" + \"absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation \".concat(message.trim().length === 0 ? \"bg-[#E0E0E0] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                                            className: \"w-4 h-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 1032,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1024,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1004,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 999,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-bfe26d46d13290ac\" + \" \" + \"text-center py-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-bfe26d46d13290ac\" + \" \" + \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"This chat is powered by\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"jsx-bfe26d46d13290ac\" + \" \" + \"text-black\",\n                                                        children: \"Driply.me\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1039,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1037,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1036,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 995,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 985,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-bfe26d46d13290ac\" + \" \" + \"absolute bottom-0 left-0 right-0 bg-white\",\n                        children: messages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"120ms\" : \"0ms\",\n                                        position: \"relative\",\n                                        zIndex: 10,\n                                        backgroundColor: \"white\",\n                                        paddingBottom: \"10px\"\n                                    },\n                                    className: \"jsx-bfe26d46d13290ac\" + \" \" + \"\".concat(isMobile ? \"px-0\" : \"px-4\", \" pt-2 pb-2 transition-all duration-500 ease-in-out \").concat(isTyping ? \"opacity-0 pointer-events-none\" : showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-bfe26d46d13290ac\" + \" \" + \"overflow-hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                ref: carouselRef,\n                                                style: {\n                                                    transform: \"translateX(-\".concat(currentSlide * (isMobile ? 50 : 100), \"%)\"),\n                                                    paddingLeft: isMobile ? \"1rem\" : \"0\"\n                                                },\n                                                onTouchStart: handleTouchStart,\n                                                onTouchMove: handleTouchMove,\n                                                onTouchEnd: handleTouchEnd,\n                                                className: \"jsx-bfe26d46d13290ac\" + \" \" + \"flex transition-transform duration-300 ease-in-out\",\n                                                children: isMobile ? (()=>{\n                                                    const circularCards = [\n                                                        ..._utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS,\n                                                        ..._utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS\n                                                    ];\n                                                    return circularCards.map((card, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-bfe26d46d13290ac\" + \" \" + \"flex-shrink-0 mt-3 mr-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleSuggestionClick(card.title),\n                                                                style: {\n                                                                    width: \"fit-content\"\n                                                                },\n                                                                className: \"jsx-bfe26d46d13290ac\" + \" \" + \"py-3 px-2.5 rounded-[8px] bg-[#F6F6F6] text-left hover:bg-[#D6EDFF] transition-all duration-200 inline-block touch-manipulation\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-bfe26d46d13290ac\" + \" \" + \"text-[16px] font-[600] text-black mb-0.5 leading-tight whitespace-nowrap overflow-hidden text-ellipsis\",\n                                                                        children: card.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                        lineNumber: 1099,\n                                                                        columnNumber: 31\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-bfe26d46d13290ac\" + \" \" + \"text-[16px] text-gray-500 leading-tight whitespace-nowrap overflow-hidden text-ellipsis\",\n                                                                        children: card.subtitle\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                        lineNumber: 1102,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                lineNumber: 1092,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        }, \"\".concat(index, \"-\").concat(card.title), false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 1088,\n                                                            columnNumber: 27\n                                                        }, undefined));\n                                                })() : Array.from({\n                                                    length: Math.ceil(_utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.length / 2)\n                                                }).map((_, slideIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-bfe26d46d13290ac\" + \" \" + \"w-full flex-shrink-0 flex gap-2 justify-center\",\n                                                        children: _utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.slice(slideIndex * 2, slideIndex * 2 + 2).map((card, cardIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleSuggestionClick(card.title),\n                                                                style: {\n                                                                    width: \"fit-content\"\n                                                                },\n                                                                className: \"jsx-bfe26d46d13290ac\" + \" \" + \"py-2 px-2.5 rounded-[8px] bg-[#F6F6F6] text-left group hover:bg-[#D6EDFF] transition-all duration-200 inline-block touch-manipulation\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-bfe26d46d13290ac\" + \" \" + \"text-[14px] font-[600] text-black mb-0.5 leading-tight whitespace-nowrap\",\n                                                                        children: card.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                        lineNumber: 1128,\n                                                                        columnNumber: 31\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-bfe26d46d13290ac\" + \" \" + \"text-[16px] text-gray-500 leading-tight whitespace-nowrap\",\n                                                                        children: card.subtitle\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                        lineNumber: 1131,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                ]\n                                                            }, slideIndex * 2 + cardIndex, true, {\n                                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                lineNumber: 1120,\n                                                                columnNumber: 29\n                                                            }, undefined))\n                                                    }, slideIndex, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1112,\n                                                        columnNumber: 25\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1069,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1068,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handlePrevSlide,\n                                            className: \"jsx-bfe26d46d13290ac\" + \" \" + \"hidden md:block absolute left-28 top-1/2 transform -translate-y-1/2 -translate-x-6 w-8 h-8 bg-white border border-gray-200 rounded-full items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronLeft, {\n                                                className: \"w-3 h-3 text-gray-600  ms-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1146,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1142,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleNextSlide,\n                                            className: \"jsx-bfe26d46d13290ac\" + \" \" + \"hidden md:block absolute right-28 top-1/2 transform -translate-y-1/2 translate-x-6 w-8 h-8 bg-white border border-gray-200 rounded-full items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronRight, {\n                                                className: \"w-3 h-3 text-gray-600 ms-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1152,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1148,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1052,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"200ms\" : \"0ms\",\n                                        position: \"relative\",\n                                        zIndex: 5,\n                                        paddingBottom: \"env(safe-area-inset-bottom)\"\n                                    },\n                                    className: \"jsx-bfe26d46d13290ac\" + \" \" + \"px-4 bg-white transition-all duration-500 \".concat(showInitialUI ? \"opacity-100 translate-y-0\" : \"translate-y-4\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            className: \"jsx-bfe26d46d13290ac\" + \" \" + \"relative\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-bfe26d46d13290ac\" + \" \" + \"relative w-full max-w-[890px] mx-auto\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        ref: messages.length === 0 ? inputRef : null,\n                                                        value: message,\n                                                        onChange: handleInputChange,\n                                                        onKeyDown: handleKeyPress,\n                                                        placeholder: \"Ask anything\",\n                                                        rows: 1,\n                                                        style: {\n                                                            boxSizing: \"border-box\",\n                                                            maxHeight: \"250px\",\n                                                            fontSize: \"16px\",\n                                                            zIndex: 1001,\n                                                            overscrollBehaviorY: \"contain\",\n                                                            position: \"relative\"\n                                                        },\n                                                        className: \"jsx-bfe26d46d13290ac\" + \" \" + \"w-full min-h-[104px] pt-3 px-4 pb-10 pr-12 text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base resize-none overflow-y-auto reset-height\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1169,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        disabled: message.trim().length === 0,\n                                                        className: \"jsx-bfe26d46d13290ac\" + \" \" + \"absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation \".concat(message.trim().length === 0 ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                                            className: \"w-4 h-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 1194,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1186,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1168,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1167,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-bfe26d46d13290ac\" + \" \" + \"text-center py-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-bfe26d46d13290ac\" + \" \" + \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"This chat is powered by\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"jsx-bfe26d46d13290ac\" + \" \" + \"text-black\",\n                                                        children: \"Driply.me\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1201,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1199,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1198,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1157,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 1048,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 926,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"bfe26d46d13290ac\",\n                children: '.loading-dot{-webkit-animation:loading-dots 1.4s infinite ease-in-out both;-moz-animation:loading-dots 1.4s infinite ease-in-out both;-o-animation:loading-dots 1.4s infinite ease-in-out both;animation:loading-dots 1.4s infinite ease-in-out both}.loading-dot:nth-child(1){-webkit-animation-delay:-.32s;-moz-animation-delay:-.32s;-o-animation-delay:-.32s;animation-delay:-.32s}.loading-dot:nth-child(2){-webkit-animation-delay:-.16s;-moz-animation-delay:-.16s;-o-animation-delay:-.16s;animation-delay:-.16s}.loading-dot:nth-child(3){-webkit-animation-delay:0s;-moz-animation-delay:0s;-o-animation-delay:0s;animation-delay:0s}@-webkit-keyframes loading-dots{0%,80%,100%{-webkit-transform:scale(1);transform:scale(1);opacity:1}40%{-webkit-transform:scale(1.5);transform:scale(1.5);opacity:.7}}@-moz-keyframes loading-dots{0%,80%,100%{-moz-transform:scale(1);transform:scale(1);opacity:1}40%{-moz-transform:scale(1.5);transform:scale(1.5);opacity:.7}}@-o-keyframes loading-dots{0%,80%,100%{-o-transform:scale(1);transform:scale(1);opacity:1}40%{-o-transform:scale(1.5);transform:scale(1.5);opacity:.7}}@keyframes loading-dots{0%,80%,100%{-webkit-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1);opacity:1}40%{-webkit-transform:scale(1.5);-moz-transform:scale(1.5);-o-transform:scale(1.5);transform:scale(1.5);opacity:.7}}.hide-scrollbar{-ms-overflow-style:none!important;scrollbar-width:none!important}.hide-scrollbar::-webkit-scrollbar{display:none!important}.message-pair{-webkit-transition:opacity.3s ease-in-out,-webkit-transform.3s ease-in-out;-moz-transition:opacity.3s ease-in-out,-moz-transform.3s ease-in-out;-o-transition:opacity.3s ease-in-out,-o-transform.3s ease-in-out;transition:opacity.3s ease-in-out,-webkit-transform.3s ease-in-out;transition:opacity.3s ease-in-out,-moz-transform.3s ease-in-out;transition:opacity.3s ease-in-out,-o-transform.3s ease-in-out;transition:opacity.3s ease-in-out,transform.3s ease-in-out}[ref*=\"MessagesContainerRef\"]{scroll-behavior:smooth}.message-pair:last-child{opacity:1;-webkit-transform:translatey(0);-moz-transform:translatey(0);-ms-transform:translatey(0);-o-transform:translatey(0);transform:translatey(0)}.message-pair:not(:last-child){opacity:.85}.message-pair:nth-last-child(n+3){opacity:.7}.message-pair:nth-last-child(n+5){opacity:.5}@media only screen and (max-width:1023px){.lg\\\\\\\\.jsx-bfe26d46d13290ac:hidden .absolute.bottom-0.jsx-bfe26d46d13290ac{position:fixed!important;bottom:env(safe-area-inset-bottom,0);left:0;right:0;width:100%;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;z-index:1000;background-color:white}textarea.jsx-bfe26d46d13290ac{-webkit-user-select:auto!important;-moz-user-select:auto!important;-ms-user-select:auto!important;user-select:auto!important;-webkit-appearance:none;-moz-appearance:none;-ms-appearance:none;appearance:none;-ms-scroll-chaining:none;overscroll-behavior:none;line-height:24px;max-height:250px!important;position:relative!important;-webkit-transform:translatez(0);-moz-transform:translatez(0);-ms-transform:translatez(0);-o-transform:translatez(0);transform:translatez(0);-webkit-backface-visibility:hidden;-moz-backface-visibility:hidden;backface-visibility:hidden;-webkit-perspective:1000;-moz-perspective:1000;perspective:1000;z-index:1001!important;background-color:white}.lg\\\\\\\\.jsx-bfe26d46d13290ac:hidden form.jsx-bfe26d46d13290ac{position:relative!important;z-index:1001;background-color:white}button[type=\"submit\"].jsx-bfe26d46d13290ac{position:absolute!important;right:12px;bottom:12px;z-index:1002!important;-webkit-tap-highlight-color:transparent;-ms-touch-action:-ms-manipulation;-ms-touch-action:manipulation;touch-action:manipulation;-webkit-transform:translatez(0);-moz-transform:translatez(0);-ms-transform:translatez(0);-o-transform:translatez(0);transform:translatez(0)}.lg\\\\\\\\.jsx-bfe26d46d13290ac:hidden .bg-white.jsx-bfe26d46d13290ac{background-color:white!important}}@media only screen and (min-width:1024px){textarea.jsx-bfe26d46d13290ac{max-height:250px!important;overflow-y:auto!important;-ms-scroll-chaining:none;overscroll-behavior:contain}.reset-height.jsx-bfe26d46d13290ac{min-height:104px!important;max-height:250px!important;overflow-y:auto!important}}.fixed-bottom.jsx-bfe26d46d13290ac{position:fixed!important;bottom:0;left:0;right:0;background:white}'\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n        lineNumber: 750,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatInterface, \"rVXT4NmLTWQ5AbHzbpGibd4ccew=\", false, function() {\n    return [\n        _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__.useChatContext\n    ];\n});\n_c = ChatInterface;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatInterface);\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ChatInterface.jsx\n"));

/***/ })

});