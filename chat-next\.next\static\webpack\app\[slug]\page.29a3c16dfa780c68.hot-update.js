"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[slug]/page",{

/***/ "(app-pages-browser)/./src/components/ChatInterface.jsx":
/*!******************************************!*\
  !*** ./src/components/ChatInterface.jsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/constants */ \"(app-pages-browser)/./src/utils/constants.js\");\n/* harmony import */ var _utils_config__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/config */ \"(app-pages-browser)/./src/utils/config.js\");\n/* harmony import */ var _barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FaArrowUp,FaChevronLeft,FaChevronRight!=!react-icons/fa6 */ \"(app-pages-browser)/./node_modules/react-icons/fa6/index.mjs\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../contexts/ChatContext */ \"(app-pages-browser)/./src/contexts/ChatContext.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst ChatInterface = (param)=>{\n    let { slug, query } = param;\n    var _settingData_suggestedTopics, _settingData_suggestedTopics1, _settingData_suggestedTopics2;\n    _s();\n    // Context API\n    const { metadata, updateMetadata, setLoadingState, setErrorState, clearError, settingData, updateSettingData, getCustomerName, getBusinessName, hasMetadata, loading: contextLoading, error: contextError } = (0,_contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__.useChatContext)();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [currentSlide, setCurrentSlide] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [touchStart, setTouchStart] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [touchEnd, setTouchEnd] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isTablet, setIsTablet] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showInitialUI, setShowInitialUI] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isBotTyping, setIsBotTyping] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isBotThinking, setIsBotThinking] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [sseConnection, setSseConnection] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isFirstMessage, setIsFirstMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [shouldScrollToTop, setShouldScrollToTop] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Enhanced message pairing state\n    const [pendingQueries, setPendingQueries] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(new Map()); // Track queries waiting for responses\n    const [messageSequence, setMessageSequence] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0); // Global sequence counter\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const carouselRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const desktopMessagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const mobileMessagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const tabletMessagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // Debug: Log state updates when they actually happen\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (metadata && Object.keys(metadata).some({\n                \"ChatInterface.useEffect\": (key)=>metadata[key] !== null\n            }[\"ChatInterface.useEffect\"])) {\n                console.log(\"✅ METADATA STATE UPDATED:\", metadata);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        metadata\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (settingData && Object.keys(settingData).some({\n                \"ChatInterface.useEffect\": (key)=>settingData[key] !== null\n            }[\"ChatInterface.useEffect\"])) {\n                console.log(\"✅ SETTING DATA STATE UPDATED:\", settingData);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        settingData\n    ]);\n    // Cleanup pending queries that are too old (timeout mechanism)\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const cleanup = setInterval({\n                \"ChatInterface.useEffect.cleanup\": ()=>{\n                    const now = Date.now();\n                    const TIMEOUT_MS = 60000; // 60 seconds timeout\n                    setPendingQueries({\n                        \"ChatInterface.useEffect.cleanup\": (prev)=>{\n                            const updated = new Map(prev);\n                            let hasChanges = false;\n                            for (const [queryId, queryData] of prev.entries()){\n                                if (now - queryData.sentAt > TIMEOUT_MS) {\n                                    updated.delete(queryId);\n                                    hasChanges = true;\n                                }\n                            }\n                            return hasChanges ? updated : prev;\n                        }\n                    }[\"ChatInterface.useEffect.cleanup\"]);\n                }\n            }[\"ChatInterface.useEffect.cleanup\"], 10000); // Check every 10 seconds\n            return ({\n                \"ChatInterface.useEffect\": ()=>clearInterval(cleanup)\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], []);\n    // Initialize carousel to start from middle section for better circular behavior\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            var _settingData_suggestedTopics;\n            const totalCards = (settingData === null || settingData === void 0 ? void 0 : (_settingData_suggestedTopics = settingData.suggestedTopics) === null || _settingData_suggestedTopics === void 0 ? void 0 : _settingData_suggestedTopics.length) || 0;\n            if (totalCards > 0 && currentSlide === 0) {\n                // Start from the middle section (second copy of cards)\n                setCurrentSlide(totalCards);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        settingData === null || settingData === void 0 ? void 0 : (_settingData_suggestedTopics = settingData.suggestedTopics) === null || _settingData_suggestedTopics === void 0 ? void 0 : _settingData_suggestedTopics.length\n    ]);\n    // Utility functions for message pairing\n    const generateMessageId = ()=>{\n        const sequence = messageSequence + 1;\n        setMessageSequence(sequence);\n        return \"msg_\".concat(Date.now(), \"_\").concat(sequence, \"_\").concat(Math.random().toString(36).substr(2, 9));\n    };\n    const createMessagePairs = (messages)=>{\n        const pairs = [];\n        const processedMessages = new Set();\n        // Sort messages by timestamp to handle out-of-order arrivals\n        const sortedMessages = [\n            ...messages\n        ].sort((a, b)=>a.timestamp - b.timestamp);\n        // Group messages by queryId for proper pairing\n        const messageGroups = new Map();\n        sortedMessages.forEach((msg)=>{\n            if (msg.source === \"USER\") {\n                // User message starts a new conversation pair\n                const queryId = msg.queryId || msg.id || \"fallback_\".concat(msg.timestamp);\n                if (!messageGroups.has(queryId)) {\n                    messageGroups.set(queryId, {\n                        user: null,\n                        bot: null,\n                        timestamp: msg.timestamp\n                    });\n                }\n                messageGroups.get(queryId).user = msg;\n            } else if (msg.source === \"BOT\") {\n                // Bot message should be paired with corresponding user message\n                const queryId = msg.queryId || msg.responseToId;\n                if (queryId && messageGroups.has(queryId)) {\n                    messageGroups.get(queryId).bot = msg;\n                } else {\n                    // Fallback: pair with most recent unpaired user message\n                    const unpairedGroups = Array.from(messageGroups.entries()).filter((param)=>{\n                        let [_, group] = param;\n                        return group.user && !group.bot;\n                    }).sort((a, b)=>b[1].timestamp - a[1].timestamp);\n                    if (unpairedGroups.length > 0) {\n                        const [groupId, group] = unpairedGroups[0];\n                        group.bot = msg;\n                        // Update the message with proper queryId for future reference\n                        msg.queryId = groupId;\n                    }\n                }\n            }\n        });\n        // Convert groups to pairs array, sorted by timestamp\n        const sortedGroups = Array.from(messageGroups.entries()).sort((a, b)=>a[1].timestamp - b[1].timestamp);\n        return sortedGroups.map((param)=>{\n            let [queryId, group] = param;\n            return {\n                id: queryId,\n                user: group.user,\n                bot: group.bot,\n                timestamp: group.timestamp,\n                isComplete: !!(group.user && group.bot),\n                isPending: !!(group.user && !group.bot)\n            };\n        });\n    };\n    // Detect iOS for targeted fixes\n    const isIOS = ()=>{\n        if (true) {\n            return /iPhone|iPad|iPod/i.test(navigator.userAgent);\n        }\n        return false;\n    };\n    const username = slug;\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (username && \"object\" !== \"undefined\") {\n                fetchCustomer(username);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        username\n    ]);\n    const fetchCustomer = async (customerName)=>{\n        try {\n            setLoadingState(true);\n            clearError();\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"\".concat(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_SETTINGS, \"?customerName=\").concat(customerName));\n            // Extract data from axios response\n            const responseData = response.data;\n            const metadataFromResponse = responseData.metaData;\n            updateSettingData(responseData);\n            updateMetadata(metadataFromResponse);\n            if (responseData && responseData.customerName) {\n                //  localStorage for backward compatibility\n                localStorage.setItem(\"customerName_userId\", responseData.customerName);\n                localStorage.setItem(\"BusinessName\", responseData.businessName);\n                window.dispatchEvent(new CustomEvent(\"businessNameLoaded\", {\n                    detail: {\n                        businessName: responseData.businessName\n                    }\n                }));\n                const existingUserId = localStorage.getItem(\"userID\");\n                if (existingUserId) {\n                    await fetchExistingMessages(existingUserId);\n                } else {\n                    await initializeChatSession();\n                }\n            } else {\n                await initializeChatSession();\n            }\n        } catch (error) {\n            console.error(\"Error fetching customer data:\", error);\n            setErrorState(\"Failed to load customer settings\");\n            // Fallback metadata\n            updateMetadata({\n                customerName: customerName,\n                businessName: \"Driply\"\n            });\n            localStorage.setItem(\"BusinessName\", \"Driply\");\n            window.dispatchEvent(new CustomEvent(\"businessNameLoaded\", {\n                detail: {\n                    businessName: \"Driply\"\n                }\n            }));\n            await initializeChatSession();\n        } finally{\n            setLoadingState(false);\n        }\n    };\n    const fetchExistingMessages = async (userId)=>{\n        // console.log(\"fetchExistingMessages\");\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"\".concat(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_MESSAGES, \"?userId=\").concat(userId));\n            if (response.data && response.data.length > 0) {\n                setMessages(response.data.map((msg)=>({\n                        text: msg.message,\n                        timestamp: new Date(msg.createdAt).getTime(),\n                        type: msg.type,\n                        source: msg.source\n                    })));\n            }\n            connectToSSE(userId);\n        } catch (error) {\n            await initializeChatSession();\n        }\n    };\n    const initializeChatSession = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_INIT, {\n                customerName: username\n            });\n            if (response.data && response.data.userId) {\n                localStorage.setItem(\"userId\", response.data.userId);\n                localStorage.setItem(\"userID\", response.data._id);\n                await fetchExistingMessages(response.data._id);\n            }\n        } catch (error) {\n            if (!localStorage.getItem(\"BusinessName\")) {\n                localStorage.setItem(\"BusinessName\", \"Driply\");\n                window.dispatchEvent(new CustomEvent(\"businessNameLoaded\", {\n                    detail: {\n                        businessName: \"Driply\"\n                    }\n                }));\n            }\n        }\n    };\n    const connectToSSE = (userId)=>{\n        try {\n            if (sseConnection) {\n                sseConnection.close();\n            }\n            const sseUrl = (0,_utils_config__WEBPACK_IMPORTED_MODULE_4__.getSSEUrl)(userId);\n            const eventSource = new EventSource(sseUrl);\n            setSseConnection(eventSource);\n            eventSource.addEventListener(\"message\", (param)=>{\n                let { data } = param;\n                try {\n                    const contents = JSON.parse(data);\n                    handleSSEMessage(contents);\n                } catch (error) {\n                // Handle parsing error silently\n                }\n            });\n            eventSource.addEventListener(\"error\", ()=>{\n            // Handle connection error silently\n            });\n        } catch (error) {\n        // Handle SSE connection error silently\n        }\n    };\n    const handleSSEMessage = (data)=>{\n        const subtype = data.subType || data.subtype || data.type || \"UNKNOWN\";\n        const content = data.content || \"\";\n        const message = data.message || data.text || \"\";\n        if ([\n            \"TYPING\",\n            \"THINKING\",\n            \"BEHAVIOUR_MESSAGE\"\n        ].includes(subtype)) {\n            switch(subtype){\n                case \"TYPING\":\n                    setIsBotTyping(true);\n                    setIsBotThinking(false);\n                    break;\n                case \"THINKING\":\n                    setIsBotThinking(true);\n                    setIsBotTyping(false);\n                    break;\n                case \"BEHAVIOUR_MESSAGE\":\n                    setIsBotTyping(false);\n                    setIsBotThinking(false);\n                    break;\n            }\n            return;\n        }\n        const displayText = message || content;\n        if (!displayText) return;\n        const displayType = [\n            \"TEXT\",\n            \"MESSAGE\",\n            \"DATA_MESSAGE\"\n        ].includes(subtype) ? subtype : \"UNKNOWN\";\n        // Enhanced message pairing logic\n        const responseToId = data.queryId || data.responseToId || data.correlationId;\n        let queryId = responseToId;\n        // If no explicit queryId, find the most recent pending query\n        if (!queryId) {\n            const pendingEntries = Array.from(pendingQueries.entries()).sort((a, b)=>b[1].timestamp - a[1].timestamp);\n            if (pendingEntries.length > 0) {\n                queryId = pendingEntries[0][0];\n            }\n        }\n        // Create bot message with proper pairing information\n        const botMessage = {\n            id: generateMessageId(),\n            text: displayText,\n            timestamp: Date.now(),\n            type: displayType,\n            source: \"BOT\",\n            queryId: queryId,\n            responseToId: queryId // Explicit response relationship\n        };\n        setMessages((prev)=>[\n                ...prev,\n                botMessage\n            ]);\n        // Remove from pending queries if we found a match\n        if (queryId && pendingQueries.has(queryId)) {\n            setPendingQueries((prev)=>{\n                const updated = new Map(prev);\n                updated.delete(queryId);\n                return updated;\n            });\n        }\n        setIsBotTyping(false);\n        setIsBotThinking(false);\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (message.trim()) {\n            const userMessage = message.trim();\n            const isFirstMsg = messages.length === 0;\n            // Generate unique ID for this query\n            const queryId = generateMessageId();\n            const userMessageObj = {\n                id: queryId,\n                queryId: queryId,\n                text: userMessage,\n                timestamp: Date.now(),\n                type: \"TEXT\",\n                source: \"USER\"\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    userMessageObj\n                ]);\n            // Track this query as pending response\n            setPendingQueries((prev)=>new Map(prev).set(queryId, {\n                    id: queryId,\n                    message: userMessage,\n                    timestamp: userMessageObj.timestamp,\n                    sentAt: Date.now()\n                }));\n            setMessage(\"\");\n            setIsTyping(false);\n            // For first message, set flag to scroll to top\n            if (isFirstMsg) {\n                setShouldScrollToTop(true);\n                setIsFirstMessage(false);\n            }\n            // Show loading dots immediately after sending message\n            setIsBotThinking(true);\n            setIsBotTyping(false);\n            // Reset textarea\n            if (inputRef.current) {\n                inputRef.current.style.height = \"104px\";\n                inputRef.current.scrollTop = 0;\n                inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n                inputRef.current.classList.add(\"reset-height\");\n            }\n            try {\n                const userId = localStorage.getItem(\"userID\");\n                if (userId) {\n                    await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_SEND, {\n                        userId: userId,\n                        message: userMessage,\n                        queryId: queryId,\n                        typs: \"TEXT\",\n                        source: \"USER\",\n                        isTest: query.isTest === \"1\" ? true : false\n                    });\n                }\n            } catch (error) {\n                // Hide loading dots on error\n                setIsBotThinking(false);\n                setIsBotTyping(false);\n                const errorMessage = {\n                    id: generateMessageId(),\n                    text: \"Sorry, there was an error sending your message. Please try again.\",\n                    timestamp: Date.now(),\n                    type: \"TEXT\",\n                    source: \"BOT\",\n                    queryId: queryId,\n                    isError: true\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        errorMessage\n                    ]);\n                // Remove from pending queries since we got an error\n                setPendingQueries((prev)=>{\n                    const updated = new Map(prev);\n                    updated.delete(queryId);\n                    return updated;\n                });\n            }\n        }\n    };\n    const handleTextareaResize = (textarea)=>{\n        if (textarea) {\n            textarea.style.height = \"104px\";\n            const scrollHeight = textarea.scrollHeight;\n            if (isIOS()) {\n                if (scrollHeight > 48) {\n                    textarea.style.overflowY = \"auto\";\n                } else {\n                    textarea.style.overflowY = \"hidden\";\n                }\n            } else {\n                const newHeight = Math.min(250, Math.max(104, scrollHeight));\n                textarea.style.height = \"\".concat(newHeight, \"px\");\n                textarea.style.overflowY = scrollHeight > newHeight ? \"auto\" : \"hidden\";\n            }\n        }\n    };\n    const handleInputChange = (e)=>{\n        const value = e.target.value;\n        setMessage(value);\n        handleTextareaResize(e.target);\n        if (messages.length === 0) {\n            setIsTyping(value.length > 0);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length > 0 && inputRef.current && !message) {\n                const timer = setTimeout({\n                    \"ChatInterface.useEffect.timer\": ()=>{\n                        if (inputRef.current) {\n                            inputRef.current.style.height = \"104px\";\n                            inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n                            inputRef.current.classList.add(\"reset-height\");\n                        }\n                    }\n                }[\"ChatInterface.useEffect.timer\"], 50);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                })[\"ChatInterface.useEffect\"];\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages,\n        message\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length > 0) {\n                handleChatGPTScroll();\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages,\n        isBotTyping,\n        isBotThinking\n    ]);\n    const handleChatGPTScroll = ()=>{\n        setTimeout(()=>{\n            // Determine screen type and get appropriate container\n            const screenWidth = window.innerWidth;\n            const isDesktopLayout = screenWidth >= 1024;\n            const isTabletLayout = screenWidth >= 768 && screenWidth < 1024;\n            let container;\n            if (isDesktopLayout) {\n                container = desktopMessagesContainerRef.current;\n            } else if (isTabletLayout) {\n                container = tabletMessagesContainerRef.current;\n            } else {\n                container = mobileMessagesContainerRef.current;\n            }\n            if (!container) return;\n            if (shouldScrollToTop) {\n                // For first message, scroll to top to show the message there\n                container.scrollTo({\n                    top: 0,\n                    behavior: \"smooth\"\n                });\n                setShouldScrollToTop(false);\n            } else {\n                // Different scrolling behavior for each screen type\n                const messageElements = container.querySelectorAll(\".message-pair\");\n                if (messageElements.length > 0) {\n                    const lastMessagePair = messageElements[messageElements.length - 1];\n                    const containerHeight = container.clientHeight;\n                    const pairHeight = lastMessagePair.offsetHeight;\n                    const pairTop = lastMessagePair.offsetTop;\n                    if (isTabletLayout) {\n                        // Tablet-specific scrolling: Keep messages visible by scrolling to bottom of container\n                        // This ensures both user message and AI response remain visible\n                        const scrollPosition = Math.max(0, pairTop + pairHeight - containerHeight + 100);\n                        container.scrollTo({\n                            top: scrollPosition,\n                            behavior: \"smooth\"\n                        });\n                    } else {\n                        // Desktop and mobile: Center the message pair on screen\n                        const scrollPosition = pairTop - (containerHeight - pairHeight) / 2;\n                        container.scrollTo({\n                            top: Math.max(0, scrollPosition),\n                            behavior: \"smooth\"\n                        });\n                    }\n                }\n            }\n        }, 150);\n    };\n    const renderMessagePairs = ()=>{\n        // Use the enhanced message pairing logic\n        const messagePairs = createMessagePairs(messages);\n        return messagePairs.map((pair, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"message-pair flex flex-col justify-start\",\n                \"data-pair-id\": pair.id,\n                \"data-is-complete\": pair.isComplete,\n                \"data-is-pending\": pair.isPending,\n                style: {\n                    minHeight: i === messagePairs.length - 1 ? isMobile ? \"calc(100vh - 200px)\" // Mobile-specific height for newest message\n                     : \"calc(100vh - 200px)\" // Desktop/tablet height for newest message\n                     : \"\",\n                    paddingTop: i === 0 ? \"1rem\" : \"1rem\",\n                    paddingBottom: \"0rem\"\n                },\n                children: [\n                    pair.user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-end mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-[16px] font-[400] rounded-3xl max-w-xs lg:max-w-lg break-words hyphens-auto bg-gray-100 \".concat(isMobile ? \"rounded-[15px] px-3 py-2\" : \"px-4 py-3\"),\n                            \"data-message-id\": pair.user.id,\n                            \"data-query-id\": pair.user.queryId,\n                            style: {\n                                overflowWrap: \"break-word\",\n                                wordBreak: \"break-word\",\n                                maxWidth: isMobile ? \"90%\" : undefined\n                            },\n                            children: [\n                                pair.user.text,\n                                pair.user.isSuggestion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-500 ml-2\",\n                                    children: \"\\uD83D\\uDCA1\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 634,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 620,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 619,\n                        columnNumber: 11\n                    }, undefined),\n                    pair.bot && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-start\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-[16px] font-[400] rounded-3xl max-w-xs lg:max-w-lg break-words hyphens-auto bg-white \".concat(isMobile ? \"rounded-[15px] px-3 py-2\" : \"px-4 py-3\", \" \").concat(pair.bot.isError ? \"border-red-200 bg-red-50\" : \"\"),\n                            \"data-message-id\": pair.bot.id,\n                            \"data-query-id\": pair.bot.queryId,\n                            \"data-response-to\": pair.bot.responseToId,\n                            style: {\n                                overflowWrap: \"break-word\",\n                                wordBreak: \"break-word\",\n                                maxWidth: isMobile ? \"90%\" : undefined\n                            },\n                            children: [\n                                pair.bot.text,\n                                pair.bot.isError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-red-500 ml-2\",\n                                    children: \"⚠️\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 658,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 643,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 642,\n                        columnNumber: 11\n                    }, undefined),\n                    i === messagePairs.length - 1 && (isBotTyping || isBotThinking) && !pair.bot && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-start\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white \".concat(isMobile ? \"px-3 py-2 rounded-[15px]\" : \"px-4 py-3 rounded-3xl\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-black rounded-full loading-dot\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 677,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-black rounded-full loading-dot\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 678,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-black rounded-full loading-dot\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 679,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 676,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 669,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 668,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, \"pair-\".concat(pair.id), true, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 600,\n                columnNumber: 7\n            }, undefined));\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSubmit(e);\n        }\n    };\n    const handleSuggestionClick = async (cardTitle)=>{\n        const isFirstMsg = messages.length === 0;\n        // Generate unique ID for this suggestion query\n        const queryId = generateMessageId();\n        const userMessageObj = {\n            id: queryId,\n            queryId: queryId,\n            text: cardTitle,\n            timestamp: Date.now(),\n            type: \"TEXT\",\n            source: \"USER\",\n            isSuggestion: true\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessageObj\n            ]);\n        // Track this suggestion query as pending response\n        setPendingQueries((prev)=>new Map(prev).set(queryId, {\n                id: queryId,\n                message: cardTitle,\n                timestamp: userMessageObj.timestamp,\n                sentAt: Date.now(),\n                isSuggestion: true\n            }));\n        setMessage(\"\");\n        setIsTyping(false);\n        // For first message, set flag to scroll to top\n        if (isFirstMsg) {\n            setShouldScrollToTop(true);\n            setIsFirstMessage(false);\n        }\n        // Show loading dots immediately after sending message\n        setIsBotThinking(true);\n        setIsBotTyping(false);\n        if (inputRef.current) {\n            inputRef.current.style.height = \"104px\";\n            inputRef.current.scrollTop = 0;\n            inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n            inputRef.current.classList.add(\"reset-height\");\n        }\n        try {\n            const userId = localStorage.getItem(\"userID\");\n            if (userId) {\n                await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_SEND, {\n                    userId: userId,\n                    message: cardTitle,\n                    queryId: queryId,\n                    customerName: username,\n                    isSuggestion: true\n                });\n            }\n        } catch (error) {\n            // Hide loading dots on error\n            setIsBotThinking(false);\n            setIsBotTyping(false);\n            const errorMessage = {\n                id: generateMessageId(),\n                text: \"Sorry, there was an error sending your message. Please try again.\",\n                timestamp: Date.now(),\n                type: \"TEXT\",\n                source: \"BOT\",\n                queryId: queryId,\n                isError: true\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n            // Remove from pending queries since we got an error\n            setPendingQueries((prev)=>{\n                const updated = new Map(prev);\n                updated.delete(queryId);\n                return updated;\n            });\n        }\n    };\n    const getMaxSlides = ()=>{\n        if (isMobile) return settingData === null || settingData === void 0 ? void 0 : settingData.suggestedTopics.length;\n        return Math.ceil((settingData === null || settingData === void 0 ? void 0 : settingData.suggestedTopics.length) / 2);\n    };\n    const nextSlide = ()=>{\n        var _settingData_suggestedTopics;\n        const totalCards = (settingData === null || settingData === void 0 ? void 0 : (_settingData_suggestedTopics = settingData.suggestedTopics) === null || _settingData_suggestedTopics === void 0 ? void 0 : _settingData_suggestedTopics.length) || 0;\n        if (totalCards === 0) return;\n        // Calculate scroll distance to ensure last card is fully visible\n        // Reduce scroll distance to show partial cards and ensure last card visibility\n        const scrollStep = isMobile ? 0.7 : 0.8; // Scroll by 70% on mobile, 80% on desktop\n        const maxSlide = Math.max(0, totalCards - 1); // Allow scrolling to show last card fully\n        setCurrentSlide((prev)=>Math.min(prev + scrollStep, maxSlide));\n    };\n    const prevSlide = ()=>{\n        const scrollStep = isMobile ? 0.7 : 0.8; // Same scroll step for consistency\n        setCurrentSlide((prev)=>Math.max(prev - scrollStep, 0));\n    };\n    // Check if we're at the boundaries for button states\n    const isAtStart = currentSlide === 0;\n    const isAtEnd = ()=>{\n        var _settingData_suggestedTopics;\n        const totalCards = (settingData === null || settingData === void 0 ? void 0 : (_settingData_suggestedTopics = settingData.suggestedTopics) === null || _settingData_suggestedTopics === void 0 ? void 0 : _settingData_suggestedTopics.length) || 0;\n        const maxSlide = Math.max(0, totalCards - 1);\n        return currentSlide >= maxSlide;\n    };\n    const handleNextSlide = ()=>{\n        nextSlide();\n    };\n    const handlePrevSlide = ()=>{\n        prevSlide();\n    };\n    const handleTouchStart = (e)=>{\n        setTouchStart(e.targetTouches[0].clientX);\n    };\n    const handleTouchMove = (e)=>{\n        setTouchEnd(e.targetTouches[0].clientX);\n    };\n    const handleTouchEnd = ()=>{\n        if (!touchStart || !touchEnd) return;\n        const swipeDistance = touchStart - touchEnd;\n        const minSwipeDistance = 50;\n        if (swipeDistance > minSwipeDistance) {\n            nextSlide();\n        }\n        if (swipeDistance < -minSwipeDistance) {\n            prevSlide();\n        }\n        setTouchStart(0);\n        setTouchEnd(0);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length === 0) {\n                setShowInitialUI(false);\n                const timer = setTimeout({\n                    \"ChatInterface.useEffect.timer\": ()=>setShowInitialUI(true)\n                }[\"ChatInterface.useEffect.timer\"], 60);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                })[\"ChatInterface.useEffect\"];\n            } else {\n                setShowInitialUI(false);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages.length\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const checkScreenSize = {\n                \"ChatInterface.useEffect.checkScreenSize\": ()=>{\n                    if (true) {\n                        const width = window.innerWidth;\n                        setIsMobile(width < 768);\n                        setIsTablet(width >= 768 && width < 1024);\n                    }\n                }\n            }[\"ChatInterface.useEffect.checkScreenSize\"];\n            checkScreenSize();\n            const handleResize = {\n                \"ChatInterface.useEffect.handleResize\": ()=>{\n                    setCurrentSlide(0);\n                    checkScreenSize();\n                }\n            }[\"ChatInterface.useEffect.handleResize\"];\n            if (true) {\n                window.addEventListener(\"resize\", handleResize);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>window.removeEventListener(\"resize\", handleResize)\n                })[\"ChatInterface.useEffect\"];\n            }\n        }\n    }[\"ChatInterface.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (inputRef.current && messages.length === 0) {\n                const shouldAutoFocus = window.innerWidth >= 768;\n                if (shouldAutoFocus) {\n                    const timer = setTimeout({\n                        \"ChatInterface.useEffect.timer\": ()=>{\n                            var _inputRef_current;\n                            (_inputRef_current = inputRef.current) === null || _inputRef_current === void 0 ? void 0 : _inputRef_current.focus();\n                        }\n                    }[\"ChatInterface.useEffect.timer\"], 100);\n                    return ({\n                        \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                    })[\"ChatInterface.useEffect\"];\n                }\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages.length\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            return ({\n                \"ChatInterface.useEffect\": ()=>{\n                    if (sseConnection) {\n                        sseConnection.close();\n                    }\n                }\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], [\n        sseConnection\n    ]);\n    // Check if settingData has been populated with actual data\n    const hasSettingData = settingData && Object.keys(settingData).some((key)=>settingData[key] !== null);\n    // Show loading state while waiting for data\n    if (!hasSettingData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white flex flex-col min-h-screen items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 912,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading chat settings...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 913,\n                        columnNumber: 11\n                    }, undefined),\n                    contextError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-500 mt-2\",\n                        children: [\n                            \"Error: \",\n                            contextError\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 915,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 911,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n            lineNumber: 910,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"bg-white flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"hidden lg:flex flex-1 flex-col px-4 \",\n                children: messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex flex-1 items-center justify-center min-h-[calc(100vh-64px)] mt-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex flex-col items-center w-[768px] justify-center mx-auto \",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    transitionDelay: showInitialUI ? \"40ms\" : \"0ms\"\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-4xl text-gray-900 mb-6 text-center transition-all duration-700 \".concat(showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                children: \"How can I help you?\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 929,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                style: {\n                                    transitionDelay: showInitialUI ? \"120ms\" : \"0ms\"\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"relative w-full mb-6 flex flex-col items-center transition-all duration-500 \".concat(showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        ref: messages.length === 0 ? inputRef : null,\n                                        value: message,\n                                        onChange: handleInputChange,\n                                        onKeyDown: handleKeyPress,\n                                        placeholder: \"Ask anything\",\n                                        autoFocus: messages.length === 0,\n                                        rows: 1,\n                                        style: {\n                                            boxSizing: \"border-box\",\n                                            zIndex: 1001,\n                                            position: \"relative\"\n                                        },\n                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"pt-3 px-4 lg:p-4 pb-16 pr-12 w-full min-h-[104px] max-h-[250px] overflow-y-auto hide-scrollbar text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base shadow-md resize-none reset-height\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 949,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: message.trim().length === 0,\n                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"absolute right-3 bottom-3 w-9 h-9 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation \".concat(message.trim().length === 0 ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                            className: \"w-4 h-4 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 973,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 964,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 940,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    transitionDelay: showInitialUI ? \"200ms\" : \"0ms\",\n                                    zIndex: 10\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"relative w-full max-w-2xl transition-all duration-500 ease-in-out \".concat(isTyping ? \"opacity-0 pointer-events-none\" : showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            paddingRight: '100px'\n                                        },\n                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"overflow-hidden px-12\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: carouselRef,\n                                            style: {\n                                                transform: \"translateX(-\".concat(currentSlide * (isMobile ? 200 : 250), \"px)\"),\n                                                paddingLeft: \"0\"\n                                            },\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex gap-2 transition-transform duration-300 ease-in-out justify-start\",\n                                            children: settingData === null || settingData === void 0 ? void 0 : (_settingData_suggestedTopics1 = settingData.suggestedTopics) === null || _settingData_suggestedTopics1 === void 0 ? void 0 : _settingData_suggestedTopics1.map((card, cardIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleSuggestionClick(card === null || card === void 0 ? void 0 : card.question),\n                                                    style: {\n                                                        minWidth: \"fit-content\",\n                                                        maxWidth: \"300px\",\n                                                        width: \"auto\"\n                                                    },\n                                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"py-2 px-3 rounded-[8px] bg-[#f6f6f6] text-left group transition-all duration-200 touch-manipulation flex-shrink-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-[16px] font-[600] text-black mb-0.5 leading-tight\",\n                                                            children: card.question\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 1011,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-[16px] text-gray-500 leading-tight\",\n                                                            children: card.subtitle\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 1014,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, cardIndex, true, {\n                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                    lineNumber: 999,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 990,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 989,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handlePrevSlide,\n                                        disabled: isAtStart,\n                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-10 w-8 h-8 border rounded-full flex items-center justify-center shadow-md transition-all duration-200 touch-manipulation z-10 \".concat(isAtStart ? 'bg-gray-100 border-gray-300 cursor-not-allowed' : 'bg-white border-gray-200 hover:bg-gray-50 cursor-pointer'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronLeft, {\n                                            className: \"w-3 h-3 \".concat(isAtStart ? 'text-gray-400' : 'text-gray-600')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1030,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 1021,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleNextSlide,\n                                        disabled: isAtEnd(),\n                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-10 w-8 h-8 border rounded-full flex items-center justify-center shadow-md transition-all duration-200 touch-manipulation z-10 \".concat(isAtEnd() ? 'bg-gray-100 border-gray-300 cursor-not-allowed' : 'bg-white border-gray-200 hover:bg-gray-50 cursor-pointer'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronRight, {\n                                            className: \"w-3 h-3 \".concat(isAtEnd() ? 'text-gray-400' : 'text-gray-600')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1041,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 1032,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 976,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 928,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                    lineNumber: 927,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: desktopMessagesContainerRef,\n                            style: {\n                                overscrollBehavior: \"contain\"\n                            },\n                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex-1 overflow-y-auto max-h-[calc(100vh-200px)] mt-14\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"w-full lg:w-[768px] mx-auto px-4 h-fit \",\n                                children: [\n                                    renderMessagePairs(),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: messagesEndRef,\n                                        className: \"jsx-69ac67aa0147cd65\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 1055,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 1053,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 1048,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                zIndex: 1000,\n                                marginTop: \"20px\"\n                            },\n                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"fixed bottom-0 left-0 right-0 p-4 mt-5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSubmit,\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"relative w-full lg:w-[768px] mx-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            ref: messages.length > 0 ? inputRef : null,\n                                            value: message,\n                                            onChange: handleInputChange,\n                                            onKeyDown: handleKeyPress,\n                                            placeholder: \"Ask anything\",\n                                            rows: 1,\n                                            style: {\n                                                boxSizing: \"border-box\",\n                                                zIndex: 1001,\n                                                position: \"relative\"\n                                            },\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"pt-3 px-4 md:px-5 pb-10 pr-12 w-full min-h-[104px] max-h-[250px] overflow-y-auto text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base shadow-lg resize-none reset-height hide-scrollbar\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1067,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: message.trim().length === 0,\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation \".concat(message.trim().length === 0 ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                                className: \"w-4 h-4 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1090,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1081,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1063,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-center mt-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-xs text-gray-500\",\n                                        children: [\n                                            \"This chat is powered by\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-black\",\n                                                children: \"Driply.me\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1096,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 1094,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1093,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 1059,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 925,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"lg:hidden overflow-hidden fixed inset-0 flex flex-col  mt-10 \",\n                children: [\n                    messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex flex-col items-center pt-15 pb-4 px-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"hidden md:flex lg:hidden flex-col items-center\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1109,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 1108,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    paddingBottom: \"260px\"\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex flex-col items-center justify-center flex-1 px-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"40ms\" : \"0ms\"\n                                    },\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-2xl md:text-4xl text-gray-900 text-center leading-relaxed transition-all duration-700 \".concat(showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                    children: \"How can I help you?\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1115,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 1111,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: mobileMessagesContainerRef,\n                                style: {\n                                    overscrollBehavior: \"contain\"\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"md:hidden flex-1 overflow-y-auto max-h-[calc(100vh-190px)] hide-scrollbar\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"w-full max-w-[803px] mx-auto px-4  pt-12\",\n                                    children: [\n                                        renderMessagePairs(),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: messagesEndRef,\n                                            className: \"jsx-69ac67aa0147cd65\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1137,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1135,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 1130,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: tabletMessagesContainerRef,\n                                style: {\n                                    overscrollBehavior: \"contain\"\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"hidden md:block lg:hidden flex-1 overflow-y-auto max-h-[calc(100vh-100px)]  pb-20 hide-scrollbar\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"w-full max-w-[803px] mx-auto px-4 pb-6 pt-12\",\n                                    children: [\n                                        renderMessagePairs(),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: messagesEndRef,\n                                            className: \"jsx-69ac67aa0147cd65\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1149,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1147,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 1142,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    minHeight: \"160px\",\n                                    zIndex: 1000,\n                                    paddingBottom: \"env(safe-area-inset-bottom, 0)\",\n                                    transform: \"translateZ(0)\",\n                                    backfaceVisibility: \"hidden\"\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"fixed bottom-0 left-0 right-0 bg-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        transform: \"translateZ(0)\"\n                                    },\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"px-4 py-4 bg-white\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            style: {\n                                                zIndex: 1001\n                                            },\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"relative bg-white\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    transform: \"translateZ(0)\"\n                                                },\n                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"relative w-full max-w-[890px] mx-auto bg-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        ref: messages.length > 0 ? inputRef : null,\n                                                        value: message,\n                                                        onChange: handleInputChange,\n                                                        onKeyDown: handleKeyPress,\n                                                        placeholder: \"Ask anything\",\n                                                        rows: 1,\n                                                        style: {\n                                                            boxSizing: \"border-box\",\n                                                            maxHeight: \"250px\",\n                                                            zIndex: 1001,\n                                                            overscrollBehavior: \"none\",\n                                                            position: \"relative\"\n                                                        },\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"w-full min-h-[104px] pt-3 px-4 pb-10 pr-12 text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base resize-none overflow-y-auto reset-height\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1176,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        disabled: message.trim().length === 0,\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation \".concat(message.trim().length === 0 ? \"bg-[#E0E0E0] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                                            className: \"w-4 h-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 1201,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1192,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1172,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1167,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-center py-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"This chat is powered by\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-black\",\n                                                        children: \"Driply.me\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1208,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1206,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1205,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1163,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 1153,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"absolute bottom-0 left-0 right-0 bg-white\",\n                        children: messages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"120ms\" : \"0ms\",\n                                        position: \"relative\",\n                                        zIndex: 10,\n                                        backgroundColor: \"white\",\n                                        paddingBottom: \"10px\"\n                                    },\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"\".concat(isMobile ? \"px-0\" : \"px-4\", \" pt-2 pb-2 transition-all duration-500 ease-in-out \").concat(isTyping ? \"opacity-0 pointer-events-none\" : showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                paddingRight: isMobile ? '80px' : '100px'\n                                            },\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"overflow-hidden \".concat(isMobile ? 'px-4' : 'px-12'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                ref: carouselRef,\n                                                style: {\n                                                    transform: \"translateX(-\".concat(currentSlide * (isMobile ? 150 : 200), \"px)\"),\n                                                    paddingLeft: \"0\"\n                                                },\n                                                onTouchStart: handleTouchStart,\n                                                onTouchMove: handleTouchMove,\n                                                onTouchEnd: handleTouchEnd,\n                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex gap-2 transition-transform duration-300 ease-in-out justify-start\",\n                                                children: settingData === null || settingData === void 0 ? void 0 : (_settingData_suggestedTopics2 = settingData.suggestedTopics) === null || _settingData_suggestedTopics2 === void 0 ? void 0 : _settingData_suggestedTopics2.map((card, cardIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex-shrink-0 \".concat(isMobile ? 'mt-3 mr-2' : 'mr-2'),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleSuggestionClick(card.question),\n                                                            style: {\n                                                                minWidth: \"fit-content\",\n                                                                maxWidth: isMobile ? \"280px\" : \"300px\",\n                                                                width: \"auto\"\n                                                            },\n                                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"\".concat(isMobile ? 'py-3' : 'py-2', \" px-2.5 rounded-[8px] bg-[#F6F6F6] text-left hover:bg-[#D6EDFF] transition-all duration-200 inline-block touch-manipulation\"),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"\".concat(isMobile ? 'text-[16px]' : 'text-[14px]', \" font-[600] text-black mb-0.5 leading-tight\"),\n                                                                    children: card.question\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                    lineNumber: 1270,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-[16px] text-gray-500 leading-tight\",\n                                                                    children: card.subtitle\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                    lineNumber: 1273,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 1259,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, cardIndex, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1255,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1243,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1239,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handlePrevSlide,\n                                            disabled: isAtStart,\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"hidden md:flex absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-10 w-8 h-8 border rounded-full items-center justify-center shadow-md transition-all duration-200 touch-manipulation z-10 \".concat(isAtStart ? 'bg-gray-100 border-gray-300 cursor-not-allowed' : 'bg-white border-gray-200 hover:bg-gray-50 cursor-pointer'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronLeft, {\n                                                className: \"w-3 h-3 \".concat(isAtStart ? 'text-gray-400' : 'text-gray-600')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1292,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1283,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleNextSlide,\n                                            disabled: isAtEnd(),\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"hidden md:flex absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-10 w-8 h-8 border rounded-full items-center justify-center shadow-md transition-all duration-200 touch-manipulation z-10 \".concat(isAtEnd() ? 'bg-gray-100 border-gray-300 cursor-not-allowed' : 'bg-white border-gray-200 hover:bg-gray-50 cursor-pointer'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronRight, {\n                                                className: \"w-3 h-3 \".concat(isAtEnd() ? 'text-gray-400' : 'text-gray-600')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1303,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1294,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1221,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"200ms\" : \"0ms\",\n                                        position: \"relative\",\n                                        zIndex: 5,\n                                        paddingBottom: \"env(safe-area-inset-bottom)\"\n                                    },\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"px-4 bg-white transition-all duration-500 \".concat(showInitialUI ? \"opacity-100 translate-y-0\" : \"translate-y-4\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"relative\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"relative w-full max-w-[890px] mx-auto\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        ref: messages.length === 0 ? inputRef : null,\n                                                        value: message,\n                                                        onChange: handleInputChange,\n                                                        onKeyDown: handleKeyPress,\n                                                        placeholder: \"Ask anything\",\n                                                        rows: 1,\n                                                        style: {\n                                                            boxSizing: \"border-box\",\n                                                            maxHeight: \"250px\",\n                                                            fontSize: \"16px\",\n                                                            zIndex: 1001,\n                                                            overscrollBehaviorY: \"contain\",\n                                                            position: \"relative\"\n                                                        },\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"w-full min-h-[104px] pt-3 px-4 pb-10 pr-12 text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base resize-none overflow-y-auto reset-height\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1321,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        disabled: message.trim().length === 0,\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation \".concat(message.trim().length === 0 ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                                            className: \"w-4 h-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 1347,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1338,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1320,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1319,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-center py-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"This chat is powered by\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-black\",\n                                                        children: \"Driply.me\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1354,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1352,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1351,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1308,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 1217,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 1105,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"69ac67aa0147cd65\",\n                children: '.loading-dot{-webkit-animation:loading-dots 1.4s infinite ease-in-out both;-moz-animation:loading-dots 1.4s infinite ease-in-out both;-o-animation:loading-dots 1.4s infinite ease-in-out both;animation:loading-dots 1.4s infinite ease-in-out both}.loading-dot:nth-child(1){-webkit-animation-delay:-.32s;-moz-animation-delay:-.32s;-o-animation-delay:-.32s;animation-delay:-.32s}.loading-dot:nth-child(2){-webkit-animation-delay:-.16s;-moz-animation-delay:-.16s;-o-animation-delay:-.16s;animation-delay:-.16s}.loading-dot:nth-child(3){-webkit-animation-delay:0s;-moz-animation-delay:0s;-o-animation-delay:0s;animation-delay:0s}@-webkit-keyframes loading-dots{0%,80%,100%{-webkit-transform:scale(1);transform:scale(1);opacity:1}40%{-webkit-transform:scale(1.5);transform:scale(1.5);opacity:.7}}@-moz-keyframes loading-dots{0%,80%,100%{-moz-transform:scale(1);transform:scale(1);opacity:1}40%{-moz-transform:scale(1.5);transform:scale(1.5);opacity:.7}}@-o-keyframes loading-dots{0%,80%,100%{-o-transform:scale(1);transform:scale(1);opacity:1}40%{-o-transform:scale(1.5);transform:scale(1.5);opacity:.7}}@keyframes loading-dots{0%,80%,100%{-webkit-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1);opacity:1}40%{-webkit-transform:scale(1.5);-moz-transform:scale(1.5);-o-transform:scale(1.5);transform:scale(1.5);opacity:.7}}.hide-scrollbar{-ms-overflow-style:none!important;scrollbar-width:none!important}.hide-scrollbar::-webkit-scrollbar{display:none!important}@media only screen and (max-width:1023px){.lg\\\\\\\\.jsx-69ac67aa0147cd65:hidden .absolute.bottom-0.jsx-69ac67aa0147cd65{position:fixed!important;bottom:env(safe-area-inset-bottom,0);left:0;right:0;width:100%;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;z-index:1000;background-color:white}textarea.jsx-69ac67aa0147cd65{-webkit-user-select:auto!important;-moz-user-select:auto!important;-ms-user-select:auto!important;user-select:auto!important;-webkit-appearance:none;-moz-appearance:none;-ms-appearance:none;appearance:none;-ms-scroll-chaining:none;overscroll-behavior:none;line-height:24px;max-height:250px!important;position:relative!important;-webkit-transform:translatez(0);-moz-transform:translatez(0);-ms-transform:translatez(0);-o-transform:translatez(0);transform:translatez(0);-webkit-backface-visibility:hidden;-moz-backface-visibility:hidden;backface-visibility:hidden;-webkit-perspective:1000;-moz-perspective:1000;perspective:1000;z-index:1001!important;background-color:white}.lg\\\\\\\\.jsx-69ac67aa0147cd65:hidden form.jsx-69ac67aa0147cd65{position:relative!important;z-index:1001;background-color:white}button[type=\"submit\"].jsx-69ac67aa0147cd65{position:absolute!important;right:12px;bottom:12px;z-index:1002!important;-webkit-tap-highlight-color:transparent;-ms-touch-action:-ms-manipulation;-ms-touch-action:manipulation;touch-action:manipulation;-webkit-transform:translatez(0);-moz-transform:translatez(0);-ms-transform:translatez(0);-o-transform:translatez(0);transform:translatez(0)}.lg\\\\\\\\.jsx-69ac67aa0147cd65:hidden .bg-white.jsx-69ac67aa0147cd65{background-color:white!important}}@media only screen and (min-width:1024px){textarea.jsx-69ac67aa0147cd65{max-height:250px!important;overflow-y:auto!important;-ms-scroll-chaining:none;overscroll-behavior:contain}.reset-height.jsx-69ac67aa0147cd65{min-height:104px!important;max-height:250px!important;overflow-y:auto!important}}.fixed-bottom.jsx-69ac67aa0147cd65{position:fixed!important;bottom:0;left:0;right:0;background:white}'\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n        lineNumber: 923,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatInterface, \"11I1ZIMf13g4EsXvUwg83BGQ4a8=\", false, function() {\n    return [\n        _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__.useChatContext\n    ];\n});\n_c = ChatInterface;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatInterface);\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ChatInterface.jsx\n"));

/***/ })

});