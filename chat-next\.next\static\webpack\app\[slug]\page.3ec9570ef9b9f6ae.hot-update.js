"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[slug]/page",{

/***/ "(app-pages-browser)/./src/components/ChatInterface.jsx":
/*!******************************************!*\
  !*** ./src/components/ChatInterface.jsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/constants */ \"(app-pages-browser)/./src/utils/constants.js\");\n/* harmony import */ var _utils_config__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/config */ \"(app-pages-browser)/./src/utils/config.js\");\n/* harmony import */ var _barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FaArrowUp,FaChevronLeft,FaChevronRight!=!react-icons/fa6 */ \"(app-pages-browser)/./node_modules/react-icons/fa6/index.mjs\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../contexts/ChatContext */ \"(app-pages-browser)/./src/contexts/ChatContext.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst ChatInterface = (param)=>{\n    let { slug, query } = param;\n    var _settingData_suggestedTopics, _settingData_suggestedTopics1, _settingData_suggestedTopics2;\n    _s();\n    // Context API\n    const { metadata, updateMetadata, setLoadingState, setErrorState, clearError, settingData, updateSettingData, getCustomerName, getBusinessName, hasMetadata, loading: contextLoading, error: contextError } = (0,_contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__.useChatContext)();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [currentSlide, setCurrentSlide] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [touchStart, setTouchStart] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [touchEnd, setTouchEnd] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isTablet, setIsTablet] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showInitialUI, setShowInitialUI] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isBotTyping, setIsBotTyping] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isBotThinking, setIsBotThinking] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [sseConnection, setSseConnection] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isFirstMessage, setIsFirstMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [shouldScrollToTop, setShouldScrollToTop] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Enhanced message pairing state\n    const [pendingQueries, setPendingQueries] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(new Map()); // Track queries waiting for responses\n    const [messageSequence, setMessageSequence] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0); // Global sequence counter\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const carouselRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const desktopMessagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const mobileMessagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const tabletMessagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // Debug: Log state updates when they actually happen\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (metadata && Object.keys(metadata).some({\n                \"ChatInterface.useEffect\": (key)=>metadata[key] !== null\n            }[\"ChatInterface.useEffect\"])) {\n                console.log(\"✅ METADATA STATE UPDATED:\", metadata);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        metadata\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (settingData && Object.keys(settingData).some({\n                \"ChatInterface.useEffect\": (key)=>settingData[key] !== null\n            }[\"ChatInterface.useEffect\"])) {\n                console.log(\"✅ SETTING DATA STATE UPDATED:\", settingData);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        settingData\n    ]);\n    // Cleanup pending queries that are too old (timeout mechanism)\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const cleanup = setInterval({\n                \"ChatInterface.useEffect.cleanup\": ()=>{\n                    const now = Date.now();\n                    const TIMEOUT_MS = 60000; // 60 seconds timeout\n                    setPendingQueries({\n                        \"ChatInterface.useEffect.cleanup\": (prev)=>{\n                            const updated = new Map(prev);\n                            let hasChanges = false;\n                            for (const [queryId, queryData] of prev.entries()){\n                                if (now - queryData.sentAt > TIMEOUT_MS) {\n                                    updated.delete(queryId);\n                                    hasChanges = true;\n                                }\n                            }\n                            return hasChanges ? updated : prev;\n                        }\n                    }[\"ChatInterface.useEffect.cleanup\"]);\n                }\n            }[\"ChatInterface.useEffect.cleanup\"], 10000); // Check every 10 seconds\n            return ({\n                \"ChatInterface.useEffect\": ()=>clearInterval(cleanup)\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], []);\n    // Initialize carousel to start from middle section for better circular behavior\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            var _settingData_suggestedTopics;\n            const totalCards = (settingData === null || settingData === void 0 ? void 0 : (_settingData_suggestedTopics = settingData.suggestedTopics) === null || _settingData_suggestedTopics === void 0 ? void 0 : _settingData_suggestedTopics.length) || 0;\n            if (totalCards > 0 && currentSlide === 0) {\n                // Start from the middle section (second copy of cards)\n                setCurrentSlide(totalCards);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        settingData === null || settingData === void 0 ? void 0 : (_settingData_suggestedTopics = settingData.suggestedTopics) === null || _settingData_suggestedTopics === void 0 ? void 0 : _settingData_suggestedTopics.length\n    ]);\n    // Utility functions for message pairing\n    const generateMessageId = ()=>{\n        const sequence = messageSequence + 1;\n        setMessageSequence(sequence);\n        return \"msg_\".concat(Date.now(), \"_\").concat(sequence, \"_\").concat(Math.random().toString(36).substr(2, 9));\n    };\n    const createMessagePairs = (messages)=>{\n        const pairs = [];\n        const processedMessages = new Set();\n        // Sort messages by timestamp to handle out-of-order arrivals\n        const sortedMessages = [\n            ...messages\n        ].sort((a, b)=>a.timestamp - b.timestamp);\n        // Group messages by queryId for proper pairing\n        const messageGroups = new Map();\n        sortedMessages.forEach((msg)=>{\n            if (msg.source === \"USER\") {\n                // User message starts a new conversation pair\n                const queryId = msg.queryId || msg.id || \"fallback_\".concat(msg.timestamp);\n                if (!messageGroups.has(queryId)) {\n                    messageGroups.set(queryId, {\n                        user: null,\n                        bot: null,\n                        timestamp: msg.timestamp\n                    });\n                }\n                messageGroups.get(queryId).user = msg;\n            } else if (msg.source === \"BOT\") {\n                // Bot message should be paired with corresponding user message\n                const queryId = msg.queryId || msg.responseToId;\n                if (queryId && messageGroups.has(queryId)) {\n                    messageGroups.get(queryId).bot = msg;\n                } else {\n                    // Fallback: pair with most recent unpaired user message\n                    const unpairedGroups = Array.from(messageGroups.entries()).filter((param)=>{\n                        let [_, group] = param;\n                        return group.user && !group.bot;\n                    }).sort((a, b)=>b[1].timestamp - a[1].timestamp);\n                    if (unpairedGroups.length > 0) {\n                        const [groupId, group] = unpairedGroups[0];\n                        group.bot = msg;\n                        // Update the message with proper queryId for future reference\n                        msg.queryId = groupId;\n                    }\n                }\n            }\n        });\n        // Convert groups to pairs array, sorted by timestamp\n        const sortedGroups = Array.from(messageGroups.entries()).sort((a, b)=>a[1].timestamp - b[1].timestamp);\n        return sortedGroups.map((param)=>{\n            let [queryId, group] = param;\n            return {\n                id: queryId,\n                user: group.user,\n                bot: group.bot,\n                timestamp: group.timestamp,\n                isComplete: !!(group.user && group.bot),\n                isPending: !!(group.user && !group.bot)\n            };\n        });\n    };\n    // Detect iOS for targeted fixes\n    const isIOS = ()=>{\n        if (true) {\n            return /iPhone|iPad|iPod/i.test(navigator.userAgent);\n        }\n        return false;\n    };\n    const username = slug;\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (username && \"object\" !== \"undefined\") {\n                fetchCustomer(username);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        username\n    ]);\n    const fetchCustomer = async (customerName)=>{\n        try {\n            setLoadingState(true);\n            clearError();\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"\".concat(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_SETTINGS, \"?customerName=\").concat(customerName));\n            // Extract data from axios response\n            const responseData = response.data;\n            const metadataFromResponse = responseData.metaData;\n            updateSettingData(responseData);\n            updateMetadata(metadataFromResponse);\n            if (responseData && responseData.customerName) {\n                //  localStorage for backward compatibility\n                localStorage.setItem(\"customerName_userId\", responseData.customerName);\n                localStorage.setItem(\"BusinessName\", responseData.businessName);\n                window.dispatchEvent(new CustomEvent(\"businessNameLoaded\", {\n                    detail: {\n                        businessName: responseData.businessName\n                    }\n                }));\n                const existingUserId = localStorage.getItem(\"userID\");\n                if (existingUserId) {\n                    await fetchExistingMessages(existingUserId);\n                } else {\n                    await initializeChatSession();\n                }\n            } else {\n                await initializeChatSession();\n            }\n        } catch (error) {\n            console.error(\"Error fetching customer data:\", error);\n            setErrorState(\"Failed to load customer settings\");\n            // Fallback metadata\n            updateMetadata({\n                customerName: customerName,\n                businessName: \"Driply\"\n            });\n            localStorage.setItem(\"BusinessName\", \"Driply\");\n            window.dispatchEvent(new CustomEvent(\"businessNameLoaded\", {\n                detail: {\n                    businessName: \"Driply\"\n                }\n            }));\n            await initializeChatSession();\n        } finally{\n            setLoadingState(false);\n        }\n    };\n    const fetchExistingMessages = async (userId)=>{\n        // console.log(\"fetchExistingMessages\");\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"\".concat(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_MESSAGES, \"?userId=\").concat(userId));\n            if (response.data && response.data.length > 0) {\n                setMessages(response.data.map((msg)=>({\n                        text: msg.message,\n                        timestamp: new Date(msg.createdAt).getTime(),\n                        type: msg.type,\n                        source: msg.source\n                    })));\n            }\n            connectToSSE(userId);\n        } catch (error) {\n            await initializeChatSession();\n        }\n    };\n    const initializeChatSession = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_INIT, {\n                customerName: username\n            });\n            if (response.data && response.data.userId) {\n                localStorage.setItem(\"userId\", response.data.userId);\n                localStorage.setItem(\"userID\", response.data._id);\n                await fetchExistingMessages(response.data._id);\n            }\n        } catch (error) {\n            if (!localStorage.getItem(\"BusinessName\")) {\n                localStorage.setItem(\"BusinessName\", \"Driply\");\n                window.dispatchEvent(new CustomEvent(\"businessNameLoaded\", {\n                    detail: {\n                        businessName: \"Driply\"\n                    }\n                }));\n            }\n        }\n    };\n    const connectToSSE = (userId)=>{\n        try {\n            if (sseConnection) {\n                sseConnection.close();\n            }\n            const sseUrl = (0,_utils_config__WEBPACK_IMPORTED_MODULE_4__.getSSEUrl)(userId);\n            const eventSource = new EventSource(sseUrl);\n            setSseConnection(eventSource);\n            eventSource.addEventListener(\"message\", (param)=>{\n                let { data } = param;\n                try {\n                    const contents = JSON.parse(data);\n                    handleSSEMessage(contents);\n                } catch (error) {\n                // Handle parsing error silently\n                }\n            });\n            eventSource.addEventListener(\"error\", ()=>{\n            // Handle connection error silently\n            });\n        } catch (error) {\n        // Handle SSE connection error silently\n        }\n    };\n    const handleSSEMessage = (data)=>{\n        const subtype = data.subType || data.subtype || data.type || \"UNKNOWN\";\n        const content = data.content || \"\";\n        const message = data.message || data.text || \"\";\n        if ([\n            \"TYPING\",\n            \"THINKING\",\n            \"BEHAVIOUR_MESSAGE\"\n        ].includes(subtype)) {\n            switch(subtype){\n                case \"TYPING\":\n                    setIsBotTyping(true);\n                    setIsBotThinking(false);\n                    break;\n                case \"THINKING\":\n                    setIsBotThinking(true);\n                    setIsBotTyping(false);\n                    break;\n                case \"BEHAVIOUR_MESSAGE\":\n                    setIsBotTyping(false);\n                    setIsBotThinking(false);\n                    break;\n            }\n            return;\n        }\n        const displayText = message || content;\n        if (!displayText) return;\n        const displayType = [\n            \"TEXT\",\n            \"MESSAGE\",\n            \"DATA_MESSAGE\"\n        ].includes(subtype) ? subtype : \"UNKNOWN\";\n        // Enhanced message pairing logic\n        const responseToId = data.queryId || data.responseToId || data.correlationId;\n        let queryId = responseToId;\n        // If no explicit queryId, find the most recent pending query\n        if (!queryId) {\n            const pendingEntries = Array.from(pendingQueries.entries()).sort((a, b)=>b[1].timestamp - a[1].timestamp);\n            if (pendingEntries.length > 0) {\n                queryId = pendingEntries[0][0];\n            }\n        }\n        // Create bot message with proper pairing information\n        const botMessage = {\n            id: generateMessageId(),\n            text: displayText,\n            timestamp: Date.now(),\n            type: displayType,\n            source: \"BOT\",\n            queryId: queryId,\n            responseToId: queryId // Explicit response relationship\n        };\n        setMessages((prev)=>[\n                ...prev,\n                botMessage\n            ]);\n        // Remove from pending queries if we found a match\n        if (queryId && pendingQueries.has(queryId)) {\n            setPendingQueries((prev)=>{\n                const updated = new Map(prev);\n                updated.delete(queryId);\n                return updated;\n            });\n        }\n        setIsBotTyping(false);\n        setIsBotThinking(false);\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (message.trim()) {\n            const userMessage = message.trim();\n            const isFirstMsg = messages.length === 0;\n            // Generate unique ID for this query\n            const queryId = generateMessageId();\n            const userMessageObj = {\n                id: queryId,\n                queryId: queryId,\n                text: userMessage,\n                timestamp: Date.now(),\n                type: \"TEXT\",\n                source: \"USER\"\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    userMessageObj\n                ]);\n            // Track this query as pending response\n            setPendingQueries((prev)=>new Map(prev).set(queryId, {\n                    id: queryId,\n                    message: userMessage,\n                    timestamp: userMessageObj.timestamp,\n                    sentAt: Date.now()\n                }));\n            setMessage(\"\");\n            setIsTyping(false);\n            // For first message, set flag to scroll to top\n            if (isFirstMsg) {\n                setShouldScrollToTop(true);\n                setIsFirstMessage(false);\n            }\n            // Show loading dots immediately after sending message\n            setIsBotThinking(true);\n            setIsBotTyping(false);\n            // Reset textarea\n            if (inputRef.current) {\n                inputRef.current.style.height = \"104px\";\n                inputRef.current.scrollTop = 0;\n                inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n                inputRef.current.classList.add(\"reset-height\");\n            }\n            try {\n                const userId = localStorage.getItem(\"userID\");\n                if (userId) {\n                    await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_SEND, {\n                        userId: userId,\n                        message: userMessage,\n                        queryId: queryId,\n                        typs: \"TEXT\",\n                        source: \"USER\",\n                        isTest: query.isTest === \"1\" ? true : false\n                    });\n                }\n            } catch (error) {\n                // Hide loading dots on error\n                setIsBotThinking(false);\n                setIsBotTyping(false);\n                const errorMessage = {\n                    id: generateMessageId(),\n                    text: \"Sorry, there was an error sending your message. Please try again.\",\n                    timestamp: Date.now(),\n                    type: \"TEXT\",\n                    source: \"BOT\",\n                    queryId: queryId,\n                    isError: true\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        errorMessage\n                    ]);\n                // Remove from pending queries since we got an error\n                setPendingQueries((prev)=>{\n                    const updated = new Map(prev);\n                    updated.delete(queryId);\n                    return updated;\n                });\n            }\n        }\n    };\n    const handleTextareaResize = (textarea)=>{\n        if (textarea) {\n            textarea.style.height = \"104px\";\n            const scrollHeight = textarea.scrollHeight;\n            if (isIOS()) {\n                if (scrollHeight > 48) {\n                    textarea.style.overflowY = \"auto\";\n                } else {\n                    textarea.style.overflowY = \"hidden\";\n                }\n            } else {\n                const newHeight = Math.min(250, Math.max(104, scrollHeight));\n                textarea.style.height = \"\".concat(newHeight, \"px\");\n                textarea.style.overflowY = scrollHeight > newHeight ? \"auto\" : \"hidden\";\n            }\n        }\n    };\n    const handleInputChange = (e)=>{\n        const value = e.target.value;\n        setMessage(value);\n        handleTextareaResize(e.target);\n        if (messages.length === 0) {\n            setIsTyping(value.length > 0);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length > 0 && inputRef.current && !message) {\n                const timer = setTimeout({\n                    \"ChatInterface.useEffect.timer\": ()=>{\n                        if (inputRef.current) {\n                            inputRef.current.style.height = \"104px\";\n                            inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n                            inputRef.current.classList.add(\"reset-height\");\n                        }\n                    }\n                }[\"ChatInterface.useEffect.timer\"], 50);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                })[\"ChatInterface.useEffect\"];\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages,\n        message\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length > 0) {\n                handleChatGPTScroll();\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages,\n        isBotTyping,\n        isBotThinking\n    ]);\n    const handleChatGPTScroll = ()=>{\n        setTimeout(()=>{\n            // Determine screen type and get appropriate container\n            const screenWidth = window.innerWidth;\n            const isDesktopLayout = screenWidth >= 1024;\n            const isTabletLayout = screenWidth >= 768 && screenWidth < 1024;\n            let container;\n            if (isDesktopLayout) {\n                container = desktopMessagesContainerRef.current;\n            } else if (isTabletLayout) {\n                container = tabletMessagesContainerRef.current;\n            } else {\n                container = mobileMessagesContainerRef.current;\n            }\n            if (!container) return;\n            if (shouldScrollToTop) {\n                // For first message, scroll to top to show the message there\n                container.scrollTo({\n                    top: 0,\n                    behavior: \"smooth\"\n                });\n                setShouldScrollToTop(false);\n            } else {\n                // Different scrolling behavior for each screen type\n                const messageElements = container.querySelectorAll(\".message-pair\");\n                if (messageElements.length > 0) {\n                    const lastMessagePair = messageElements[messageElements.length - 1];\n                    const containerHeight = container.clientHeight;\n                    const pairHeight = lastMessagePair.offsetHeight;\n                    const pairTop = lastMessagePair.offsetTop;\n                    if (isTabletLayout) {\n                        // Tablet-specific scrolling: Keep messages visible by scrolling to bottom of container\n                        // This ensures both user message and AI response remain visible\n                        const scrollPosition = Math.max(0, pairTop + pairHeight - containerHeight + 100);\n                        container.scrollTo({\n                            top: scrollPosition,\n                            behavior: \"smooth\"\n                        });\n                    } else {\n                        // Desktop and mobile: Center the message pair on screen\n                        const scrollPosition = pairTop - (containerHeight - pairHeight) / 2;\n                        container.scrollTo({\n                            top: Math.max(0, scrollPosition),\n                            behavior: \"smooth\"\n                        });\n                    }\n                }\n            }\n        }, 150);\n    };\n    const renderMessagePairs = ()=>{\n        // Use the enhanced message pairing logic\n        const messagePairs = createMessagePairs(messages);\n        return messagePairs.map((pair, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"message-pair flex flex-col justify-start\",\n                \"data-pair-id\": pair.id,\n                \"data-is-complete\": pair.isComplete,\n                \"data-is-pending\": pair.isPending,\n                style: {\n                    minHeight: i === messagePairs.length - 1 ? isMobile ? \"calc(100vh - 200px)\" // Mobile-specific height for newest message\n                     : \"calc(100vh - 200px)\" // Desktop/tablet height for newest message\n                     : \"\",\n                    paddingTop: i === 0 ? \"1rem\" : \"1rem\",\n                    paddingBottom: \"0rem\"\n                },\n                children: [\n                    pair.user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-end mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-[16px] font-[400] rounded-3xl max-w-xs lg:max-w-lg break-words hyphens-auto bg-gray-100 \".concat(isMobile ? \"rounded-[15px] px-3 py-2\" : \"px-4 py-3\"),\n                            \"data-message-id\": pair.user.id,\n                            \"data-query-id\": pair.user.queryId,\n                            style: {\n                                overflowWrap: \"break-word\",\n                                wordBreak: \"break-word\",\n                                maxWidth: isMobile ? \"90%\" : undefined\n                            },\n                            children: [\n                                pair.user.text,\n                                pair.user.isSuggestion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-500 ml-2\",\n                                    children: \"\\uD83D\\uDCA1\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 634,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 620,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 619,\n                        columnNumber: 11\n                    }, undefined),\n                    pair.bot && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-start\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-[16px] font-[400] rounded-3xl max-w-xs lg:max-w-lg break-words hyphens-auto bg-white \".concat(isMobile ? \"rounded-[15px] px-3 py-2\" : \"px-4 py-3\", \" \").concat(pair.bot.isError ? \"border-red-200 bg-red-50\" : \"\"),\n                            \"data-message-id\": pair.bot.id,\n                            \"data-query-id\": pair.bot.queryId,\n                            \"data-response-to\": pair.bot.responseToId,\n                            style: {\n                                overflowWrap: \"break-word\",\n                                wordBreak: \"break-word\",\n                                maxWidth: isMobile ? \"90%\" : undefined\n                            },\n                            children: [\n                                pair.bot.text,\n                                pair.bot.isError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-red-500 ml-2\",\n                                    children: \"⚠️\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 658,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 643,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 642,\n                        columnNumber: 11\n                    }, undefined),\n                    i === messagePairs.length - 1 && (isBotTyping || isBotThinking) && !pair.bot && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-start\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white \".concat(isMobile ? \"px-3 py-2 rounded-[15px]\" : \"px-4 py-3 rounded-3xl\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-black rounded-full loading-dot\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 677,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-black rounded-full loading-dot\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 678,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-black rounded-full loading-dot\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 679,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 676,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 669,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 668,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, \"pair-\".concat(pair.id), true, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 600,\n                columnNumber: 7\n            }, undefined));\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSubmit(e);\n        }\n    };\n    const handleSuggestionClick = async (cardTitle)=>{\n        const isFirstMsg = messages.length === 0;\n        // Generate unique ID for this suggestion query\n        const queryId = generateMessageId();\n        const userMessageObj = {\n            id: queryId,\n            queryId: queryId,\n            text: cardTitle,\n            timestamp: Date.now(),\n            type: \"TEXT\",\n            source: \"USER\",\n            isSuggestion: true\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessageObj\n            ]);\n        // Track this suggestion query as pending response\n        setPendingQueries((prev)=>new Map(prev).set(queryId, {\n                id: queryId,\n                message: cardTitle,\n                timestamp: userMessageObj.timestamp,\n                sentAt: Date.now(),\n                isSuggestion: true\n            }));\n        setMessage(\"\");\n        setIsTyping(false);\n        // For first message, set flag to scroll to top\n        if (isFirstMsg) {\n            setShouldScrollToTop(true);\n            setIsFirstMessage(false);\n        }\n        // Show loading dots immediately after sending message\n        setIsBotThinking(true);\n        setIsBotTyping(false);\n        if (inputRef.current) {\n            inputRef.current.style.height = \"104px\";\n            inputRef.current.scrollTop = 0;\n            inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n            inputRef.current.classList.add(\"reset-height\");\n        }\n        try {\n            const userId = localStorage.getItem(\"userID\");\n            if (userId) {\n                await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_SEND, {\n                    userId: userId,\n                    message: cardTitle,\n                    queryId: queryId,\n                    customerName: username,\n                    isSuggestion: true\n                });\n            }\n        } catch (error) {\n            // Hide loading dots on error\n            setIsBotThinking(false);\n            setIsBotTyping(false);\n            const errorMessage = {\n                id: generateMessageId(),\n                text: \"Sorry, there was an error sending your message. Please try again.\",\n                timestamp: Date.now(),\n                type: \"TEXT\",\n                source: \"BOT\",\n                queryId: queryId,\n                isError: true\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n            // Remove from pending queries since we got an error\n            setPendingQueries((prev)=>{\n                const updated = new Map(prev);\n                updated.delete(queryId);\n                return updated;\n            });\n        }\n    };\n    const getMaxSlides = ()=>{\n        if (isMobile) return settingData === null || settingData === void 0 ? void 0 : settingData.suggestedTopics.length;\n        return Math.ceil((settingData === null || settingData === void 0 ? void 0 : settingData.suggestedTopics.length) / 2);\n    };\n    const nextSlide = ()=>{\n        var _settingData_suggestedTopics;\n        const totalCards = (settingData === null || settingData === void 0 ? void 0 : (_settingData_suggestedTopics = settingData.suggestedTopics) === null || _settingData_suggestedTopics === void 0 ? void 0 : _settingData_suggestedTopics.length) || 0;\n        if (totalCards === 0) return;\n        // Calculate scroll distance to ensure last card is fully visible\n        // Reduce scroll distance to show partial cards and ensure last card visibility\n        const scrollStep = isMobile ? 0.7 : 0.8; // Scroll by 70% on mobile, 80% on desktop\n        const maxSlide = Math.max(0, totalCards - 1); // Allow scrolling to show last card fully\n        setCurrentSlide((prev)=>Math.min(prev + scrollStep, maxSlide));\n    };\n    const prevSlide = ()=>{\n        const scrollStep = isMobile ? 0.7 : 0.8; // Same scroll step for consistency\n        setCurrentSlide((prev)=>Math.max(prev - scrollStep, 0));\n    };\n    // Check if we're at the boundaries for button states\n    const isAtStart = currentSlide === 0;\n    const isAtEnd = ()=>{\n        var _settingData_suggestedTopics;\n        const totalCards = (settingData === null || settingData === void 0 ? void 0 : (_settingData_suggestedTopics = settingData.suggestedTopics) === null || _settingData_suggestedTopics === void 0 ? void 0 : _settingData_suggestedTopics.length) || 0;\n        const maxSlide = Math.max(0, totalCards - 1);\n        return currentSlide >= maxSlide;\n    };\n    const handleNextSlide = ()=>{\n        nextSlide();\n    };\n    const handlePrevSlide = ()=>{\n        prevSlide();\n    };\n    const handleTouchStart = (e)=>{\n        setTouchStart(e.targetTouches[0].clientX);\n    };\n    const handleTouchMove = (e)=>{\n        setTouchEnd(e.targetTouches[0].clientX);\n    };\n    const handleTouchEnd = ()=>{\n        if (!touchStart || !touchEnd) return;\n        const swipeDistance = touchStart - touchEnd;\n        const minSwipeDistance = 50;\n        if (swipeDistance > minSwipeDistance) {\n            nextSlide();\n        }\n        if (swipeDistance < -minSwipeDistance) {\n            prevSlide();\n        }\n        setTouchStart(0);\n        setTouchEnd(0);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length === 0) {\n                setShowInitialUI(false);\n                const timer = setTimeout({\n                    \"ChatInterface.useEffect.timer\": ()=>setShowInitialUI(true)\n                }[\"ChatInterface.useEffect.timer\"], 60);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                })[\"ChatInterface.useEffect\"];\n            } else {\n                setShowInitialUI(false);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages.length\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const checkScreenSize = {\n                \"ChatInterface.useEffect.checkScreenSize\": ()=>{\n                    if (true) {\n                        const width = window.innerWidth;\n                        setIsMobile(width < 768);\n                        setIsTablet(width >= 768 && width < 1024);\n                    }\n                }\n            }[\"ChatInterface.useEffect.checkScreenSize\"];\n            checkScreenSize();\n            const handleResize = {\n                \"ChatInterface.useEffect.handleResize\": ()=>{\n                    setCurrentSlide(0);\n                    checkScreenSize();\n                }\n            }[\"ChatInterface.useEffect.handleResize\"];\n            if (true) {\n                window.addEventListener(\"resize\", handleResize);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>window.removeEventListener(\"resize\", handleResize)\n                })[\"ChatInterface.useEffect\"];\n            }\n        }\n    }[\"ChatInterface.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (inputRef.current && messages.length === 0) {\n                const shouldAutoFocus = window.innerWidth >= 768;\n                if (shouldAutoFocus) {\n                    const timer = setTimeout({\n                        \"ChatInterface.useEffect.timer\": ()=>{\n                            var _inputRef_current;\n                            (_inputRef_current = inputRef.current) === null || _inputRef_current === void 0 ? void 0 : _inputRef_current.focus();\n                        }\n                    }[\"ChatInterface.useEffect.timer\"], 100);\n                    return ({\n                        \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                    })[\"ChatInterface.useEffect\"];\n                }\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages.length\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            return ({\n                \"ChatInterface.useEffect\": ()=>{\n                    if (sseConnection) {\n                        sseConnection.close();\n                    }\n                }\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], [\n        sseConnection\n    ]);\n    // Check if settingData has been populated with actual data\n    const hasSettingData = settingData && Object.keys(settingData).some((key)=>settingData[key] !== null);\n    // Show loading state while waiting for data\n    if (!hasSettingData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white flex flex-col min-h-screen items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 912,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading chat settings...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 913,\n                        columnNumber: 11\n                    }, undefined),\n                    contextError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-500 mt-2\",\n                        children: [\n                            \"Error: \",\n                            contextError\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 915,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 911,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n            lineNumber: 910,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"bg-white flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"hidden lg:flex flex-1 flex-col px-4 \",\n                children: messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex flex-1 items-center justify-center min-h-[calc(100vh-64px)] mt-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex flex-col items-center w-[768px] justify-center mx-auto \",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    transitionDelay: showInitialUI ? \"40ms\" : \"0ms\"\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-4xl text-gray-900 mb-6 text-center transition-all duration-700 \".concat(showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                children: \"How can I help you?\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 929,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                style: {\n                                    transitionDelay: showInitialUI ? \"120ms\" : \"0ms\"\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"relative w-full mb-6 flex flex-col items-center transition-all duration-500 \".concat(showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        ref: messages.length === 0 ? inputRef : null,\n                                        value: message,\n                                        onChange: handleInputChange,\n                                        onKeyDown: handleKeyPress,\n                                        placeholder: \"Ask anything\",\n                                        autoFocus: messages.length === 0,\n                                        rows: 1,\n                                        style: {\n                                            boxSizing: \"border-box\",\n                                            zIndex: 1001,\n                                            position: \"relative\"\n                                        },\n                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"pt-3 px-4 lg:p-4 pb-16 pr-12 w-full min-h-[104px] max-h-[250px] overflow-y-auto hide-scrollbar text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base shadow-md resize-none reset-height\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 949,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: message.trim().length === 0,\n                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"absolute right-3 bottom-3 w-9 h-9 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation \".concat(message.trim().length === 0 ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                            className: \"w-4 h-4 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 973,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 964,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 940,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    transitionDelay: showInitialUI ? \"200ms\" : \"0ms\",\n                                    zIndex: 10\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"relative w-full max-w-2xl transition-all duration-500 ease-in-out \".concat(isTyping ? \"opacity-0 pointer-events-none\" : showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"overflow-hidden px-12\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: carouselRef,\n                                            style: {\n                                                transform: \"translateX(-\".concat(currentSlide * (isMobile ? 200 : 250), \"px)\"),\n                                                paddingLeft: \"0\"\n                                            },\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex gap-2 transition-transform duration-300 ease-in-out justify-start\",\n                                            children: settingData === null || settingData === void 0 ? void 0 : (_settingData_suggestedTopics1 = settingData.suggestedTopics) === null || _settingData_suggestedTopics1 === void 0 ? void 0 : _settingData_suggestedTopics1.map((card, cardIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleSuggestionClick(card === null || card === void 0 ? void 0 : card.question),\n                                                    style: {\n                                                        minWidth: \"fit-content\",\n                                                        maxWidth: \"300px\",\n                                                        width: \"auto\"\n                                                    },\n                                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"py-2 px-3 rounded-[8px] bg-[#f6f6f6] text-left group transition-all duration-200 touch-manipulation flex-shrink-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-[16px] font-[600] text-black mb-0.5 leading-tight\",\n                                                            children: card.question\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 1011,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-[16px] text-gray-500 leading-tight\",\n                                                            children: card.subtitle\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 1014,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, cardIndex, true, {\n                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                    lineNumber: 999,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 990,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 989,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handlePrevSlide,\n                                        disabled: isAtStart,\n                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-10 w-8 h-8 border rounded-full flex items-center justify-center shadow-md transition-all duration-200 touch-manipulation z-10 \".concat(isAtStart ? 'bg-gray-100 border-gray-300 cursor-not-allowed' : 'bg-white border-gray-200 hover:bg-gray-50 cursor-pointer'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronLeft, {\n                                            className: \"w-3 h-3 \".concat(isAtStart ? 'text-gray-400' : 'text-gray-600')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1030,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 1021,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleNextSlide,\n                                        disabled: isAtEnd(),\n                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-10 w-8 h-8 border rounded-full flex items-center justify-center shadow-md transition-all duration-200 touch-manipulation z-10 \".concat(isAtEnd() ? 'bg-gray-100 border-gray-300 cursor-not-allowed' : 'bg-white border-gray-200 hover:bg-gray-50 cursor-pointer'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronRight, {\n                                            className: \"w-3 h-3 \".concat(isAtEnd() ? 'text-gray-400' : 'text-gray-600')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1041,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 1032,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 976,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 928,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                    lineNumber: 927,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: desktopMessagesContainerRef,\n                            style: {\n                                overscrollBehavior: \"contain\"\n                            },\n                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex-1 overflow-y-auto max-h-[calc(100vh-200px)] mt-14\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"w-full lg:w-[768px] mx-auto px-4 h-fit \",\n                                children: [\n                                    renderMessagePairs(),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: messagesEndRef,\n                                        className: \"jsx-69ac67aa0147cd65\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 1055,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 1053,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 1048,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                zIndex: 1000,\n                                marginTop: \"20px\"\n                            },\n                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"fixed bottom-0 left-0 right-0 p-4 mt-5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSubmit,\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"relative w-full lg:w-[768px] mx-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            ref: messages.length > 0 ? inputRef : null,\n                                            value: message,\n                                            onChange: handleInputChange,\n                                            onKeyDown: handleKeyPress,\n                                            placeholder: \"Ask anything\",\n                                            rows: 1,\n                                            style: {\n                                                boxSizing: \"border-box\",\n                                                zIndex: 1001,\n                                                position: \"relative\"\n                                            },\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"pt-3 px-4 md:px-5 pb-10 pr-12 w-full min-h-[104px] max-h-[250px] overflow-y-auto text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base shadow-lg resize-none reset-height hide-scrollbar\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1067,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: message.trim().length === 0,\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation \".concat(message.trim().length === 0 ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                                className: \"w-4 h-4 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1090,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1081,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1063,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-center mt-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-xs text-gray-500\",\n                                        children: [\n                                            \"This chat is powered by\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-black\",\n                                                children: \"Driply.me\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1096,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 1094,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1093,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 1059,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 925,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"lg:hidden overflow-hidden fixed inset-0 flex flex-col  mt-10 \",\n                children: [\n                    messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex flex-col items-center pt-15 pb-4 px-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"hidden md:flex lg:hidden flex-col items-center\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1109,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 1108,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    paddingBottom: \"260px\"\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex flex-col items-center justify-center flex-1 px-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"40ms\" : \"0ms\"\n                                    },\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-2xl md:text-4xl text-gray-900 text-center leading-relaxed transition-all duration-700 \".concat(showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                    children: \"How can I help you?\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1115,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 1111,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: mobileMessagesContainerRef,\n                                style: {\n                                    overscrollBehavior: \"contain\"\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"md:hidden flex-1 overflow-y-auto max-h-[calc(100vh-190px)] hide-scrollbar\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"w-full max-w-[803px] mx-auto px-4  pt-12\",\n                                    children: [\n                                        renderMessagePairs(),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: messagesEndRef,\n                                            className: \"jsx-69ac67aa0147cd65\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1137,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1135,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 1130,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: tabletMessagesContainerRef,\n                                style: {\n                                    overscrollBehavior: \"contain\"\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"hidden md:block lg:hidden flex-1 overflow-y-auto max-h-[calc(100vh-100px)]  pb-20 hide-scrollbar\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"w-full max-w-[803px] mx-auto px-4 pb-6 pt-12\",\n                                    children: [\n                                        renderMessagePairs(),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: messagesEndRef,\n                                            className: \"jsx-69ac67aa0147cd65\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1149,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1147,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 1142,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    minHeight: \"160px\",\n                                    zIndex: 1000,\n                                    paddingBottom: \"env(safe-area-inset-bottom, 0)\",\n                                    transform: \"translateZ(0)\",\n                                    backfaceVisibility: \"hidden\"\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"fixed bottom-0 left-0 right-0 bg-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        transform: \"translateZ(0)\"\n                                    },\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"px-4 py-4 bg-white\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            style: {\n                                                zIndex: 1001\n                                            },\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"relative bg-white\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    transform: \"translateZ(0)\"\n                                                },\n                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"relative w-full max-w-[890px] mx-auto bg-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        ref: messages.length > 0 ? inputRef : null,\n                                                        value: message,\n                                                        onChange: handleInputChange,\n                                                        onKeyDown: handleKeyPress,\n                                                        placeholder: \"Ask anything\",\n                                                        rows: 1,\n                                                        style: {\n                                                            boxSizing: \"border-box\",\n                                                            maxHeight: \"250px\",\n                                                            zIndex: 1001,\n                                                            overscrollBehavior: \"none\",\n                                                            position: \"relative\"\n                                                        },\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"w-full min-h-[104px] pt-3 px-4 pb-10 pr-12 text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base resize-none overflow-y-auto reset-height\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1176,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        disabled: message.trim().length === 0,\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation \".concat(message.trim().length === 0 ? \"bg-[#E0E0E0] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                                            className: \"w-4 h-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 1201,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1192,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1172,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1167,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-center py-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"This chat is powered by\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-black\",\n                                                        children: \"Driply.me\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1208,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1206,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1205,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1163,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 1153,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"absolute bottom-0 left-0 right-0 bg-white\",\n                        children: messages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"120ms\" : \"0ms\",\n                                        position: \"relative\",\n                                        zIndex: 10,\n                                        backgroundColor: \"white\",\n                                        paddingBottom: \"10px\"\n                                    },\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"\".concat(isMobile ? \"px-0\" : \"px-4\", \" pt-2 pb-2 transition-all duration-500 ease-in-out \").concat(isTyping ? \"opacity-0 pointer-events-none\" : showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"overflow-hidden \".concat(isMobile ? 'px-4' : 'px-12'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                ref: carouselRef,\n                                                style: {\n                                                    transform: \"translateX(-\".concat(currentSlide * (isMobile ? 180 : 280), \"px)\"),\n                                                    paddingLeft: \"0\"\n                                                },\n                                                onTouchStart: handleTouchStart,\n                                                onTouchMove: handleTouchMove,\n                                                onTouchEnd: handleTouchEnd,\n                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex gap-2 transition-transform duration-300 ease-in-out justify-start\",\n                                                children: settingData === null || settingData === void 0 ? void 0 : (_settingData_suggestedTopics2 = settingData.suggestedTopics) === null || _settingData_suggestedTopics2 === void 0 ? void 0 : _settingData_suggestedTopics2.map((card, cardIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex-shrink-0 \".concat(isMobile ? 'mt-3 mr-2' : 'mr-2'),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleSuggestionClick(card.question),\n                                                            style: {\n                                                                minWidth: \"fit-content\",\n                                                                maxWidth: isMobile ? \"280px\" : \"300px\",\n                                                                width: \"auto\"\n                                                            },\n                                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"\".concat(isMobile ? 'py-3' : 'py-2', \" px-2.5 rounded-[8px] bg-[#F6F6F6] text-left hover:bg-[#D6EDFF] transition-all duration-200 inline-block touch-manipulation\"),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"\".concat(isMobile ? 'text-[16px]' : 'text-[14px]', \" font-[600] text-black mb-0.5 leading-tight\"),\n                                                                    children: card.question\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                    lineNumber: 1267,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-[16px] text-gray-500 leading-tight\",\n                                                                    children: card.subtitle\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                    lineNumber: 1270,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 1256,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, cardIndex, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1252,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1240,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1239,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handlePrevSlide,\n                                            disabled: isAtStart,\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"hidden md:flex absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-10 w-8 h-8 border rounded-full items-center justify-center shadow-md transition-all duration-200 touch-manipulation z-10 \".concat(isAtStart ? 'bg-gray-100 border-gray-300 cursor-not-allowed' : 'bg-white border-gray-200 hover:bg-gray-50 cursor-pointer'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronLeft, {\n                                                className: \"w-3 h-3 \".concat(isAtStart ? 'text-gray-400' : 'text-gray-600')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1289,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1280,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleNextSlide,\n                                            disabled: isAtEnd(),\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"hidden md:flex absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-10 w-8 h-8 border rounded-full items-center justify-center shadow-md transition-all duration-200 touch-manipulation z-10 \".concat(isAtEnd() ? 'bg-gray-100 border-gray-300 cursor-not-allowed' : 'bg-white border-gray-200 hover:bg-gray-50 cursor-pointer'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronRight, {\n                                                className: \"w-3 h-3 \".concat(isAtEnd() ? 'text-gray-400' : 'text-gray-600')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1300,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1291,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1221,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"200ms\" : \"0ms\",\n                                        position: \"relative\",\n                                        zIndex: 5,\n                                        paddingBottom: \"env(safe-area-inset-bottom)\"\n                                    },\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"px-4 bg-white transition-all duration-500 \".concat(showInitialUI ? \"opacity-100 translate-y-0\" : \"translate-y-4\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"relative\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"relative w-full max-w-[890px] mx-auto\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        ref: messages.length === 0 ? inputRef : null,\n                                                        value: message,\n                                                        onChange: handleInputChange,\n                                                        onKeyDown: handleKeyPress,\n                                                        placeholder: \"Ask anything\",\n                                                        rows: 1,\n                                                        style: {\n                                                            boxSizing: \"border-box\",\n                                                            maxHeight: \"250px\",\n                                                            fontSize: \"16px\",\n                                                            zIndex: 1001,\n                                                            overscrollBehaviorY: \"contain\",\n                                                            position: \"relative\"\n                                                        },\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"w-full min-h-[104px] pt-3 px-4 pb-10 pr-12 text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base resize-none overflow-y-auto reset-height\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1318,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        disabled: message.trim().length === 0,\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation \".concat(message.trim().length === 0 ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                                            className: \"w-4 h-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 1344,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1335,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1317,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1316,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-center py-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"This chat is powered by\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-black\",\n                                                        children: \"Driply.me\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1351,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1349,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1348,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1305,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 1217,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 1105,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"69ac67aa0147cd65\",\n                children: '.loading-dot{-webkit-animation:loading-dots 1.4s infinite ease-in-out both;-moz-animation:loading-dots 1.4s infinite ease-in-out both;-o-animation:loading-dots 1.4s infinite ease-in-out both;animation:loading-dots 1.4s infinite ease-in-out both}.loading-dot:nth-child(1){-webkit-animation-delay:-.32s;-moz-animation-delay:-.32s;-o-animation-delay:-.32s;animation-delay:-.32s}.loading-dot:nth-child(2){-webkit-animation-delay:-.16s;-moz-animation-delay:-.16s;-o-animation-delay:-.16s;animation-delay:-.16s}.loading-dot:nth-child(3){-webkit-animation-delay:0s;-moz-animation-delay:0s;-o-animation-delay:0s;animation-delay:0s}@-webkit-keyframes loading-dots{0%,80%,100%{-webkit-transform:scale(1);transform:scale(1);opacity:1}40%{-webkit-transform:scale(1.5);transform:scale(1.5);opacity:.7}}@-moz-keyframes loading-dots{0%,80%,100%{-moz-transform:scale(1);transform:scale(1);opacity:1}40%{-moz-transform:scale(1.5);transform:scale(1.5);opacity:.7}}@-o-keyframes loading-dots{0%,80%,100%{-o-transform:scale(1);transform:scale(1);opacity:1}40%{-o-transform:scale(1.5);transform:scale(1.5);opacity:.7}}@keyframes loading-dots{0%,80%,100%{-webkit-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1);opacity:1}40%{-webkit-transform:scale(1.5);-moz-transform:scale(1.5);-o-transform:scale(1.5);transform:scale(1.5);opacity:.7}}.hide-scrollbar{-ms-overflow-style:none!important;scrollbar-width:none!important}.hide-scrollbar::-webkit-scrollbar{display:none!important}@media only screen and (max-width:1023px){.lg\\\\\\\\.jsx-69ac67aa0147cd65:hidden .absolute.bottom-0.jsx-69ac67aa0147cd65{position:fixed!important;bottom:env(safe-area-inset-bottom,0);left:0;right:0;width:100%;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;z-index:1000;background-color:white}textarea.jsx-69ac67aa0147cd65{-webkit-user-select:auto!important;-moz-user-select:auto!important;-ms-user-select:auto!important;user-select:auto!important;-webkit-appearance:none;-moz-appearance:none;-ms-appearance:none;appearance:none;-ms-scroll-chaining:none;overscroll-behavior:none;line-height:24px;max-height:250px!important;position:relative!important;-webkit-transform:translatez(0);-moz-transform:translatez(0);-ms-transform:translatez(0);-o-transform:translatez(0);transform:translatez(0);-webkit-backface-visibility:hidden;-moz-backface-visibility:hidden;backface-visibility:hidden;-webkit-perspective:1000;-moz-perspective:1000;perspective:1000;z-index:1001!important;background-color:white}.lg\\\\\\\\.jsx-69ac67aa0147cd65:hidden form.jsx-69ac67aa0147cd65{position:relative!important;z-index:1001;background-color:white}button[type=\"submit\"].jsx-69ac67aa0147cd65{position:absolute!important;right:12px;bottom:12px;z-index:1002!important;-webkit-tap-highlight-color:transparent;-ms-touch-action:-ms-manipulation;-ms-touch-action:manipulation;touch-action:manipulation;-webkit-transform:translatez(0);-moz-transform:translatez(0);-ms-transform:translatez(0);-o-transform:translatez(0);transform:translatez(0)}.lg\\\\\\\\.jsx-69ac67aa0147cd65:hidden .bg-white.jsx-69ac67aa0147cd65{background-color:white!important}}@media only screen and (min-width:1024px){textarea.jsx-69ac67aa0147cd65{max-height:250px!important;overflow-y:auto!important;-ms-scroll-chaining:none;overscroll-behavior:contain}.reset-height.jsx-69ac67aa0147cd65{min-height:104px!important;max-height:250px!important;overflow-y:auto!important}}.fixed-bottom.jsx-69ac67aa0147cd65{position:fixed!important;bottom:0;left:0;right:0;background:white}'\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n        lineNumber: 923,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatInterface, \"11I1ZIMf13g4EsXvUwg83BGQ4a8=\", false, function() {\n    return [\n        _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__.useChatContext\n    ];\n});\n_c = ChatInterface;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatInterface);\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ChatInterface.jsx\n"));

/***/ })

});