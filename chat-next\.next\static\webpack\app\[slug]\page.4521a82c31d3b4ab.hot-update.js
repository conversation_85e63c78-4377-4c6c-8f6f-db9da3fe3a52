"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[slug]/page",{

/***/ "(app-pages-browser)/./src/components/ChatInterface.jsx":
/*!******************************************!*\
  !*** ./src/components/ChatInterface.jsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/constants */ \"(app-pages-browser)/./src/utils/constants.js\");\n/* harmony import */ var _utils_config__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/config */ \"(app-pages-browser)/./src/utils/config.js\");\n/* harmony import */ var _barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FaArrowUp,FaChevronLeft,FaChevronRight!=!react-icons/fa6 */ \"(app-pages-browser)/./node_modules/react-icons/fa6/index.mjs\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../contexts/ChatContext */ \"(app-pages-browser)/./src/contexts/ChatContext.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst ChatInterface = (param)=>{\n    let { slug, query } = param;\n    var _settingData_suggestedTopics, _settingData_suggestedTopics1;\n    _s();\n    // Context API\n    const { metadata, updateMetadata, setLoadingState, setErrorState, clearError, settingData, updateSettingData, getCustomerName, getBusinessName, hasMetadata, loading: contextLoading, error: contextError } = (0,_contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__.useChatContext)();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [currentSlide, setCurrentSlide] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [touchStart, setTouchStart] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [touchEnd, setTouchEnd] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isTablet, setIsTablet] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showInitialUI, setShowInitialUI] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isBotTyping, setIsBotTyping] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isBotThinking, setIsBotThinking] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [sseConnection, setSseConnection] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isFirstMessage, setIsFirstMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [shouldScrollToTop, setShouldScrollToTop] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Enhanced message pairing state\n    const [pendingQueries, setPendingQueries] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(new Map()); // Track queries waiting for responses\n    const [messageSequence, setMessageSequence] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0); // Global sequence counter\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const carouselRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const desktopMessagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const mobileMessagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const tabletMessagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // Debug: Log state updates when they actually happen\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (metadata && Object.keys(metadata).some({\n                \"ChatInterface.useEffect\": (key)=>metadata[key] !== null\n            }[\"ChatInterface.useEffect\"])) {\n                console.log(\"✅ METADATA STATE UPDATED:\", metadata);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        metadata\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (settingData && Object.keys(settingData).some({\n                \"ChatInterface.useEffect\": (key)=>settingData[key] !== null\n            }[\"ChatInterface.useEffect\"])) {\n                console.log(\"✅ SETTING DATA STATE UPDATED:\", settingData);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        settingData\n    ]);\n    // Cleanup pending queries that are too old (timeout mechanism)\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const cleanup = setInterval({\n                \"ChatInterface.useEffect.cleanup\": ()=>{\n                    const now = Date.now();\n                    const TIMEOUT_MS = 60000; // 60 seconds timeout\n                    setPendingQueries({\n                        \"ChatInterface.useEffect.cleanup\": (prev)=>{\n                            const updated = new Map(prev);\n                            let hasChanges = false;\n                            for (const [queryId, queryData] of prev.entries()){\n                                if (now - queryData.sentAt > TIMEOUT_MS) {\n                                    updated.delete(queryId);\n                                    hasChanges = true;\n                                }\n                            }\n                            return hasChanges ? updated : prev;\n                        }\n                    }[\"ChatInterface.useEffect.cleanup\"]);\n                }\n            }[\"ChatInterface.useEffect.cleanup\"], 10000); // Check every 10 seconds\n            return ({\n                \"ChatInterface.useEffect\": ()=>clearInterval(cleanup)\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], []);\n    // Ensure carousel starts from first card on initial load\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            var _settingData_suggestedTopics;\n            if ((settingData === null || settingData === void 0 ? void 0 : (_settingData_suggestedTopics = settingData.suggestedTopics) === null || _settingData_suggestedTopics === void 0 ? void 0 : _settingData_suggestedTopics.length) > 0) {\n                setCurrentSlide(0); // Always start from first card\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        settingData === null || settingData === void 0 ? void 0 : (_settingData_suggestedTopics = settingData.suggestedTopics) === null || _settingData_suggestedTopics === void 0 ? void 0 : _settingData_suggestedTopics.length\n    ]);\n    // Utility functions for message pairing\n    const generateMessageId = ()=>{\n        const sequence = messageSequence + 1;\n        setMessageSequence(sequence);\n        return \"msg_\".concat(Date.now(), \"_\").concat(sequence, \"_\").concat(Math.random().toString(36).substr(2, 9));\n    };\n    const createMessagePairs = (messages)=>{\n        const pairs = [];\n        const processedMessages = new Set();\n        // Sort messages by timestamp to handle out-of-order arrivals\n        const sortedMessages = [\n            ...messages\n        ].sort((a, b)=>a.timestamp - b.timestamp);\n        // Group messages by queryId for proper pairing\n        const messageGroups = new Map();\n        sortedMessages.forEach((msg)=>{\n            if (msg.source === \"USER\") {\n                // User message starts a new conversation pair\n                const queryId = msg.queryId || msg.id || \"fallback_\".concat(msg.timestamp);\n                if (!messageGroups.has(queryId)) {\n                    messageGroups.set(queryId, {\n                        user: null,\n                        bot: null,\n                        timestamp: msg.timestamp\n                    });\n                }\n                messageGroups.get(queryId).user = msg;\n            } else if (msg.source === \"BOT\") {\n                // Bot message should be paired with corresponding user message\n                const queryId = msg.queryId || msg.responseToId;\n                if (queryId && messageGroups.has(queryId)) {\n                    messageGroups.get(queryId).bot = msg;\n                } else {\n                    // Fallback: pair with most recent unpaired user message\n                    const unpairedGroups = Array.from(messageGroups.entries()).filter((param)=>{\n                        let [_, group] = param;\n                        return group.user && !group.bot;\n                    }).sort((a, b)=>b[1].timestamp - a[1].timestamp);\n                    if (unpairedGroups.length > 0) {\n                        const [groupId, group] = unpairedGroups[0];\n                        group.bot = msg;\n                        // Update the message with proper queryId for future reference\n                        msg.queryId = groupId;\n                    }\n                }\n            }\n        });\n        // Convert groups to pairs array, sorted by timestamp\n        const sortedGroups = Array.from(messageGroups.entries()).sort((a, b)=>a[1].timestamp - b[1].timestamp);\n        return sortedGroups.map((param)=>{\n            let [queryId, group] = param;\n            return {\n                id: queryId,\n                user: group.user,\n                bot: group.bot,\n                timestamp: group.timestamp,\n                isComplete: !!(group.user && group.bot),\n                isPending: !!(group.user && !group.bot)\n            };\n        });\n    };\n    // Detect iOS for targeted fixes\n    const isIOS = ()=>{\n        if (true) {\n            return /iPhone|iPad|iPod/i.test(navigator.userAgent);\n        }\n        return false;\n    };\n    const username = slug;\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (username && \"object\" !== \"undefined\") {\n                fetchCustomer(username);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        username\n    ]);\n    const fetchCustomer = async (customerName)=>{\n        try {\n            setLoadingState(true);\n            clearError();\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"\".concat(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_SETTINGS, \"?customerName=\").concat(customerName));\n            // Extract data from axios response\n            const responseData = response.data;\n            const metadataFromResponse = responseData.metaData;\n            updateSettingData(responseData);\n            updateMetadata(metadataFromResponse);\n            if (responseData && responseData.customerName) {\n                //  localStorage for backward compatibility\n                localStorage.setItem(\"customerName_userId\", responseData.customerName);\n                localStorage.setItem(\"BusinessName\", responseData.businessName);\n                window.dispatchEvent(new CustomEvent(\"businessNameLoaded\", {\n                    detail: {\n                        businessName: responseData.businessName\n                    }\n                }));\n                const existingUserId = localStorage.getItem(\"userID\");\n                if (existingUserId) {\n                    await fetchExistingMessages(existingUserId);\n                } else {\n                    await initializeChatSession();\n                }\n            } else {\n                await initializeChatSession();\n            }\n        } catch (error) {\n            console.error(\"Error fetching customer data:\", error);\n            setErrorState(\"Failed to load customer settings\");\n            // Fallback metadata\n            updateMetadata({\n                customerName: customerName,\n                businessName: \"Driply\"\n            });\n            localStorage.setItem(\"BusinessName\", \"Driply\");\n            window.dispatchEvent(new CustomEvent(\"businessNameLoaded\", {\n                detail: {\n                    businessName: \"Driply\"\n                }\n            }));\n            await initializeChatSession();\n        } finally{\n            setLoadingState(false);\n        }\n    };\n    const fetchExistingMessages = async (userId)=>{\n        // console.log(\"fetchExistingMessages\");\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"\".concat(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_MESSAGES, \"?userId=\").concat(userId));\n            if (response.data && response.data.length > 0) {\n                setMessages(response.data.map((msg)=>({\n                        text: msg.message,\n                        timestamp: new Date(msg.createdAt).getTime(),\n                        type: msg.type,\n                        source: msg.source\n                    })));\n            }\n            connectToSSE(userId);\n        } catch (error) {\n            await initializeChatSession();\n        }\n    };\n    const initializeChatSession = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_INIT, {\n                customerName: username\n            });\n            if (response.data && response.data.userId) {\n                localStorage.setItem(\"userId\", response.data.userId);\n                localStorage.setItem(\"userID\", response.data._id);\n                await fetchExistingMessages(response.data._id);\n            }\n        } catch (error) {\n            if (!localStorage.getItem(\"BusinessName\")) {\n                localStorage.setItem(\"BusinessName\", \"Driply\");\n                window.dispatchEvent(new CustomEvent(\"businessNameLoaded\", {\n                    detail: {\n                        businessName: \"Driply\"\n                    }\n                }));\n            }\n        }\n    };\n    const connectToSSE = (userId)=>{\n        try {\n            if (sseConnection) {\n                sseConnection.close();\n            }\n            const sseUrl = (0,_utils_config__WEBPACK_IMPORTED_MODULE_4__.getSSEUrl)(userId);\n            const eventSource = new EventSource(sseUrl);\n            setSseConnection(eventSource);\n            eventSource.addEventListener(\"message\", (param)=>{\n                let { data } = param;\n                try {\n                    const contents = JSON.parse(data);\n                    handleSSEMessage(contents);\n                } catch (error) {\n                // Handle parsing error silently\n                }\n            });\n            eventSource.addEventListener(\"error\", ()=>{\n            // Handle connection error silently\n            });\n        } catch (error) {\n        // Handle SSE connection error silently\n        }\n    };\n    const handleSSEMessage = (data)=>{\n        const subtype = data.subType || data.subtype || data.type || \"UNKNOWN\";\n        const content = data.content || \"\";\n        const message = data.message || data.text || \"\";\n        if ([\n            \"TYPING\",\n            \"THINKING\",\n            \"BEHAVIOUR_MESSAGE\"\n        ].includes(subtype)) {\n            switch(subtype){\n                case \"TYPING\":\n                    setIsBotTyping(true);\n                    setIsBotThinking(false);\n                    break;\n                case \"THINKING\":\n                    setIsBotThinking(true);\n                    setIsBotTyping(false);\n                    break;\n                case \"BEHAVIOUR_MESSAGE\":\n                    setIsBotTyping(false);\n                    setIsBotThinking(false);\n                    break;\n            }\n            return;\n        }\n        const displayText = message || content;\n        if (!displayText) return;\n        const displayType = [\n            \"TEXT\",\n            \"MESSAGE\",\n            \"DATA_MESSAGE\"\n        ].includes(subtype) ? subtype : \"UNKNOWN\";\n        // Enhanced message pairing logic\n        const responseToId = data.queryId || data.responseToId || data.correlationId;\n        let queryId = responseToId;\n        // If no explicit queryId, find the most recent pending query\n        if (!queryId) {\n            const pendingEntries = Array.from(pendingQueries.entries()).sort((a, b)=>b[1].timestamp - a[1].timestamp);\n            if (pendingEntries.length > 0) {\n                queryId = pendingEntries[0][0];\n            }\n        }\n        // Create bot message with proper pairing information\n        const botMessage = {\n            id: generateMessageId(),\n            text: displayText,\n            timestamp: Date.now(),\n            type: displayType,\n            source: \"BOT\",\n            queryId: queryId,\n            responseToId: queryId\n        };\n        setMessages((prev)=>[\n                ...prev,\n                botMessage\n            ]);\n        // Remove from pending queries if we found a match\n        if (queryId && pendingQueries.has(queryId)) {\n            setPendingQueries((prev)=>{\n                const updated = new Map(prev);\n                updated.delete(queryId);\n                return updated;\n            });\n        }\n        setIsBotTyping(false);\n        setIsBotThinking(false);\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (message.trim()) {\n            const userMessage = message.trim();\n            const isFirstMsg = messages.length === 0;\n            // Generate unique ID for this query\n            const queryId = generateMessageId();\n            const userMessageObj = {\n                id: queryId,\n                queryId: queryId,\n                text: userMessage,\n                timestamp: Date.now(),\n                type: \"TEXT\",\n                source: \"USER\"\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    userMessageObj\n                ]);\n            // Track this query as pending response\n            setPendingQueries((prev)=>new Map(prev).set(queryId, {\n                    id: queryId,\n                    message: userMessage,\n                    timestamp: userMessageObj.timestamp,\n                    sentAt: Date.now()\n                }));\n            setMessage(\"\");\n            setIsTyping(false);\n            // For first message, set flag to scroll to top\n            if (isFirstMsg) {\n                setShouldScrollToTop(true);\n                setIsFirstMessage(false);\n            }\n            // Show loading dots immediately after sending message\n            setIsBotThinking(true);\n            setIsBotTyping(false);\n            // Reset textarea\n            if (inputRef.current) {\n                inputRef.current.style.height = \"104px\";\n                inputRef.current.scrollTop = 0;\n                inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n                inputRef.current.classList.add(\"reset-height\");\n            }\n            try {\n                const userId = localStorage.getItem(\"userID\");\n                if (userId) {\n                    await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_SEND, {\n                        userId: userId,\n                        message: userMessage,\n                        queryId: queryId,\n                        typs: \"TEXT\",\n                        source: \"USER\",\n                        isTest: query.isTest === \"1\" ? true : false\n                    });\n                }\n            } catch (error) {\n                // Hide loading dots on error\n                setIsBotThinking(false);\n                setIsBotTyping(false);\n                const errorMessage = {\n                    id: generateMessageId(),\n                    text: \"Sorry, there was an error sending your message. Please try again.\",\n                    timestamp: Date.now(),\n                    type: \"TEXT\",\n                    source: \"BOT\",\n                    queryId: queryId,\n                    isError: true\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        errorMessage\n                    ]);\n                // Remove from pending queries since we got an error\n                setPendingQueries((prev)=>{\n                    const updated = new Map(prev);\n                    updated.delete(queryId);\n                    return updated;\n                });\n            }\n        }\n    };\n    const handleTextareaResize = (textarea)=>{\n        if (textarea) {\n            textarea.style.height = \"104px\";\n            const scrollHeight = textarea.scrollHeight;\n            if (isIOS()) {\n                if (scrollHeight > 48) {\n                    textarea.style.overflowY = \"auto\";\n                } else {\n                    textarea.style.overflowY = \"hidden\";\n                }\n            } else {\n                const newHeight = Math.min(250, Math.max(104, scrollHeight));\n                textarea.style.height = \"\".concat(newHeight, \"px\");\n                textarea.style.overflowY = scrollHeight > newHeight ? \"auto\" : \"hidden\";\n            }\n        }\n    };\n    const handleInputChange = (e)=>{\n        const value = e.target.value;\n        setMessage(value);\n        handleTextareaResize(e.target);\n        if (messages.length === 0) {\n            setIsTyping(value.length > 0);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length > 0 && inputRef.current && !message) {\n                const timer = setTimeout({\n                    \"ChatInterface.useEffect.timer\": ()=>{\n                        if (inputRef.current) {\n                            inputRef.current.style.height = \"104px\";\n                            inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n                            inputRef.current.classList.add(\"reset-height\");\n                        }\n                    }\n                }[\"ChatInterface.useEffect.timer\"], 50);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                })[\"ChatInterface.useEffect\"];\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages,\n        message\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length > 0) {\n                handleChatGPTScroll();\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages,\n        isBotTyping,\n        isBotThinking\n    ]);\n    const handleChatGPTScroll = ()=>{\n        setTimeout(()=>{\n            // Determine screen type and get appropriate container\n            const screenWidth = window.innerWidth;\n            const isDesktopLayout = screenWidth >= 1024;\n            const isTabletLayout = screenWidth >= 768 && screenWidth < 1024;\n            let container;\n            if (isDesktopLayout) {\n                container = desktopMessagesContainerRef.current;\n            } else if (isTabletLayout) {\n                container = tabletMessagesContainerRef.current;\n            } else {\n                container = mobileMessagesContainerRef.current;\n            }\n            if (!container) return;\n            if (shouldScrollToTop) {\n                // For first message, scroll to top to show the message there\n                container.scrollTo({\n                    top: 0,\n                    behavior: \"smooth\"\n                });\n                setShouldScrollToTop(false);\n            } else {\n                // Different scrolling behavior for each screen type\n                const messageElements = container.querySelectorAll(\".message-pair\");\n                if (messageElements.length > 0) {\n                    const lastMessagePair = messageElements[messageElements.length - 1];\n                    const containerHeight = container.clientHeight;\n                    const pairHeight = lastMessagePair.offsetHeight;\n                    const pairTop = lastMessagePair.offsetTop;\n                    if (isTabletLayout) {\n                        // Tablet-specific scrolling: Keep messages visible by scrolling to bottom of container\n                        // This ensures both user message and AI response remain visible\n                        const scrollPosition = Math.max(0, pairTop + pairHeight - containerHeight + 100);\n                        container.scrollTo({\n                            top: scrollPosition,\n                            behavior: \"smooth\"\n                        });\n                    } else {\n                        // Desktop and mobile: Center the message pair on screen\n                        const scrollPosition = pairTop - (containerHeight - pairHeight) / 2;\n                        container.scrollTo({\n                            top: Math.max(0, scrollPosition),\n                            behavior: \"smooth\"\n                        });\n                    }\n                }\n            }\n        }, 150);\n    };\n    const renderMessagePairs = ()=>{\n        // Use the enhanced message pairing logic\n        const messagePairs = createMessagePairs(messages);\n        return messagePairs.map((pair, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"message-pair flex flex-col justify-start\",\n                \"data-pair-id\": pair.id,\n                \"data-is-complete\": pair.isComplete,\n                \"data-is-pending\": pair.isPending,\n                style: {\n                    minHeight: i === messagePairs.length - 1 ? isMobile ? \"calc(100vh - 200px)\" // Mobile-specific height for newest message\n                     : \"calc(100vh - 200px)\" // Desktop/tablet height for newest message\n                     : \"\",\n                    paddingTop: i === 0 ? \"1rem\" : \"1rem\",\n                    paddingBottom: \"0rem\"\n                },\n                children: [\n                    pair.user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-end mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-[16px] font-[400] rounded-3xl max-w-xs lg:max-w-lg break-words hyphens-auto bg-gray-100 \".concat(isMobile ? \"rounded-[15px] px-3 py-2\" : \"px-4 py-3\"),\n                            \"data-message-id\": pair.user.id,\n                            \"data-query-id\": pair.user.queryId,\n                            style: {\n                                overflowWrap: \"break-word\",\n                                wordBreak: \"break-word\",\n                                maxWidth: isMobile ? \"90%\" : undefined\n                            },\n                            children: [\n                                pair.user.text,\n                                pair.user.isSuggestion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-500 ml-2\",\n                                    children: \"\\uD83D\\uDCA1\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 626,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 612,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 611,\n                        columnNumber: 11\n                    }, undefined),\n                    pair.bot && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-start\",\n                        children: pair.bot ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-[16px] font-[400] rounded-3xl   hyphens-auto bg-white \".concat(isMobile ? \"rounded-[15px] px-3 py-2\" : \"px-4 py-3\"),\n                            style: {\n                                overflowWrap: \"break-word\",\n                                maxWidth: isMobile ? \"90%\" : undefined\n                            },\n                            children: pair.bot.text\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 636,\n                            columnNumber: 15\n                        }, undefined) : // Show loading dots for the last pair if bot is thinking/typing\n                        i === messagePairs.length - 1 && (isBotTyping || isBotThinking) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white \".concat(isMobile ? \"px-3 py-2 rounded-[15px]\" : \"px-4 py-3 rounded-3xl\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1 h-1 bg-black rounded-full loading-dot\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 660,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1 h-1 bg-black rounded-full loading-dot\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 661,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1 h-1 bg-black rounded-full loading-dot\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 662,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 659,\n                                columnNumber: 19\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 652,\n                            columnNumber: 17\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 634,\n                        columnNumber: 11\n                    }, undefined),\n                    i === messagePairs.length - 1 && (isBotTyping || isBotThinking) && !pair.bot && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-start\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white \".concat(isMobile ? \"px-3 py-2 rounded-[15px]\" : \"px-4 py-3 rounded-3xl\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1 h-1 bg-black rounded-full loading-dot\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 683,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1 h-1 bg-black rounded-full loading-dot\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 684,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1 h-1 bg-black rounded-full loading-dot\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 685,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 682,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 675,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 674,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, \"pair-\".concat(pair.id), true, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 592,\n                columnNumber: 7\n            }, undefined));\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSubmit(e);\n        }\n    };\n    const handleSuggestionClick = async (cardTitle)=>{\n        const isFirstMsg = messages.length === 0;\n        // Generate unique ID for this suggestion query\n        const queryId = generateMessageId();\n        const userMessageObj = {\n            id: queryId,\n            queryId: queryId,\n            text: cardTitle,\n            timestamp: Date.now(),\n            type: \"TEXT\",\n            source: \"USER\",\n            isSuggestion: true\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessageObj\n            ]);\n        // Track this suggestion query as pending response\n        setPendingQueries((prev)=>new Map(prev).set(queryId, {\n                id: queryId,\n                message: cardTitle,\n                timestamp: userMessageObj.timestamp,\n                sentAt: Date.now(),\n                isSuggestion: true\n            }));\n        setMessage(\"\");\n        setIsTyping(false);\n        // For first message, set flag to scroll to top\n        if (isFirstMsg) {\n            setShouldScrollToTop(true);\n            setIsFirstMessage(false);\n        }\n        // Show loading dots immediately after sending message\n        setIsBotThinking(true);\n        setIsBotTyping(false);\n        if (inputRef.current) {\n            inputRef.current.style.height = \"104px\";\n            inputRef.current.scrollTop = 0;\n            inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n            inputRef.current.classList.add(\"reset-height\");\n        }\n        try {\n            const userId = localStorage.getItem(\"userID\");\n            if (userId) {\n                await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_SEND, {\n                    userId: userId,\n                    message: cardTitle,\n                    queryId: queryId,\n                    customerName: username,\n                    isSuggestion: true\n                });\n            }\n        } catch (error) {\n            // Hide loading dots on error\n            setIsBotThinking(false);\n            setIsBotTyping(false);\n            const errorMessage = {\n                id: generateMessageId(),\n                text: \"Sorry, there was an error sending your message. Please try again.\",\n                timestamp: Date.now(),\n                type: \"TEXT\",\n                source: \"BOT\",\n                queryId: queryId,\n                isError: true\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n            // Remove from pending queries since we got an error\n            setPendingQueries((prev)=>{\n                const updated = new Map(prev);\n                updated.delete(queryId);\n                return updated;\n            });\n        }\n    };\n    const getMaxSlides = ()=>{\n        if (isMobile) return settingData === null || settingData === void 0 ? void 0 : settingData.suggestedTopics.length;\n        return Math.ceil((settingData === null || settingData === void 0 ? void 0 : settingData.suggestedTopics.length) / 2);\n    };\n    const nextSlide = ()=>{\n        var _settingData_suggestedTopics;\n        const totalCards = (settingData === null || settingData === void 0 ? void 0 : (_settingData_suggestedTopics = settingData.suggestedTopics) === null || _settingData_suggestedTopics === void 0 ? void 0 : _settingData_suggestedTopics.length) || 0;\n        if (totalCards === 0) return;\n        // Fix: Prevent scrolling past the last card\n        const maxSlide = totalCards - 1; // Last card index (0-based)\n        setCurrentSlide((prev)=>{\n            const nextSlide = prev + 1;\n            return nextSlide > maxSlide ? maxSlide : nextSlide;\n        });\n    };\n    const prevSlide = ()=>{\n        setCurrentSlide((prev)=>{\n            const prevSlide = prev - 1;\n            return prevSlide < 0 ? 0 : prevSlide;\n        });\n    };\n    // Check if we're at the boundaries for button states\n    const isAtStart = currentSlide === 0;\n    const isAtEnd = ()=>{\n        var _settingData_suggestedTopics;\n        const totalCards = (settingData === null || settingData === void 0 ? void 0 : (_settingData_suggestedTopics = settingData.suggestedTopics) === null || _settingData_suggestedTopics === void 0 ? void 0 : _settingData_suggestedTopics.length) || 0;\n        if (totalCards === 0) return true;\n        return currentSlide >= totalCards - 1; // Fix: Use >= instead of >\n    };\n    const handleNextSlide = ()=>{\n        nextSlide();\n    };\n    const handlePrevSlide = ()=>{\n        prevSlide();\n    };\n    const handleTouchStart = (e)=>{\n        setTouchStart(e.targetTouches[0].clientX);\n    };\n    const handleTouchMove = (e)=>{\n        setTouchEnd(e.targetTouches[0].clientX);\n    };\n    const handleTouchEnd = ()=>{\n        if (!touchStart || !touchEnd) return;\n        const swipeDistance = touchStart - touchEnd;\n        const minSwipeDistance = 50;\n        if (swipeDistance > minSwipeDistance) {\n            nextSlide();\n        }\n        if (swipeDistance < -minSwipeDistance) {\n            prevSlide();\n        }\n        setTouchStart(0);\n        setTouchEnd(0);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length === 0) {\n                setShowInitialUI(false);\n                const timer = setTimeout({\n                    \"ChatInterface.useEffect.timer\": ()=>setShowInitialUI(true)\n                }[\"ChatInterface.useEffect.timer\"], 60);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                })[\"ChatInterface.useEffect\"];\n            } else {\n                setShowInitialUI(false);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages.length\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const checkScreenSize = {\n                \"ChatInterface.useEffect.checkScreenSize\": ()=>{\n                    if (true) {\n                        const width = window.innerWidth;\n                        setIsMobile(width < 768);\n                        setIsTablet(width >= 768 && width < 1024);\n                    }\n                }\n            }[\"ChatInterface.useEffect.checkScreenSize\"];\n            checkScreenSize();\n            const handleResize = {\n                \"ChatInterface.useEffect.handleResize\": ()=>{\n                    setCurrentSlide(0);\n                    checkScreenSize();\n                }\n            }[\"ChatInterface.useEffect.handleResize\"];\n            if (true) {\n                window.addEventListener(\"resize\", handleResize);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>window.removeEventListener(\"resize\", handleResize)\n                })[\"ChatInterface.useEffect\"];\n            }\n        }\n    }[\"ChatInterface.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (inputRef.current && messages.length === 0) {\n                const shouldAutoFocus = window.innerWidth >= 768;\n                if (shouldAutoFocus) {\n                    const timer = setTimeout({\n                        \"ChatInterface.useEffect.timer\": ()=>{\n                            var _inputRef_current;\n                            (_inputRef_current = inputRef.current) === null || _inputRef_current === void 0 ? void 0 : _inputRef_current.focus();\n                        }\n                    }[\"ChatInterface.useEffect.timer\"], 100);\n                    return ({\n                        \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                    })[\"ChatInterface.useEffect\"];\n                }\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages.length\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            return ({\n                \"ChatInterface.useEffect\": ()=>{\n                    if (sseConnection) {\n                        sseConnection.close();\n                    }\n                }\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], [\n        sseConnection\n    ]);\n    // Check if settingData has been populated with actual data\n    const hasSettingData = settingData && Object.keys(settingData).some((key)=>settingData[key] !== null);\n    // Show loading state while waiting for data\n    // if (!hasSettingData) {\n    //   return (\n    //     <div className=\"bg-white flex flex-col min-h-screen items-center justify-center\">\n    //       <div className=\"text-center\">\n    //         <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4\"></div>\n    //         <p className=\"text-gray-600\">Loading chat settings...</p>\n    //         {contextError && (\n    //           <p className=\"text-red-500 mt-2\">Error: {contextError}</p>\n    //         )}\n    //       </div>\n    //     </div>\n    //   );\n    // }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-94fe332e7a778b23\" + \" \" + \"bg-white flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-94fe332e7a778b23\" + \" \" + \"hidden lg:flex flex-1 flex-col px-4 \",\n                children: messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-94fe332e7a778b23\" + \" \" + \"flex flex-1 items-center justify-center min-h-[calc(100vh-64px)] mt-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-94fe332e7a778b23\" + \" \" + \"flex flex-col items-center w-[768px] justify-center mx-auto \",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    transitionDelay: showInitialUI ? \"40ms\" : \"0ms\"\n                                },\n                                className: \"jsx-94fe332e7a778b23\" + \" \" + \"text-4xl text-gray-900 mb-6 text-center transition-opacity duration-500 \".concat(showInitialUI ? \"opacity-100\" : \"opacity-0\"),\n                                children: \"How can I help you?\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 928,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                style: {\n                                    transitionDelay: showInitialUI ? \"80ms\" : \"0ms\"\n                                },\n                                className: \"jsx-94fe332e7a778b23\" + \" \" + \"relative w-full mb-6 flex flex-col items-center transition-all duration-500 \".concat(showInitialUI ? \"opacity-100 \" : \"opacity-0\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        ref: messages.length === 0 ? inputRef : null,\n                                        value: message,\n                                        onChange: handleInputChange,\n                                        onKeyDown: handleKeyPress,\n                                        placeholder: \"Ask anything\",\n                                        autoFocus: messages.length === 0,\n                                        rows: 1,\n                                        style: {\n                                            boxSizing: \"border-box\",\n                                            zIndex: 1001,\n                                            position: \"relative\",\n                                            minHeight: \"104px\",\n                                            maxHeight: \"104px\",\n                                            height: \"104px\",\n                                            resize: \"none\",\n                                            overflowY: \"auto\"\n                                        },\n                                        className: \"jsx-94fe332e7a778b23\" + \" \" + \"pt-3 px-4 lg:p-4 pb-16 pr-12 w-full h-[104px] overflow-y-auto hide-scrollbar text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base shadow-md resize-none\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 946,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: message.trim().length === 0,\n                                        className: \"jsx-94fe332e7a778b23\" + \" \" + \"absolute right-3 bottom-3 w-9 h-9 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation \".concat(message.trim().length === 0 ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                            className: \"w-4 h-4 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 975,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 966,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 937,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    transitionDelay: showInitialUI ? \"40ms\" : \"0ms\",\n                                    zIndex: 10\n                                },\n                                className: \"jsx-94fe332e7a778b23\" + \" \" + \"relative w-full max-w-2xl transition-all duration-200 ease-in-out \".concat(isTyping ? \"opacity-100 pointer-events-none\" // TC1\n                                 : showInitialUI ? \"opacity-100 \" : \"opacity-0\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-94fe332e7a778b23\" + \" \" + \"overflow-hidden px-4\",\n                                        children: !(settingData === null || settingData === void 0 ? void 0 : settingData.suggestedTopics) || settingData.suggestedTopics.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-94fe332e7a778b23\" + \" \" + \"flex justify-center items-center py-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-94fe332e7a778b23\" + \" \" + \"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 996,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 995,\n                                            columnNumber: 21\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: carouselRef,\n                                            style: {\n                                                transform: \"translateX(-\".concat(currentSlide * 100, \"%)\")\n                                            },\n                                            className: \"jsx-94fe332e7a778b23\" + \" \" + \"flex gap-4 transition-transform duration-300 ease-in-out\",\n                                            children: settingData.suggestedTopics.map((card, cardIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleSuggestionClick(card === null || card === void 0 ? void 0 : card.question),\n                                                    style: {\n                                                        width: \"calc(100% - 16px)\",\n                                                        minHeight: \"40px\"\n                                                    },\n                                                    className: \"jsx-94fe332e7a778b23\" + \" \" + \"py-3 px-4 rounded-[8px] bg-[#f6f6f6] text-left group transition-all duration-200 touch-manipulation flex-shrink-0 hover:bg-[#e6e6e6]\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-94fe332e7a778b23\" + \" \" + \"text-[16px] font-[600] text-black mb-1 leading-snug break-words word-wrap overflow-wrap-anywhere\",\n                                                            children: card.question\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 1016,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-94fe332e7a778b23\" + \" \" + \"text-[14px] text-gray-500 leading-snug break-words word-wrap overflow-wrap-anywhere\",\n                                                            children: card.subtitle\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 1019,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, cardIndex, true, {\n                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                    lineNumber: 1007,\n                                                    columnNumber: 25\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 999,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 992,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handlePrevSlide,\n                                        disabled: isAtStart,\n                                        className: \"jsx-94fe332e7a778b23\" + \" \" + \"absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-6 w-8 h-8 border rounded-full flex items-center justify-center shadow-md transition-all duration-200 touch-manipulation z-10 \".concat(isAtStart ? \"bg-gray-100 border-gray-300 cursor-not-allowed\" : \"bg-white border-gray-200 hover:bg-gray-50 cursor-pointer\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronLeft, {\n                                            className: \"w-3 h-3 \".concat(isAtStart ? \"text-gray-400\" : \"text-gray-600\")\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1036,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 1027,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleNextSlide,\n                                        disabled: isAtEnd(),\n                                        className: \"jsx-94fe332e7a778b23\" + \" \" + \"absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-6 w-8 h-8 border rounded-full flex items-center justify-center shadow-md transition-all duration-200 touch-manipulation z-10 \".concat(isAtEnd() ? \"bg-gray-100 border-gray-300 cursor-not-allowed\" : \"bg-white border-gray-200 hover:bg-gray-50 cursor-pointer\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronRight, {\n                                            className: \"w-3 h-3 \".concat(isAtEnd() ? \"text-gray-400\" : \"text-gray-600\")\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1051,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 1042,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 979,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 927,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                    lineNumber: 926,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: desktopMessagesContainerRef,\n                            style: {\n                                overscrollBehavior: \"contain\"\n                            },\n                            className: \"jsx-94fe332e7a778b23\" + \" \" + \"flex-1 overflow-y-auto max-h-[calc(100vh-200px)] mt-14\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-94fe332e7a778b23\" + \" \" + \"w-full lg:w-[768px] mx-auto px-4 h-fit \",\n                                children: [\n                                    renderMessagePairs(),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: messagesEndRef,\n                                        className: \"jsx-94fe332e7a778b23\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 1069,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 1067,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 1062,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                zIndex: 1000,\n                                marginTop: \"20px\"\n                            },\n                            className: \"jsx-94fe332e7a778b23\" + \" \" + \"fixed bottom-0 left-0 right-0 p-4 mt-5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSubmit,\n                                    className: \"jsx-94fe332e7a778b23\" + \" \" + \"relative w-full lg:w-[768px] mx-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            ref: messages.length > 0 ? inputRef : null,\n                                            value: message,\n                                            onChange: handleInputChange,\n                                            onKeyDown: handleKeyPress,\n                                            placeholder: \"Ask anything\",\n                                            rows: 1,\n                                            style: {\n                                                boxSizing: \"border-box\",\n                                                zIndex: 1001,\n                                                position: \"relative\",\n                                                minHeight: \"104px\",\n                                                maxHeight: \"104px\",\n                                                height: \"104px\",\n                                                resize: \"none\",\n                                                overflowY: \"auto\"\n                                            },\n                                            className: \"jsx-94fe332e7a778b23\" + \" \" + \"pt-3 px-4 md:px-5 pb-10 pr-12 w-full h-[104px] overflow-y-auto text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base shadow-lg resize-none hide-scrollbar\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1081,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: message.trim().length === 0,\n                                            className: \"jsx-94fe332e7a778b23\" + \" \" + \"absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation \".concat(message.trim().length === 0 ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                                className: \"w-4 h-4 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1109,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1100,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1077,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-94fe332e7a778b23\" + \" \" + \"text-center mt-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-94fe332e7a778b23\" + \" \" + \"text-xs text-gray-500\",\n                                        children: [\n                                            \"This chat is powered by\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                className: \"jsx-94fe332e7a778b23\" + \" \" + \"text-black\",\n                                                children: \"Driply.me\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1115,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 1113,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1112,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 1073,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 924,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-94fe332e7a778b23\" + \" \" + \"lg:hidden overflow-hidden fixed inset-0 flex flex-col  mt-10 \",\n                children: [\n                    messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-94fe332e7a778b23\" + \" \" + \"flex flex-col items-center pt-15 pb-4 px-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-94fe332e7a778b23\" + \" \" + \"hidden md:flex lg:hidden flex-col items-center\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1128,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 1127,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    paddingBottom: \"260px\"\n                                },\n                                className: \"jsx-94fe332e7a778b23\" + \" \" + \"flex flex-col items-center justify-center flex-1 px-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"40ms\" : \"0ms\"\n                                    },\n                                    className: \"jsx-94fe332e7a778b23\" + \" \" + \"text-2xl md:text-4xl text-gray-900 text-center leading-relaxed transition-all duration-700 \".concat(showInitialUI ? \"opacity-100 \" : \"opacity-0 \"),\n                                    children: \"How can I help you?\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1134,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 1130,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: mobileMessagesContainerRef,\n                                style: {\n                                    overscrollBehavior: \"contain\"\n                                },\n                                className: \"jsx-94fe332e7a778b23\" + \" \" + \"md:hidden flex-1 overflow-y-auto max-h-[calc(100vh-190px)] hide-scrollbar\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-94fe332e7a778b23\" + \" \" + \"w-full max-w-[803px] mx-auto px-4  pt-12\",\n                                    children: [\n                                        renderMessagePairs(),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: messagesEndRef,\n                                            className: \"jsx-94fe332e7a778b23\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1156,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1154,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 1149,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: tabletMessagesContainerRef,\n                                style: {\n                                    overscrollBehavior: \"contain\"\n                                },\n                                className: \"jsx-94fe332e7a778b23\" + \" \" + \"hidden md:block lg:hidden flex-1 overflow-y-auto max-h-[calc(100vh-100px)] pb-[140px] hide-scrollbar\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-94fe332e7a778b23\" + \" \" + \"w-full max-w-[803px] mx-auto px-4 pb-6 pt-12\",\n                                    children: [\n                                        renderMessagePairs(),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: messagesEndRef,\n                                            className: \"jsx-94fe332e7a778b23\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1168,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1166,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 1161,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    minHeight: \"160px\",\n                                    zIndex: 1000,\n                                    paddingBottom: \"env(safe-area-inset-bottom, 0)\",\n                                    transform: \"translateZ(0)\",\n                                    backfaceVisibility: \"hidden\"\n                                },\n                                className: \"jsx-94fe332e7a778b23\" + \" \" + \"fixed bottom-0 left-0 right-0 bg-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        transform: \"translateZ(0)\"\n                                    },\n                                    className: \"jsx-94fe332e7a778b23\" + \" \" + \"px-4 py-4 bg-white\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            style: {\n                                                zIndex: 1001\n                                            },\n                                            className: \"jsx-94fe332e7a778b23\" + \" \" + \"relative bg-white\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    transform: \"translateZ(0)\"\n                                                },\n                                                className: \"jsx-94fe332e7a778b23\" + \" \" + \"relative w-full max-w-[890px] mx-auto bg-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        ref: messages.length > 0 ? inputRef : null,\n                                                        value: message,\n                                                        onChange: handleInputChange,\n                                                        onKeyDown: handleKeyPress,\n                                                        placeholder: \"Ask anything\",\n                                                        rows: 1,\n                                                        style: {\n                                                            boxSizing: \"border-box\",\n                                                            minHeight: \"104px\",\n                                                            maxHeight: \"104px\",\n                                                            height: \"104px\",\n                                                            zIndex: 1001,\n                                                            overscrollBehavior: \"none\",\n                                                            position: \"relative\",\n                                                            resize: \"none\",\n                                                            overflowY: \"auto\"\n                                                        },\n                                                        className: \"jsx-94fe332e7a778b23\" + \" \" + \"w-full h-[104px] pt-3 px-4 pb-10 pr-12 text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base resize-none overflow-y-auto\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1195,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        disabled: message.trim().length === 0,\n                                                        className: \"jsx-94fe332e7a778b23\" + \" \" + \"absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation \".concat(message.trim().length === 0 ? \"bg-[#E0E0E0] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                                            className: \"w-4 h-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 1224,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1215,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1191,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1186,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-94fe332e7a778b23\" + \" \" + \"text-center py-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-94fe332e7a778b23\" + \" \" + \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"This chat is powered by\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"jsx-94fe332e7a778b23\" + \" \" + \"text-black\",\n                                                        children: \"Driply.me\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1231,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1229,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1228,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1182,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 1172,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-94fe332e7a778b23\" + \" \" + \"absolute bottom-0 left-0 right-0 bg-white\",\n                        children: messages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"120ms\" : \"0ms\",\n                                        position: \"relative\",\n                                        zIndex: 10,\n                                        backgroundColor: \"white\",\n                                        paddingBottom: \"10px\"\n                                    },\n                                    className: \"jsx-94fe332e7a778b23\" + \" \" + \"\".concat(isMobile ? \"px-0\" : \"px-4\", \" pt-2 pb-2 transition-all duration-500 ease-in-out \").concat(isTyping ? \"opacity-0 pointer-events-none\" : showInitialUI ? \"opacity-100\" : \"opacity-0\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-94fe332e7a778b23\" + \" \" + \"overflow-hidden px-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                ref: carouselRef,\n                                                style: {\n                                                    transform: \"translateX(-\".concat(currentSlide * 100, \"%)\")\n                                                },\n                                                onTouchStart: handleTouchStart,\n                                                onTouchMove: handleTouchMove,\n                                                onTouchEnd: handleTouchEnd,\n                                                className: \"jsx-94fe332e7a778b23\" + \" \" + \"flex gap-4 transition-transform duration-300 ease-in-out\",\n                                                children: settingData === null || settingData === void 0 ? void 0 : (_settingData_suggestedTopics1 = settingData.suggestedTopics) === null || _settingData_suggestedTopics1 === void 0 ? void 0 : _settingData_suggestedTopics1.map((card, cardIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleSuggestionClick(card.question),\n                                                        style: {\n                                                            // Calculate width accounting for gap (gap-4 = 16px)\n                                                            width: \"calc(100% - 16px)\",\n                                                            minHeight: isMobile ? \"90px\" : \"80px\"\n                                                        },\n                                                        className: \"jsx-94fe332e7a778b23\" + \" \" + \"\".concat(isMobile ? \"py-3 mt-3\" : \"py-3\", \" px-4 rounded-[8px] bg-[#F6F6F6] text-left hover:bg-[#D6EDFF] transition-all duration-200 flex-shrink-0 touch-manipulation\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-94fe332e7a778b23\" + \" \" + \"\".concat(isMobile ? \"text-[16px]\" : \"text-[16px]\", \" font-[600] text-black mb-1 leading-snug break-words word-wrap overflow-wrap-anywhere\"),\n                                                                children: card.question\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                lineNumber: 1286,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-94fe332e7a778b23\" + \" \" + \"\".concat(isMobile ? \"text-[14px]\" : \"text-[14px]\", \" text-gray-500 leading-snug break-words word-wrap overflow-wrap-anywhere\"),\n                                                                children: card.subtitle\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                lineNumber: 1293,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, cardIndex, true, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1274,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1263,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1262,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handlePrevSlide,\n                                            disabled: isAtStart,\n                                            className: \"jsx-94fe332e7a778b23\" + \" \" + \"hidden md:flex absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-10 w-8 h-8 border rounded-full items-center justify-center shadow-md transition-all duration-200 touch-manipulation z-10 \".concat(isAtStart ? \"bg-gray-100 border-gray-300 cursor-not-allowed\" : \"bg-white border-gray-200 hover:bg-gray-50 cursor-pointer\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronLeft, {\n                                                className: \"w-3 h-3 \".concat(isAtStart ? \"text-gray-400\" : \"text-gray-600\")\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1315,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1306,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleNextSlide,\n                                            disabled: isAtEnd(),\n                                            className: \"jsx-94fe332e7a778b23\" + \" \" + \"hidden md:flex absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-10 w-8 h-8 border rounded-full items-center justify-center shadow-md transition-all duration-200 touch-manipulation z-10 \".concat(isAtEnd() ? \"bg-gray-100 border-gray-300 cursor-not-allowed\" : \"bg-white border-gray-200 hover:bg-gray-50 cursor-pointer\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronRight, {\n                                                className: \"w-3 h-3 \".concat(isAtEnd() ? \"text-gray-400\" : \"text-gray-600\")\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1330,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1321,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1244,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"200ms\" : \"0ms\",\n                                        position: \"relative\",\n                                        zIndex: 5,\n                                        paddingBottom: \"env(safe-area-inset-bottom)\"\n                                    },\n                                    className: \"jsx-94fe332e7a778b23\" + \" \" + \"px-4 bg-white transition-all duration-500 \".concat(showInitialUI ? \"opacity-100 translate-y-0\" : \"translate-y-4\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            className: \"jsx-94fe332e7a778b23\" + \" \" + \"relative\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-94fe332e7a778b23\" + \" \" + \"relative w-full max-w-[890px] mx-auto\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        ref: messages.length === 0 ? inputRef : null,\n                                                        value: message,\n                                                        onChange: handleInputChange,\n                                                        onKeyDown: handleKeyPress,\n                                                        placeholder: \"Ask anything\",\n                                                        rows: 1,\n                                                        style: {\n                                                            boxSizing: \"border-box\",\n                                                            minHeight: \"104px\",\n                                                            maxHeight: \"104px\",\n                                                            height: \"104px\",\n                                                            fontSize: \"16px\",\n                                                            zIndex: 1001,\n                                                            overscrollBehaviorY: \"contain\",\n                                                            position: \"relative\",\n                                                            resize: \"none\",\n                                                            overflowY: \"auto\"\n                                                        },\n                                                        className: \"jsx-94fe332e7a778b23\" + \" \" + \"w-full h-[104px] pt-3 px-4 pb-10 pr-12 text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base resize-none overflow-y-auto\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1352,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        disabled: message.trim().length === 0,\n                                                        className: \"jsx-94fe332e7a778b23\" + \" \" + \"absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation \".concat(message.trim().length === 0 ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                                            className: \"w-4 h-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 1382,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1373,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1351,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1350,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-94fe332e7a778b23\" + \" \" + \"text-center py-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-94fe332e7a778b23\" + \" \" + \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"This chat is powered by\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"jsx-94fe332e7a778b23\" + \" \" + \"text-black\",\n                                                        children: \"Driply.me\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1389,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1387,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1386,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1339,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 1240,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 1124,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"94fe332e7a778b23\",\n                children: '.loading-dot{-webkit-animation:loading-dots 1.4s infinite ease-in-out both;-moz-animation:loading-dots 1.4s infinite ease-in-out both;-o-animation:loading-dots 1.4s infinite ease-in-out both;animation:loading-dots 1.4s infinite ease-in-out both}.loading-dot:nth-child(1){-webkit-animation-delay:-.32s;-moz-animation-delay:-.32s;-o-animation-delay:-.32s;animation-delay:-.32s}.loading-dot:nth-child(2){-webkit-animation-delay:-.16s;-moz-animation-delay:-.16s;-o-animation-delay:-.16s;animation-delay:-.16s}.loading-dot:nth-child(3){-webkit-animation-delay:0s;-moz-animation-delay:0s;-o-animation-delay:0s;animation-delay:0s}@-webkit-keyframes loading-dots{0%,80%,100%{-webkit-transform:scale(1);transform:scale(1);opacity:1}40%{-webkit-transform:scale(1.5);transform:scale(1.5);opacity:.7}}@-moz-keyframes loading-dots{0%,80%,100%{-moz-transform:scale(1);transform:scale(1);opacity:1}40%{-moz-transform:scale(1.5);transform:scale(1.5);opacity:.7}}@-o-keyframes loading-dots{0%,80%,100%{-o-transform:scale(1);transform:scale(1);opacity:1}40%{-o-transform:scale(1.5);transform:scale(1.5);opacity:.7}}@keyframes loading-dots{0%,80%,100%{-webkit-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1);opacity:1}40%{-webkit-transform:scale(1.5);-moz-transform:scale(1.5);-o-transform:scale(1.5);transform:scale(1.5);opacity:.7}}.hide-scrollbar{-ms-overflow-style:none!important;scrollbar-width:none!important}.hide-scrollbar::-webkit-scrollbar{display:none!important}@media only screen and (max-width:1023px){.lg\\\\\\\\.jsx-94fe332e7a778b23:hidden .absolute.bottom-0.jsx-94fe332e7a778b23{position:fixed!important;bottom:env(safe-area-inset-bottom,0);left:0;right:0;width:100%;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;z-index:1000;background-color:white}textarea.jsx-94fe332e7a778b23{-webkit-user-select:auto!important;-moz-user-select:auto!important;-ms-user-select:auto!important;user-select:auto!important;-webkit-appearance:none;-moz-appearance:none;-ms-appearance:none;appearance:none;-ms-scroll-chaining:none;overscroll-behavior:none;line-height:24px;min-height:104px!important;max-height:104px!important;height:104px!important;resize:none!important;overflow-y:auto!important;position:relative!important;-webkit-transform:translatez(0);-moz-transform:translatez(0);-ms-transform:translatez(0);-o-transform:translatez(0);transform:translatez(0);-webkit-backface-visibility:hidden;-moz-backface-visibility:hidden;backface-visibility:hidden;-webkit-perspective:1000;-moz-perspective:1000;perspective:1000;z-index:1001!important;background-color:white}.lg\\\\\\\\.jsx-94fe332e7a778b23:hidden form.jsx-94fe332e7a778b23{position:relative!important;z-index:1001;background-color:white}button[type=\"submit\"].jsx-94fe332e7a778b23{position:absolute!important;right:12px;bottom:12px;z-index:1002!important;-webkit-tap-highlight-color:transparent;-ms-touch-action:-ms-manipulation;-ms-touch-action:manipulation;touch-action:manipulation;-webkit-transform:translatez(0);-moz-transform:translatez(0);-ms-transform:translatez(0);-o-transform:translatez(0);transform:translatez(0)}.lg\\\\\\\\.jsx-94fe332e7a778b23:hidden .bg-white.jsx-94fe332e7a778b23{background-color:white!important}}@media only screen and (min-width:1024px){textarea.jsx-94fe332e7a778b23{min-height:104px!important;max-height:104px!important;height:104px!important;overflow-y:auto!important;resize:none!important;-ms-scroll-chaining:none;overscroll-behavior:contain}.reset-height.jsx-94fe332e7a778b23{min-height:104px!important;max-height:104px!important;height:104px!important;overflow-y:auto!important;resize:none!important}}.fixed-bottom.jsx-94fe332e7a778b23{position:fixed!important;bottom:0;left:0;right:0;background:white}'\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n        lineNumber: 922,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatInterface, \"11I1ZIMf13g4EsXvUwg83BGQ4a8=\", false, function() {\n    return [\n        _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__.useChatContext\n    ];\n});\n_c = ChatInterface;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatInterface);\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ChatInterface.jsx\n"));

/***/ })

});