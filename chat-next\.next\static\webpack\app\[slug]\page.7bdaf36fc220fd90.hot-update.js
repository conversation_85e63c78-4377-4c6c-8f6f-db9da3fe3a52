"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[slug]/page",{

/***/ "(app-pages-browser)/./src/utils/constants.js":
/*!********************************!*\
  !*** ./src/utils/constants.js ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APP_NAME: () => (/* binding */ APP_NAME),\n/* harmony export */   SUGGESTION_CARDS: () => (/* binding */ SUGGESTION_CARDS)\n/* harmony export */ });\nconst APP_NAME = 'DRIPLY';\nconst SUGGESTION_CARDS = [\n    {\n        title: \"Design a schema\",\n        subtitle: \"for an online merch store\"\n    },\n    {\n        title: \"Explain airplane\",\n        subtitle: \"to someone 5 years old\"\n    },\n    {\n        title: \"Create a work plan\",\n        subtitle: \"for beginners at home\"\n    },\n    {\n        title: \"Write a recipe\",\n        subtitle: \"for chocolate chip cookies\"\n    },\n    {\n        title: \"Plan a budget\",\n        subtitle: \"for a weekend trip\"\n    },\n    {\n        title: \"Learn JavaScript \",\n        subtitle: \"basic concepts \"\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy91dGlscy9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFDTyxNQUFNQSxXQUFXLFNBQVE7QUFHekIsTUFBTUMsbUJBQW1CO0lBQzlCO1FBQ0VDLE9BQU87UUFDUEMsVUFBVTtJQUNaO0lBQ0E7UUFDRUQsT0FBTztRQUNQQyxVQUFVO0lBQ1o7SUFDQTtRQUNFRCxPQUFPO1FBQ1BDLFVBQVU7SUFDWjtJQUNBO1FBQ0VELE9BQU87UUFDUEMsVUFBVTtJQUNaO0lBQ0E7UUFDRUQsT0FBTztRQUNQQyxVQUFVO0lBQ1o7SUFDQTtRQUNFRCxPQUFPO1FBQ1BDLFVBQVU7SUFDWjtDQUNEIiwic291cmNlcyI6WyJEOlxcTGFwdG9wIGRhdGFcXERSSVBMWS1DSEFUXFxjaGF0LW5leHRcXHNyY1xcdXRpbHNcXGNvbnN0YW50cy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcclxuZXhwb3J0IGNvbnN0IEFQUF9OQU1FID0gJ0RSSVBMWSdcclxuXHJcblxyXG5leHBvcnQgY29uc3QgU1VHR0VTVElPTl9DQVJEUyA9IFtcclxuICB7XHJcbiAgICB0aXRsZTogXCJEZXNpZ24gYSBzY2hlbWFcIixcclxuICAgIHN1YnRpdGxlOiBcImZvciBhbiBvbmxpbmUgbWVyY2ggc3RvcmVcIlxyXG4gIH0sXHJcbiAge1xyXG4gICAgdGl0bGU6IFwiRXhwbGFpbiBhaXJwbGFuZVwiLFxyXG4gICAgc3VidGl0bGU6IFwidG8gc29tZW9uZSA1IHllYXJzIG9sZFwiXHJcbiAgfSxcclxuICB7XHJcbiAgICB0aXRsZTogXCJDcmVhdGUgYSB3b3JrIHBsYW5cIixcclxuICAgIHN1YnRpdGxlOiBcImZvciBiZWdpbm5lcnMgYXQgaG9tZVwiXHJcbiAgfSxcclxuICB7XHJcbiAgICB0aXRsZTogXCJXcml0ZSBhIHJlY2lwZVwiLFxyXG4gICAgc3VidGl0bGU6IFwiZm9yIGNob2NvbGF0ZSBjaGlwIGNvb2tpZXNcIlxyXG4gIH0sXHJcbiAge1xyXG4gICAgdGl0bGU6IFwiUGxhbiBhIGJ1ZGdldFwiLFxyXG4gICAgc3VidGl0bGU6IFwiZm9yIGEgd2Vla2VuZCB0cmlwXCJcclxuICB9LFxyXG4gIHtcclxuICAgIHRpdGxlOiBcIkxlYXJuIEphdmFTY3JpcHQgXCIsXHJcbiAgICBzdWJ0aXRsZTogXCJiYXNpYyBjb25jZXB0cyBcIlxyXG4gIH1cclxuXVxyXG5cclxuXHJcbiJdLCJuYW1lcyI6WyJBUFBfTkFNRSIsIlNVR0dFU1RJT05fQ0FSRFMiLCJ0aXRsZSIsInN1YnRpdGxlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/constants.js\n"));

/***/ })

});