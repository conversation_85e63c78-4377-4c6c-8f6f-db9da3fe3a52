"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[slug]/page",{

/***/ "(app-pages-browser)/./src/components/ChatInterface.jsx":
/*!******************************************!*\
  !*** ./src/components/ChatInterface.jsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/constants */ \"(app-pages-browser)/./src/utils/constants.js\");\n/* harmony import */ var _utils_config__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/config */ \"(app-pages-browser)/./src/utils/config.js\");\n/* harmony import */ var _barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FaArrowUp,FaChevronLeft,FaChevronRight!=!react-icons/fa6 */ \"(app-pages-browser)/./node_modules/react-icons/fa6/index.mjs\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../contexts/ChatContext */ \"(app-pages-browser)/./src/contexts/ChatContext.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst ChatInterface = (param)=>{\n    let { slug, query } = param;\n    var _settingData_suggestedTopics, _settingData_suggestedTopics1, _settingData_suggestedTopics2;\n    _s();\n    // Context API\n    const { metadata, updateMetadata, setLoadingState, setErrorState, clearError, settingData, updateSettingData, getCustomerName, getBusinessName, hasMetadata, loading: contextLoading, error: contextError } = (0,_contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__.useChatContext)();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [currentSlide, setCurrentSlide] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [touchStart, setTouchStart] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [touchEnd, setTouchEnd] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isTablet, setIsTablet] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showInitialUI, setShowInitialUI] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isBotTyping, setIsBotTyping] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isBotThinking, setIsBotThinking] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [sseConnection, setSseConnection] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isFirstMessage, setIsFirstMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [shouldScrollToTop, setShouldScrollToTop] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Enhanced message pairing state\n    const [pendingQueries, setPendingQueries] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(new Map()); // Track queries waiting for responses\n    const [messageSequence, setMessageSequence] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0); // Global sequence counter\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const carouselRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const desktopMessagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const mobileMessagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const tabletMessagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // Debug: Log state updates when they actually happen\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (metadata && Object.keys(metadata).some({\n                \"ChatInterface.useEffect\": (key)=>metadata[key] !== null\n            }[\"ChatInterface.useEffect\"])) {\n                console.log(\"✅ METADATA STATE UPDATED:\", metadata);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        metadata\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (settingData && Object.keys(settingData).some({\n                \"ChatInterface.useEffect\": (key)=>settingData[key] !== null\n            }[\"ChatInterface.useEffect\"])) {\n                console.log(\"✅ SETTING DATA STATE UPDATED:\", settingData);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        settingData\n    ]);\n    // Cleanup pending queries that are too old (timeout mechanism)\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const cleanup = setInterval({\n                \"ChatInterface.useEffect.cleanup\": ()=>{\n                    const now = Date.now();\n                    const TIMEOUT_MS = 60000; // 60 seconds timeout\n                    setPendingQueries({\n                        \"ChatInterface.useEffect.cleanup\": (prev)=>{\n                            const updated = new Map(prev);\n                            let hasChanges = false;\n                            for (const [queryId, queryData] of prev.entries()){\n                                if (now - queryData.sentAt > TIMEOUT_MS) {\n                                    updated.delete(queryId);\n                                    hasChanges = true;\n                                }\n                            }\n                            return hasChanges ? updated : prev;\n                        }\n                    }[\"ChatInterface.useEffect.cleanup\"]);\n                }\n            }[\"ChatInterface.useEffect.cleanup\"], 10000); // Check every 10 seconds\n            return ({\n                \"ChatInterface.useEffect\": ()=>clearInterval(cleanup)\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], []);\n    // Initialize carousel to start from middle section for better circular behavior\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            var _settingData_suggestedTopics;\n            const totalCards = (settingData === null || settingData === void 0 ? void 0 : (_settingData_suggestedTopics = settingData.suggestedTopics) === null || _settingData_suggestedTopics === void 0 ? void 0 : _settingData_suggestedTopics.length) || 0;\n            if (totalCards > 0 && currentSlide === 0) {\n                // Start from the middle section (second copy of cards)\n                setCurrentSlide(totalCards);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        settingData === null || settingData === void 0 ? void 0 : (_settingData_suggestedTopics = settingData.suggestedTopics) === null || _settingData_suggestedTopics === void 0 ? void 0 : _settingData_suggestedTopics.length\n    ]);\n    // Utility functions for message pairing\n    const generateMessageId = ()=>{\n        const sequence = messageSequence + 1;\n        setMessageSequence(sequence);\n        return \"msg_\".concat(Date.now(), \"_\").concat(sequence, \"_\").concat(Math.random().toString(36).substr(2, 9));\n    };\n    const createMessagePairs = (messages)=>{\n        const pairs = [];\n        const processedMessages = new Set();\n        // Sort messages by timestamp to handle out-of-order arrivals\n        const sortedMessages = [\n            ...messages\n        ].sort((a, b)=>a.timestamp - b.timestamp);\n        // Group messages by queryId for proper pairing\n        const messageGroups = new Map();\n        sortedMessages.forEach((msg)=>{\n            if (msg.source === \"USER\") {\n                // User message starts a new conversation pair\n                const queryId = msg.queryId || msg.id || \"fallback_\".concat(msg.timestamp);\n                if (!messageGroups.has(queryId)) {\n                    messageGroups.set(queryId, {\n                        user: null,\n                        bot: null,\n                        timestamp: msg.timestamp\n                    });\n                }\n                messageGroups.get(queryId).user = msg;\n            } else if (msg.source === \"BOT\") {\n                // Bot message should be paired with corresponding user message\n                const queryId = msg.queryId || msg.responseToId;\n                if (queryId && messageGroups.has(queryId)) {\n                    messageGroups.get(queryId).bot = msg;\n                } else {\n                    // Fallback: pair with most recent unpaired user message\n                    const unpairedGroups = Array.from(messageGroups.entries()).filter((param)=>{\n                        let [_, group] = param;\n                        return group.user && !group.bot;\n                    }).sort((a, b)=>b[1].timestamp - a[1].timestamp);\n                    if (unpairedGroups.length > 0) {\n                        const [groupId, group] = unpairedGroups[0];\n                        group.bot = msg;\n                        // Update the message with proper queryId for future reference\n                        msg.queryId = groupId;\n                    }\n                }\n            }\n        });\n        // Convert groups to pairs array, sorted by timestamp\n        const sortedGroups = Array.from(messageGroups.entries()).sort((a, b)=>a[1].timestamp - b[1].timestamp);\n        return sortedGroups.map((param)=>{\n            let [queryId, group] = param;\n            return {\n                id: queryId,\n                user: group.user,\n                bot: group.bot,\n                timestamp: group.timestamp,\n                isComplete: !!(group.user && group.bot),\n                isPending: !!(group.user && !group.bot)\n            };\n        });\n    };\n    // Detect iOS for targeted fixes\n    const isIOS = ()=>{\n        if (true) {\n            return /iPhone|iPad|iPod/i.test(navigator.userAgent);\n        }\n        return false;\n    };\n    const username = slug;\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (username && \"object\" !== \"undefined\") {\n                fetchCustomer(username);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        username\n    ]);\n    const fetchCustomer = async (customerName)=>{\n        try {\n            setLoadingState(true);\n            clearError();\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"\".concat(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_SETTINGS, \"?customerName=\").concat(customerName));\n            // Extract data from axios response\n            const responseData = response.data;\n            const metadataFromResponse = responseData.metaData;\n            updateSettingData(responseData);\n            updateMetadata(metadataFromResponse);\n            if (responseData && responseData.customerName) {\n                //  localStorage for backward compatibility\n                localStorage.setItem(\"customerName_userId\", responseData.customerName);\n                localStorage.setItem(\"BusinessName\", responseData.businessName);\n                window.dispatchEvent(new CustomEvent(\"businessNameLoaded\", {\n                    detail: {\n                        businessName: responseData.businessName\n                    }\n                }));\n                const existingUserId = localStorage.getItem(\"userID\");\n                if (existingUserId) {\n                    await fetchExistingMessages(existingUserId);\n                } else {\n                    await initializeChatSession();\n                }\n            } else {\n                await initializeChatSession();\n            }\n        } catch (error) {\n            console.error(\"Error fetching customer data:\", error);\n            setErrorState(\"Failed to load customer settings\");\n            // Fallback metadata\n            updateMetadata({\n                customerName: customerName,\n                businessName: \"Driply\"\n            });\n            localStorage.setItem(\"BusinessName\", \"Driply\");\n            window.dispatchEvent(new CustomEvent(\"businessNameLoaded\", {\n                detail: {\n                    businessName: \"Driply\"\n                }\n            }));\n            await initializeChatSession();\n        } finally{\n            setLoadingState(false);\n        }\n    };\n    const fetchExistingMessages = async (userId)=>{\n        // console.log(\"fetchExistingMessages\");\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"\".concat(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_MESSAGES, \"?userId=\").concat(userId));\n            if (response.data && response.data.length > 0) {\n                setMessages(response.data.map((msg)=>({\n                        text: msg.message,\n                        timestamp: new Date(msg.createdAt).getTime(),\n                        type: msg.type,\n                        source: msg.source\n                    })));\n            }\n            connectToSSE(userId);\n        } catch (error) {\n            await initializeChatSession();\n        }\n    };\n    const initializeChatSession = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_INIT, {\n                customerName: username\n            });\n            if (response.data && response.data.userId) {\n                localStorage.setItem(\"userId\", response.data.userId);\n                localStorage.setItem(\"userID\", response.data._id);\n                await fetchExistingMessages(response.data._id);\n            }\n        } catch (error) {\n            if (!localStorage.getItem(\"BusinessName\")) {\n                localStorage.setItem(\"BusinessName\", \"Driply\");\n                window.dispatchEvent(new CustomEvent(\"businessNameLoaded\", {\n                    detail: {\n                        businessName: \"Driply\"\n                    }\n                }));\n            }\n        }\n    };\n    const connectToSSE = (userId)=>{\n        try {\n            if (sseConnection) {\n                sseConnection.close();\n            }\n            const sseUrl = (0,_utils_config__WEBPACK_IMPORTED_MODULE_4__.getSSEUrl)(userId);\n            const eventSource = new EventSource(sseUrl);\n            setSseConnection(eventSource);\n            eventSource.addEventListener(\"message\", (param)=>{\n                let { data } = param;\n                try {\n                    const contents = JSON.parse(data);\n                    handleSSEMessage(contents);\n                } catch (error) {\n                // Handle parsing error silently\n                }\n            });\n            eventSource.addEventListener(\"error\", ()=>{\n            // Handle connection error silently\n            });\n        } catch (error) {\n        // Handle SSE connection error silently\n        }\n    };\n    const handleSSEMessage = (data)=>{\n        const subtype = data.subType || data.subtype || data.type || \"UNKNOWN\";\n        const content = data.content || \"\";\n        const message = data.message || data.text || \"\";\n        if ([\n            \"TYPING\",\n            \"THINKING\",\n            \"BEHAVIOUR_MESSAGE\"\n        ].includes(subtype)) {\n            switch(subtype){\n                case \"TYPING\":\n                    setIsBotTyping(true);\n                    setIsBotThinking(false);\n                    break;\n                case \"THINKING\":\n                    setIsBotThinking(true);\n                    setIsBotTyping(false);\n                    break;\n                case \"BEHAVIOUR_MESSAGE\":\n                    setIsBotTyping(false);\n                    setIsBotThinking(false);\n                    break;\n            }\n            return;\n        }\n        const displayText = message || content;\n        if (!displayText) return;\n        const displayType = [\n            \"TEXT\",\n            \"MESSAGE\",\n            \"DATA_MESSAGE\"\n        ].includes(subtype) ? subtype : \"UNKNOWN\";\n        // Enhanced message pairing logic\n        const responseToId = data.queryId || data.responseToId || data.correlationId;\n        let queryId = responseToId;\n        // If no explicit queryId, find the most recent pending query\n        if (!queryId) {\n            const pendingEntries = Array.from(pendingQueries.entries()).sort((a, b)=>b[1].timestamp - a[1].timestamp);\n            if (pendingEntries.length > 0) {\n                queryId = pendingEntries[0][0];\n            }\n        }\n        // Create bot message with proper pairing information\n        const botMessage = {\n            id: generateMessageId(),\n            text: displayText,\n            timestamp: Date.now(),\n            type: displayType,\n            source: \"BOT\",\n            queryId: queryId,\n            responseToId: queryId // Explicit response relationship\n        };\n        setMessages((prev)=>[\n                ...prev,\n                botMessage\n            ]);\n        // Remove from pending queries if we found a match\n        if (queryId && pendingQueries.has(queryId)) {\n            setPendingQueries((prev)=>{\n                const updated = new Map(prev);\n                updated.delete(queryId);\n                return updated;\n            });\n        }\n        setIsBotTyping(false);\n        setIsBotThinking(false);\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (message.trim()) {\n            const userMessage = message.trim();\n            const isFirstMsg = messages.length === 0;\n            // Generate unique ID for this query\n            const queryId = generateMessageId();\n            const userMessageObj = {\n                id: queryId,\n                queryId: queryId,\n                text: userMessage,\n                timestamp: Date.now(),\n                type: \"TEXT\",\n                source: \"USER\"\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    userMessageObj\n                ]);\n            // Track this query as pending response\n            setPendingQueries((prev)=>new Map(prev).set(queryId, {\n                    id: queryId,\n                    message: userMessage,\n                    timestamp: userMessageObj.timestamp,\n                    sentAt: Date.now()\n                }));\n            setMessage(\"\");\n            setIsTyping(false);\n            // For first message, set flag to scroll to top\n            if (isFirstMsg) {\n                setShouldScrollToTop(true);\n                setIsFirstMessage(false);\n            }\n            // Show loading dots immediately after sending message\n            setIsBotThinking(true);\n            setIsBotTyping(false);\n            // Reset textarea\n            if (inputRef.current) {\n                inputRef.current.style.height = \"104px\";\n                inputRef.current.scrollTop = 0;\n                inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n                inputRef.current.classList.add(\"reset-height\");\n            }\n            try {\n                const userId = localStorage.getItem(\"userID\");\n                if (userId) {\n                    await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_SEND, {\n                        userId: userId,\n                        message: userMessage,\n                        queryId: queryId,\n                        typs: \"TEXT\",\n                        source: \"USER\",\n                        isTest: query.isTest === \"1\" ? true : false\n                    });\n                }\n            } catch (error) {\n                // Hide loading dots on error\n                setIsBotThinking(false);\n                setIsBotTyping(false);\n                const errorMessage = {\n                    id: generateMessageId(),\n                    text: \"Sorry, there was an error sending your message. Please try again.\",\n                    timestamp: Date.now(),\n                    type: \"TEXT\",\n                    source: \"BOT\",\n                    queryId: queryId,\n                    isError: true\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        errorMessage\n                    ]);\n                // Remove from pending queries since we got an error\n                setPendingQueries((prev)=>{\n                    const updated = new Map(prev);\n                    updated.delete(queryId);\n                    return updated;\n                });\n            }\n        }\n    };\n    const handleTextareaResize = (textarea)=>{\n        if (textarea) {\n            textarea.style.height = \"104px\";\n            const scrollHeight = textarea.scrollHeight;\n            if (isIOS()) {\n                if (scrollHeight > 48) {\n                    textarea.style.overflowY = \"auto\";\n                } else {\n                    textarea.style.overflowY = \"hidden\";\n                }\n            } else {\n                const newHeight = Math.min(250, Math.max(104, scrollHeight));\n                textarea.style.height = \"\".concat(newHeight, \"px\");\n                textarea.style.overflowY = scrollHeight > newHeight ? \"auto\" : \"hidden\";\n            }\n        }\n    };\n    const handleInputChange = (e)=>{\n        const value = e.target.value;\n        setMessage(value);\n        handleTextareaResize(e.target);\n        if (messages.length === 0) {\n            setIsTyping(value.length > 0);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length > 0 && inputRef.current && !message) {\n                const timer = setTimeout({\n                    \"ChatInterface.useEffect.timer\": ()=>{\n                        if (inputRef.current) {\n                            inputRef.current.style.height = \"104px\";\n                            inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n                            inputRef.current.classList.add(\"reset-height\");\n                        }\n                    }\n                }[\"ChatInterface.useEffect.timer\"], 50);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                })[\"ChatInterface.useEffect\"];\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages,\n        message\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length > 0) {\n                handleChatGPTScroll();\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages,\n        isBotTyping,\n        isBotThinking\n    ]);\n    const handleChatGPTScroll = ()=>{\n        setTimeout(()=>{\n            // Determine screen type and get appropriate container\n            const screenWidth = window.innerWidth;\n            const isDesktopLayout = screenWidth >= 1024;\n            const isTabletLayout = screenWidth >= 768 && screenWidth < 1024;\n            let container;\n            if (isDesktopLayout) {\n                container = desktopMessagesContainerRef.current;\n            } else if (isTabletLayout) {\n                container = tabletMessagesContainerRef.current;\n            } else {\n                container = mobileMessagesContainerRef.current;\n            }\n            if (!container) return;\n            if (shouldScrollToTop) {\n                // For first message, scroll to top to show the message there\n                container.scrollTo({\n                    top: 0,\n                    behavior: \"smooth\"\n                });\n                setShouldScrollToTop(false);\n            } else {\n                // Different scrolling behavior for each screen type\n                const messageElements = container.querySelectorAll(\".message-pair\");\n                if (messageElements.length > 0) {\n                    const lastMessagePair = messageElements[messageElements.length - 1];\n                    const containerHeight = container.clientHeight;\n                    const pairHeight = lastMessagePair.offsetHeight;\n                    const pairTop = lastMessagePair.offsetTop;\n                    if (isTabletLayout) {\n                        // Tablet-specific scrolling: Keep messages visible by scrolling to bottom of container\n                        // This ensures both user message and AI response remain visible\n                        const scrollPosition = Math.max(0, pairTop + pairHeight - containerHeight + 100);\n                        container.scrollTo({\n                            top: scrollPosition,\n                            behavior: \"smooth\"\n                        });\n                    } else {\n                        // Desktop and mobile: Center the message pair on screen\n                        const scrollPosition = pairTop - (containerHeight - pairHeight) / 2;\n                        container.scrollTo({\n                            top: Math.max(0, scrollPosition),\n                            behavior: \"smooth\"\n                        });\n                    }\n                }\n            }\n        }, 150);\n    };\n    const renderMessagePairs = ()=>{\n        // Use the enhanced message pairing logic\n        const messagePairs = createMessagePairs(messages);\n        return messagePairs.map((pair, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"message-pair flex flex-col justify-start\",\n                \"data-pair-id\": pair.id,\n                \"data-is-complete\": pair.isComplete,\n                \"data-is-pending\": pair.isPending,\n                style: {\n                    minHeight: i === messagePairs.length - 1 ? isMobile ? \"calc(100vh - 200px)\" // Mobile-specific height for newest message\n                     : \"calc(100vh - 200px)\" // Desktop/tablet height for newest message\n                     : \"\",\n                    paddingTop: i === 0 ? \"1rem\" : \"1rem\",\n                    paddingBottom: \"0rem\"\n                },\n                children: [\n                    pair.user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-end mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-[16px] font-[400] rounded-3xl max-w-xs lg:max-w-lg break-words hyphens-auto bg-gray-100 \".concat(isMobile ? \"rounded-[15px] px-3 py-2\" : \"px-4 py-3\"),\n                            \"data-message-id\": pair.user.id,\n                            \"data-query-id\": pair.user.queryId,\n                            style: {\n                                overflowWrap: \"break-word\",\n                                wordBreak: \"break-word\",\n                                maxWidth: isMobile ? \"90%\" : undefined\n                            },\n                            children: [\n                                pair.user.text,\n                                pair.user.isSuggestion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-500 ml-2\",\n                                    children: \"\\uD83D\\uDCA1\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 634,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 620,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 619,\n                        columnNumber: 11\n                    }, undefined),\n                    pair.bot && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-start\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-[16px] font-[400] rounded-3xl max-w-xs lg:max-w-lg break-words hyphens-auto bg-white \".concat(isMobile ? \"rounded-[15px] px-3 py-2\" : \"px-4 py-3\", \" \").concat(pair.bot.isError ? \"border-red-200 bg-red-50\" : \"\"),\n                            \"data-message-id\": pair.bot.id,\n                            \"data-query-id\": pair.bot.queryId,\n                            \"data-response-to\": pair.bot.responseToId,\n                            style: {\n                                overflowWrap: \"break-word\",\n                                wordBreak: \"break-word\",\n                                maxWidth: isMobile ? \"90%\" : undefined\n                            },\n                            children: [\n                                pair.bot.text,\n                                pair.bot.isError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-red-500 ml-2\",\n                                    children: \"⚠️\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 658,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 643,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 642,\n                        columnNumber: 11\n                    }, undefined),\n                    i === messagePairs.length - 1 && (isBotTyping || isBotThinking) && !pair.bot && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-start\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white \".concat(isMobile ? \"px-3 py-2 rounded-[15px]\" : \"px-4 py-3 rounded-3xl\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-black rounded-full loading-dot\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 677,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-black rounded-full loading-dot\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 678,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-black rounded-full loading-dot\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 679,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 676,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 669,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 668,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, \"pair-\".concat(pair.id), true, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 600,\n                columnNumber: 7\n            }, undefined));\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSubmit(e);\n        }\n    };\n    const handleSuggestionClick = async (cardTitle)=>{\n        const isFirstMsg = messages.length === 0;\n        // Generate unique ID for this suggestion query\n        const queryId = generateMessageId();\n        const userMessageObj = {\n            id: queryId,\n            queryId: queryId,\n            text: cardTitle,\n            timestamp: Date.now(),\n            type: \"TEXT\",\n            source: \"USER\",\n            isSuggestion: true\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessageObj\n            ]);\n        // Track this suggestion query as pending response\n        setPendingQueries((prev)=>new Map(prev).set(queryId, {\n                id: queryId,\n                message: cardTitle,\n                timestamp: userMessageObj.timestamp,\n                sentAt: Date.now(),\n                isSuggestion: true\n            }));\n        setMessage(\"\");\n        setIsTyping(false);\n        // For first message, set flag to scroll to top\n        if (isFirstMsg) {\n            setShouldScrollToTop(true);\n            setIsFirstMessage(false);\n        }\n        // Show loading dots immediately after sending message\n        setIsBotThinking(true);\n        setIsBotTyping(false);\n        if (inputRef.current) {\n            inputRef.current.style.height = \"104px\";\n            inputRef.current.scrollTop = 0;\n            inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n            inputRef.current.classList.add(\"reset-height\");\n        }\n        try {\n            const userId = localStorage.getItem(\"userID\");\n            if (userId) {\n                await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_SEND, {\n                    userId: userId,\n                    message: cardTitle,\n                    queryId: queryId,\n                    customerName: username,\n                    isSuggestion: true\n                });\n            }\n        } catch (error) {\n            // Hide loading dots on error\n            setIsBotThinking(false);\n            setIsBotTyping(false);\n            const errorMessage = {\n                id: generateMessageId(),\n                text: \"Sorry, there was an error sending your message. Please try again.\",\n                timestamp: Date.now(),\n                type: \"TEXT\",\n                source: \"BOT\",\n                queryId: queryId,\n                isError: true\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n            // Remove from pending queries since we got an error\n            setPendingQueries((prev)=>{\n                const updated = new Map(prev);\n                updated.delete(queryId);\n                return updated;\n            });\n        }\n    };\n    const getMaxSlides = ()=>{\n        if (isMobile) return settingData === null || settingData === void 0 ? void 0 : settingData.suggestedTopics.length;\n        return Math.ceil((settingData === null || settingData === void 0 ? void 0 : settingData.suggestedTopics.length) / 2);\n    };\n    const nextSlide = ()=>{\n        var _settingData_suggestedTopics;\n        const totalCards = (settingData === null || settingData === void 0 ? void 0 : (_settingData_suggestedTopics = settingData.suggestedTopics) === null || _settingData_suggestedTopics === void 0 ? void 0 : _settingData_suggestedTopics.length) || 0;\n        if (totalCards === 0) return;\n        // Calculate scroll distance to ensure last card is fully visible\n        // Reduce scroll distance to show partial cards and ensure last card visibility\n        const scrollStep = isMobile ? 0.7 : 0.8; // Scroll by 70% on mobile, 80% on desktop\n        const maxSlide = Math.max(0, totalCards - 1); // Allow scrolling to show last card fully\n        setCurrentSlide((prev)=>Math.min(prev + scrollStep, maxSlide));\n    };\n    const prevSlide = ()=>{\n        const scrollStep = isMobile ? 0.7 : 0.8; // Same scroll step for consistency\n        setCurrentSlide((prev)=>Math.max(prev - scrollStep, 0));\n    };\n    // Check if we're at the boundaries for button states\n    const isAtStart = currentSlide === 0;\n    const isAtEnd = ()=>{\n        var _settingData_suggestedTopics;\n        const totalCards = (settingData === null || settingData === void 0 ? void 0 : (_settingData_suggestedTopics = settingData.suggestedTopics) === null || _settingData_suggestedTopics === void 0 ? void 0 : _settingData_suggestedTopics.length) || 0;\n        const maxSlide = Math.max(0, totalCards - 1);\n        return currentSlide >= maxSlide;\n    };\n    const handleNextSlide = ()=>{\n        nextSlide();\n    };\n    const handlePrevSlide = ()=>{\n        prevSlide();\n    };\n    const handleTouchStart = (e)=>{\n        setTouchStart(e.targetTouches[0].clientX);\n    };\n    const handleTouchMove = (e)=>{\n        setTouchEnd(e.targetTouches[0].clientX);\n    };\n    const handleTouchEnd = ()=>{\n        if (!touchStart || !touchEnd) return;\n        const swipeDistance = touchStart - touchEnd;\n        const minSwipeDistance = 50;\n        if (swipeDistance > minSwipeDistance) {\n            nextSlide();\n        }\n        if (swipeDistance < -minSwipeDistance) {\n            prevSlide();\n        }\n        setTouchStart(0);\n        setTouchEnd(0);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length === 0) {\n                setShowInitialUI(false);\n                const timer = setTimeout({\n                    \"ChatInterface.useEffect.timer\": ()=>setShowInitialUI(true)\n                }[\"ChatInterface.useEffect.timer\"], 60);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                })[\"ChatInterface.useEffect\"];\n            } else {\n                setShowInitialUI(false);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages.length\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const checkScreenSize = {\n                \"ChatInterface.useEffect.checkScreenSize\": ()=>{\n                    if (true) {\n                        const width = window.innerWidth;\n                        setIsMobile(width < 768);\n                        setIsTablet(width >= 768 && width < 1024);\n                    }\n                }\n            }[\"ChatInterface.useEffect.checkScreenSize\"];\n            checkScreenSize();\n            const handleResize = {\n                \"ChatInterface.useEffect.handleResize\": ()=>{\n                    setCurrentSlide(0);\n                    checkScreenSize();\n                }\n            }[\"ChatInterface.useEffect.handleResize\"];\n            if (true) {\n                window.addEventListener(\"resize\", handleResize);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>window.removeEventListener(\"resize\", handleResize)\n                })[\"ChatInterface.useEffect\"];\n            }\n        }\n    }[\"ChatInterface.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (inputRef.current && messages.length === 0) {\n                const shouldAutoFocus = window.innerWidth >= 768;\n                if (shouldAutoFocus) {\n                    const timer = setTimeout({\n                        \"ChatInterface.useEffect.timer\": ()=>{\n                            var _inputRef_current;\n                            (_inputRef_current = inputRef.current) === null || _inputRef_current === void 0 ? void 0 : _inputRef_current.focus();\n                        }\n                    }[\"ChatInterface.useEffect.timer\"], 100);\n                    return ({\n                        \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                    })[\"ChatInterface.useEffect\"];\n                }\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages.length\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            return ({\n                \"ChatInterface.useEffect\": ()=>{\n                    if (sseConnection) {\n                        sseConnection.close();\n                    }\n                }\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], [\n        sseConnection\n    ]);\n    // Check if settingData has been populated with actual data\n    const hasSettingData = settingData && Object.keys(settingData).some((key)=>settingData[key] !== null);\n    // Show loading state while waiting for data\n    if (!hasSettingData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white flex flex-col min-h-screen items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 912,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading chat settings...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 913,\n                        columnNumber: 11\n                    }, undefined),\n                    contextError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-500 mt-2\",\n                        children: [\n                            \"Error: \",\n                            contextError\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 915,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 911,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n            lineNumber: 910,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"bg-white flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"hidden lg:flex flex-1 flex-col px-4 \",\n                children: messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex flex-1 items-center justify-center min-h-[calc(100vh-64px)] mt-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex flex-col items-center w-[768px] justify-center mx-auto \",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    transitionDelay: showInitialUI ? \"40ms\" : \"0ms\"\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-4xl text-gray-900 mb-6 text-center transition-all duration-700 \".concat(showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                children: \"How can I help you?\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 929,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                style: {\n                                    transitionDelay: showInitialUI ? \"120ms\" : \"0ms\"\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"relative w-full mb-6 flex flex-col items-center transition-all duration-500 \".concat(showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        ref: messages.length === 0 ? inputRef : null,\n                                        value: message,\n                                        onChange: handleInputChange,\n                                        onKeyDown: handleKeyPress,\n                                        placeholder: \"Ask anything\",\n                                        autoFocus: messages.length === 0,\n                                        rows: 1,\n                                        style: {\n                                            boxSizing: \"border-box\",\n                                            zIndex: 1001,\n                                            position: \"relative\"\n                                        },\n                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"pt-3 px-4 lg:p-4 pb-16 pr-12 w-full min-h-[104px] max-h-[250px] overflow-y-auto hide-scrollbar text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base shadow-md resize-none reset-height\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 949,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: message.trim().length === 0,\n                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"absolute right-3 bottom-3 w-9 h-9 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation \".concat(message.trim().length === 0 ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                            className: \"w-4 h-4 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 973,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 964,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 940,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    transitionDelay: showInitialUI ? \"200ms\" : \"0ms\",\n                                    zIndex: 10\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"relative w-full max-w-2xl transition-all duration-500 ease-in-out \".concat(isTyping ? \"opacity-0 pointer-events-none\" : showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            paddingRight: '100px'\n                                        },\n                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"overflow-hidden px-12\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: carouselRef,\n                                            style: {\n                                                transform: \"translateX(-\".concat(currentSlide * (isMobile ? 200 : 250), \"px)\"),\n                                                paddingLeft: \"0\"\n                                            },\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex gap-2 transition-transform duration-300 ease-in-out justify-start\",\n                                            children: settingData === null || settingData === void 0 ? void 0 : (_settingData_suggestedTopics1 = settingData.suggestedTopics) === null || _settingData_suggestedTopics1 === void 0 ? void 0 : _settingData_suggestedTopics1.map((card, cardIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleSuggestionClick(card === null || card === void 0 ? void 0 : card.question),\n                                                    style: {\n                                                        minWidth: \"fit-content\",\n                                                        maxWidth: \"300px\",\n                                                        width: \"auto\"\n                                                    },\n                                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"py-2 px-3 rounded-[8px] bg-[#f6f6f6] text-left group transition-all duration-200 touch-manipulation flex-shrink-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-[16px] font-[600] text-black mb-0.5 leading-tight\",\n                                                            children: card.question\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 1011,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-[16px] text-gray-500 leading-tight\",\n                                                            children: card.subtitle\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 1014,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, cardIndex, true, {\n                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                    lineNumber: 999,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 990,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 989,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handlePrevSlide,\n                                        disabled: isAtStart,\n                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-10 w-8 h-8 border rounded-full flex items-center justify-center shadow-md transition-all duration-200 touch-manipulation z-10 \".concat(isAtStart ? 'bg-gray-100 border-gray-300 cursor-not-allowed' : 'bg-white border-gray-200 hover:bg-gray-50 cursor-pointer'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronLeft, {\n                                            className: \"w-3 h-3 \".concat(isAtStart ? 'text-gray-400' : 'text-gray-600')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1030,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 1021,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleNextSlide,\n                                        disabled: isAtEnd(),\n                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-10 w-8 h-8 border rounded-full flex items-center justify-center shadow-md transition-all duration-200 touch-manipulation z-10 \".concat(isAtEnd() ? 'bg-gray-100 border-gray-300 cursor-not-allowed' : 'bg-white border-gray-200 hover:bg-gray-50 cursor-pointer'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronRight, {\n                                            className: \"w-3 h-3 \".concat(isAtEnd() ? 'text-gray-400' : 'text-gray-600')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1041,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 1032,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 976,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 928,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                    lineNumber: 927,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: desktopMessagesContainerRef,\n                            style: {\n                                overscrollBehavior: \"contain\"\n                            },\n                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex-1 overflow-y-auto max-h-[calc(100vh-200px)] mt-14\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"w-full lg:w-[768px] mx-auto px-4 h-fit \",\n                                children: [\n                                    renderMessagePairs(),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: messagesEndRef,\n                                        className: \"jsx-69ac67aa0147cd65\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 1055,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 1053,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 1048,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                zIndex: 1000,\n                                marginTop: \"20px\"\n                            },\n                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"fixed bottom-0 left-0 right-0 p-4 mt-5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSubmit,\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"relative w-full lg:w-[768px] mx-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            ref: messages.length > 0 ? inputRef : null,\n                                            value: message,\n                                            onChange: handleInputChange,\n                                            onKeyDown: handleKeyPress,\n                                            placeholder: \"Ask anything\",\n                                            rows: 1,\n                                            style: {\n                                                boxSizing: \"border-box\",\n                                                zIndex: 1001,\n                                                position: \"relative\"\n                                            },\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"pt-3 px-4 md:px-5 pb-10 pr-12 w-full min-h-[104px] max-h-[250px] overflow-y-auto text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base shadow-lg resize-none reset-height hide-scrollbar\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1067,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: message.trim().length === 0,\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation \".concat(message.trim().length === 0 ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                                className: \"w-4 h-4 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1090,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1081,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1063,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-center mt-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-xs text-gray-500\",\n                                        children: [\n                                            \"This chat is powered by\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-black\",\n                                                children: \"Driply.me\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1096,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 1094,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1093,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 1059,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 925,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"lg:hidden overflow-hidden fixed inset-0 flex flex-col  mt-10 \",\n                children: [\n                    messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex flex-col items-center pt-15 pb-4 px-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"hidden md:flex lg:hidden flex-col items-center\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1109,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 1108,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    paddingBottom: \"260px\"\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex flex-col items-center justify-center flex-1 px-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"40ms\" : \"0ms\"\n                                    },\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-2xl md:text-4xl text-gray-900 text-center leading-relaxed transition-all duration-700 \".concat(showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                    children: \"How can I help you?\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1115,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 1111,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: mobileMessagesContainerRef,\n                                style: {\n                                    overscrollBehavior: \"contain\"\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"md:hidden flex-1 overflow-y-auto max-h-[calc(100vh-190px)] hide-scrollbar\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"w-full max-w-[803px] mx-auto px-4  pt-12\",\n                                    children: [\n                                        renderMessagePairs(),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: messagesEndRef,\n                                            className: \"jsx-69ac67aa0147cd65\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1137,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1135,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 1130,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: tabletMessagesContainerRef,\n                                style: {\n                                    overscrollBehavior: \"contain\"\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"hidden md:block lg:hidden flex-1 overflow-y-auto max-h-[calc(100vh-100px)]  pb-20 hide-scrollbar\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"w-full max-w-[803px] mx-auto px-4 pb-6 pt-12\",\n                                    children: [\n                                        renderMessagePairs(),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: messagesEndRef,\n                                            className: \"jsx-69ac67aa0147cd65\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1149,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1147,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 1142,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    minHeight: \"160px\",\n                                    zIndex: 1000,\n                                    paddingBottom: \"env(safe-area-inset-bottom, 0)\",\n                                    transform: \"translateZ(0)\",\n                                    backfaceVisibility: \"hidden\"\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"fixed bottom-0 left-0 right-0 bg-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        transform: \"translateZ(0)\"\n                                    },\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"px-4 py-4 bg-white\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            style: {\n                                                zIndex: 1001\n                                            },\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"relative bg-white\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    transform: \"translateZ(0)\"\n                                                },\n                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"relative w-full max-w-[890px] mx-auto bg-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        ref: messages.length > 0 ? inputRef : null,\n                                                        value: message,\n                                                        onChange: handleInputChange,\n                                                        onKeyDown: handleKeyPress,\n                                                        placeholder: \"Ask anything\",\n                                                        rows: 1,\n                                                        style: {\n                                                            boxSizing: \"border-box\",\n                                                            maxHeight: \"250px\",\n                                                            zIndex: 1001,\n                                                            overscrollBehavior: \"none\",\n                                                            position: \"relative\"\n                                                        },\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"w-full min-h-[104px] pt-3 px-4 pb-10 pr-12 text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base resize-none overflow-y-auto reset-height\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1176,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        disabled: message.trim().length === 0,\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation \".concat(message.trim().length === 0 ? \"bg-[#E0E0E0] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                                            className: \"w-4 h-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 1201,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1192,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1172,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1167,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-center py-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"This chat is powered by\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-black\",\n                                                        children: \"Driply.me\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1208,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1206,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1205,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1163,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 1153,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"absolute bottom-0 left-0 right-0 bg-white\",\n                        children: messages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"120ms\" : \"0ms\",\n                                        position: \"relative\",\n                                        zIndex: 10,\n                                        backgroundColor: \"white\",\n                                        paddingBottom: \"10px\"\n                                    },\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"\".concat(isMobile ? \"px-0\" : \"px-4\", \" pt-2 pb-2 transition-all duration-500 ease-in-out \").concat(isTyping ? \"opacity-0 pointer-events-none\" : showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"overflow-hidden \".concat(isMobile ? 'px-4' : 'px-12'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                ref: carouselRef,\n                                                style: {\n                                                    transform: \"translateX(-\".concat(currentSlide * (isMobile ? 150 : 200), \"px)\"),\n                                                    paddingLeft: \"0\"\n                                                },\n                                                onTouchStart: handleTouchStart,\n                                                onTouchMove: handleTouchMove,\n                                                onTouchEnd: handleTouchEnd,\n                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex gap-2 transition-transform duration-300 ease-in-out justify-start\",\n                                                children: settingData === null || settingData === void 0 ? void 0 : (_settingData_suggestedTopics2 = settingData.suggestedTopics) === null || _settingData_suggestedTopics2 === void 0 ? void 0 : _settingData_suggestedTopics2.map((card, cardIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex-shrink-0 \".concat(isMobile ? 'mt-3 mr-2' : 'mr-2'),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleSuggestionClick(card.question),\n                                                            style: {\n                                                                minWidth: \"fit-content\",\n                                                                maxWidth: isMobile ? \"280px\" : \"300px\",\n                                                                width: \"auto\"\n                                                            },\n                                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"\".concat(isMobile ? 'py-3' : 'py-2', \" px-2.5 rounded-[8px] bg-[#F6F6F6] text-left hover:bg-[#D6EDFF] transition-all duration-200 inline-block touch-manipulation\"),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"\".concat(isMobile ? 'text-[16px]' : 'text-[14px]', \" font-[600] text-black mb-0.5 leading-tight\"),\n                                                                    children: card.question\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                    lineNumber: 1267,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-[16px] text-gray-500 leading-tight\",\n                                                                    children: card.subtitle\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                    lineNumber: 1270,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 1256,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, cardIndex, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1252,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1240,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1239,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handlePrevSlide,\n                                            disabled: isAtStart,\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"hidden md:flex absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-10 w-8 h-8 border rounded-full items-center justify-center shadow-md transition-all duration-200 touch-manipulation z-10 \".concat(isAtStart ? 'bg-gray-100 border-gray-300 cursor-not-allowed' : 'bg-white border-gray-200 hover:bg-gray-50 cursor-pointer'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronLeft, {\n                                                className: \"w-3 h-3 \".concat(isAtStart ? 'text-gray-400' : 'text-gray-600')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1289,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1280,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleNextSlide,\n                                            disabled: isAtEnd(),\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"hidden md:flex absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-10 w-8 h-8 border rounded-full items-center justify-center shadow-md transition-all duration-200 touch-manipulation z-10 \".concat(isAtEnd() ? 'bg-gray-100 border-gray-300 cursor-not-allowed' : 'bg-white border-gray-200 hover:bg-gray-50 cursor-pointer'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronRight, {\n                                                className: \"w-3 h-3 \".concat(isAtEnd() ? 'text-gray-400' : 'text-gray-600')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1300,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1291,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1221,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"200ms\" : \"0ms\",\n                                        position: \"relative\",\n                                        zIndex: 5,\n                                        paddingBottom: \"env(safe-area-inset-bottom)\"\n                                    },\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"px-4 bg-white transition-all duration-500 \".concat(showInitialUI ? \"opacity-100 translate-y-0\" : \"translate-y-4\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"relative\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"relative w-full max-w-[890px] mx-auto\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        ref: messages.length === 0 ? inputRef : null,\n                                                        value: message,\n                                                        onChange: handleInputChange,\n                                                        onKeyDown: handleKeyPress,\n                                                        placeholder: \"Ask anything\",\n                                                        rows: 1,\n                                                        style: {\n                                                            boxSizing: \"border-box\",\n                                                            maxHeight: \"250px\",\n                                                            fontSize: \"16px\",\n                                                            zIndex: 1001,\n                                                            overscrollBehaviorY: \"contain\",\n                                                            position: \"relative\"\n                                                        },\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"w-full min-h-[104px] pt-3 px-4 pb-10 pr-12 text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base resize-none overflow-y-auto reset-height\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1318,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        disabled: message.trim().length === 0,\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation \".concat(message.trim().length === 0 ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                                            className: \"w-4 h-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 1344,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1335,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1317,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1316,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-center py-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"This chat is powered by\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-black\",\n                                                        children: \"Driply.me\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1351,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1349,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1348,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1305,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 1217,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 1105,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"69ac67aa0147cd65\",\n                children: '.loading-dot{-webkit-animation:loading-dots 1.4s infinite ease-in-out both;-moz-animation:loading-dots 1.4s infinite ease-in-out both;-o-animation:loading-dots 1.4s infinite ease-in-out both;animation:loading-dots 1.4s infinite ease-in-out both}.loading-dot:nth-child(1){-webkit-animation-delay:-.32s;-moz-animation-delay:-.32s;-o-animation-delay:-.32s;animation-delay:-.32s}.loading-dot:nth-child(2){-webkit-animation-delay:-.16s;-moz-animation-delay:-.16s;-o-animation-delay:-.16s;animation-delay:-.16s}.loading-dot:nth-child(3){-webkit-animation-delay:0s;-moz-animation-delay:0s;-o-animation-delay:0s;animation-delay:0s}@-webkit-keyframes loading-dots{0%,80%,100%{-webkit-transform:scale(1);transform:scale(1);opacity:1}40%{-webkit-transform:scale(1.5);transform:scale(1.5);opacity:.7}}@-moz-keyframes loading-dots{0%,80%,100%{-moz-transform:scale(1);transform:scale(1);opacity:1}40%{-moz-transform:scale(1.5);transform:scale(1.5);opacity:.7}}@-o-keyframes loading-dots{0%,80%,100%{-o-transform:scale(1);transform:scale(1);opacity:1}40%{-o-transform:scale(1.5);transform:scale(1.5);opacity:.7}}@keyframes loading-dots{0%,80%,100%{-webkit-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1);opacity:1}40%{-webkit-transform:scale(1.5);-moz-transform:scale(1.5);-o-transform:scale(1.5);transform:scale(1.5);opacity:.7}}.hide-scrollbar{-ms-overflow-style:none!important;scrollbar-width:none!important}.hide-scrollbar::-webkit-scrollbar{display:none!important}@media only screen and (max-width:1023px){.lg\\\\\\\\.jsx-69ac67aa0147cd65:hidden .absolute.bottom-0.jsx-69ac67aa0147cd65{position:fixed!important;bottom:env(safe-area-inset-bottom,0);left:0;right:0;width:100%;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;z-index:1000;background-color:white}textarea.jsx-69ac67aa0147cd65{-webkit-user-select:auto!important;-moz-user-select:auto!important;-ms-user-select:auto!important;user-select:auto!important;-webkit-appearance:none;-moz-appearance:none;-ms-appearance:none;appearance:none;-ms-scroll-chaining:none;overscroll-behavior:none;line-height:24px;max-height:250px!important;position:relative!important;-webkit-transform:translatez(0);-moz-transform:translatez(0);-ms-transform:translatez(0);-o-transform:translatez(0);transform:translatez(0);-webkit-backface-visibility:hidden;-moz-backface-visibility:hidden;backface-visibility:hidden;-webkit-perspective:1000;-moz-perspective:1000;perspective:1000;z-index:1001!important;background-color:white}.lg\\\\\\\\.jsx-69ac67aa0147cd65:hidden form.jsx-69ac67aa0147cd65{position:relative!important;z-index:1001;background-color:white}button[type=\"submit\"].jsx-69ac67aa0147cd65{position:absolute!important;right:12px;bottom:12px;z-index:1002!important;-webkit-tap-highlight-color:transparent;-ms-touch-action:-ms-manipulation;-ms-touch-action:manipulation;touch-action:manipulation;-webkit-transform:translatez(0);-moz-transform:translatez(0);-ms-transform:translatez(0);-o-transform:translatez(0);transform:translatez(0)}.lg\\\\\\\\.jsx-69ac67aa0147cd65:hidden .bg-white.jsx-69ac67aa0147cd65{background-color:white!important}}@media only screen and (min-width:1024px){textarea.jsx-69ac67aa0147cd65{max-height:250px!important;overflow-y:auto!important;-ms-scroll-chaining:none;overscroll-behavior:contain}.reset-height.jsx-69ac67aa0147cd65{min-height:104px!important;max-height:250px!important;overflow-y:auto!important}}.fixed-bottom.jsx-69ac67aa0147cd65{position:fixed!important;bottom:0;left:0;right:0;background:white}'\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n        lineNumber: 923,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatInterface, \"11I1ZIMf13g4EsXvUwg83BGQ4a8=\", false, function() {\n    return [\n        _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__.useChatContext\n    ];\n});\n_c = ChatInterface;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatInterface);\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ChatInterface.jsx\n"));

/***/ })

});