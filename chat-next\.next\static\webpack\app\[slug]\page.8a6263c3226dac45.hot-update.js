"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[slug]/page",{

/***/ "(app-pages-browser)/./src/components/ChatInterface.jsx":
/*!******************************************!*\
  !*** ./src/components/ChatInterface.jsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/config */ \"(app-pages-browser)/./src/utils/config.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/ChatContext */ \"(app-pages-browser)/./src/contexts/ChatContext.jsx\");\n/* harmony import */ var _hooks_useCarousel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../hooks/useCarousel */ \"(app-pages-browser)/./src/hooks/useCarousel.js\");\n/* harmony import */ var _hooks_useDeviceDetection__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../hooks/useDeviceDetection */ \"(app-pages-browser)/./src/hooks/useDeviceDetection.js\");\n/* harmony import */ var _hooks_useMessagePairing__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../hooks/useMessagePairing */ \"(app-pages-browser)/./src/hooks/useMessagePairing.js\");\n/* harmony import */ var _MessagePairs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./MessagePairs */ \"(app-pages-browser)/./src/components/MessagePairs.jsx\");\n/* harmony import */ var _CarouselSection__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./CarouselSection */ \"(app-pages-browser)/./src/components/CarouselSection.jsx\");\n/* harmony import */ var _InputSection__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./InputSection */ \"(app-pages-browser)/./src/components/InputSection.jsx\");\n/* harmony import */ var _LoadingIndicators__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./LoadingIndicators */ \"(app-pages-browser)/./src/components/LoadingIndicators.jsx\");\n/* harmony import */ var _styles_ChatInterface_css__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../styles/ChatInterface.css */ \"(app-pages-browser)/./src/styles/ChatInterface.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// Import custom hooks\n\n\n\n// Import components\n\n\n\n\n// Import styles\n\nconst ChatInterface = (param)=>{\n    let { slug, query } = param;\n    var _settingData_suggestedTopics;\n    _s();\n    // Context API\n    const { metadata, updateMetadata, setLoadingState, setErrorState, clearError, settingData, updateSettingData, getCustomerName, getBusinessName, hasMetadata, loading: contextLoading, error: contextError } = (0,_contexts_ChatContext__WEBPACK_IMPORTED_MODULE_3__.useChatContext)();\n    // Custom hooks\n    const carousel = (0,_hooks_useCarousel__WEBPACK_IMPORTED_MODULE_4__.useCarousel)(settingData);\n    const { isMobile, isTablet } = (0,_hooks_useDeviceDetection__WEBPACK_IMPORTED_MODULE_5__.useDeviceDetection)(carousel.resetSlide);\n    const messagePairing = (0,_hooks_useMessagePairing__WEBPACK_IMPORTED_MODULE_6__.useMessagePairing)();\n    // Local state\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showInitialUI, setShowInitialUI] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isBotTyping, setIsBotTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isBotThinking, setIsBotThinking] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sseConnection, setSseConnection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isFirstMessage, setIsFirstMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [shouldScrollToTop, setShouldScrollToTop] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Refs\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const carouselRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const desktopMessagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const mobileMessagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const tabletMessagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Debug: Log state updates when they actually happen\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (metadata && Object.keys(metadata).some({\n                \"ChatInterface.useEffect\": (key)=>metadata[key] !== null\n            }[\"ChatInterface.useEffect\"])) {\n                console.log(\"✅ METADATA STATE UPDATED:\", metadata);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        metadata\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (settingData && Object.keys(settingData).some({\n                \"ChatInterface.useEffect\": (key)=>settingData[key] !== null\n            }[\"ChatInterface.useEffect\"])) {\n                console.log(\"✅ SETTING DATA STATE UPDATED:\", settingData);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        settingData\n    ]);\n    // Cleanup pending queries that are too old (timeout mechanism)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const cleanup = setInterval({\n                \"ChatInterface.useEffect.cleanup\": ()=>{\n                    const now = Date.now();\n                    const TIMEOUT_MS = 60000; // 60 seconds timeout\n                    setPendingQueries({\n                        \"ChatInterface.useEffect.cleanup\": (prev)=>{\n                            const updated = new Map(prev);\n                            let hasChanges = false;\n                            for (const [queryId, queryData] of prev.entries()){\n                                if (now - queryData.sentAt > TIMEOUT_MS) {\n                                    updated.delete(queryId);\n                                    hasChanges = true;\n                                }\n                            }\n                            return hasChanges ? updated : prev;\n                        }\n                    }[\"ChatInterface.useEffect.cleanup\"]);\n                }\n            }[\"ChatInterface.useEffect.cleanup\"], 10000); // Check every 10 seconds\n            return ({\n                \"ChatInterface.useEffect\": ()=>clearInterval(cleanup)\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], []);\n    // Ensure carousel starts from first card on initial load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            var _settingData_suggestedTopics;\n            if ((settingData === null || settingData === void 0 ? void 0 : (_settingData_suggestedTopics = settingData.suggestedTopics) === null || _settingData_suggestedTopics === void 0 ? void 0 : _settingData_suggestedTopics.length) > 0) {\n                carousel.resetSlide(); // Always start from first card\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        settingData === null || settingData === void 0 ? void 0 : (_settingData_suggestedTopics = settingData.suggestedTopics) === null || _settingData_suggestedTopics === void 0 ? void 0 : _settingData_suggestedTopics.length,\n        carousel\n    ]);\n    // Detect iOS for targeted fixes\n    const isIOS = ()=>{\n        if (true) {\n            return /iPhone|iPad|iPod/i.test(navigator.userAgent);\n        }\n        return false;\n    };\n    const username = slug;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (username && \"object\" !== \"undefined\") {\n                fetchCustomer(username);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        username\n    ]);\n    const fetchCustomer = async (customerName)=>{\n        try {\n            setLoadingState(true);\n            clearError();\n            const response = await axios__WEBPACK_IMPORTED_MODULE_12__[\"default\"].get(\"\".concat(_utils_config__WEBPACK_IMPORTED_MODULE_2__.API_ENDPOINTS.CHAT_SETTINGS, \"?customerName=\").concat(customerName));\n            // Extract data from axios response\n            const responseData = response.data;\n            const metadataFromResponse = responseData.metaData;\n            updateSettingData(responseData);\n            updateMetadata(metadataFromResponse);\n            if (responseData && responseData.customerName) {\n                //  localStorage for backward compatibility\n                localStorage.setItem(\"customerName_userId\", responseData.customerName);\n                localStorage.setItem(\"BusinessName\", responseData.businessName);\n                window.dispatchEvent(new CustomEvent(\"businessNameLoaded\", {\n                    detail: {\n                        businessName: responseData.businessName\n                    }\n                }));\n                const existingUserId = localStorage.getItem(\"userID\");\n                if (existingUserId) {\n                    await fetchExistingMessages(existingUserId);\n                } else {\n                    await initializeChatSession();\n                }\n            } else {\n                await initializeChatSession();\n            }\n        } catch (error) {\n            console.error(\"Error fetching customer data:\", error);\n            setErrorState(\"Failed to load customer settings\");\n            // Fallback metadata\n            updateMetadata({\n                customerName: customerName,\n                businessName: \"Driply\"\n            });\n            localStorage.setItem(\"BusinessName\", \"Driply\");\n            window.dispatchEvent(new CustomEvent(\"businessNameLoaded\", {\n                detail: {\n                    businessName: \"Driply\"\n                }\n            }));\n            await initializeChatSession();\n        } finally{\n            setLoadingState(false);\n        }\n    };\n    const fetchExistingMessages = async (userId)=>{\n        // console.log(\"fetchExistingMessages\");\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_12__[\"default\"].get(\"\".concat(_utils_config__WEBPACK_IMPORTED_MODULE_2__.API_ENDPOINTS.CHAT_MESSAGES, \"?userId=\").concat(userId));\n            if (response.data && response.data.length > 0) {\n                // Load existing messages into the message pairing system\n                response.data.forEach((msg)=>{\n                    messagePairing.addMessage({\n                        text: msg.message,\n                        timestamp: new Date(msg.createdAt).getTime(),\n                        type: msg.type,\n                        source: msg.source\n                    });\n                });\n            }\n            connectToSSE(userId);\n        } catch (error) {\n            await initializeChatSession();\n        }\n    };\n    const initializeChatSession = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_12__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_2__.API_ENDPOINTS.CHAT_INIT, {\n                customerName: username\n            });\n            if (response.data && response.data.userId) {\n                localStorage.setItem(\"userId\", response.data.userId);\n                localStorage.setItem(\"userID\", response.data._id);\n                await fetchExistingMessages(response.data._id);\n            }\n        } catch (error) {\n            if (!localStorage.getItem(\"BusinessName\")) {\n                localStorage.setItem(\"BusinessName\", \"Driply\");\n                window.dispatchEvent(new CustomEvent(\"businessNameLoaded\", {\n                    detail: {\n                        businessName: \"Driply\"\n                    }\n                }));\n            }\n        }\n    };\n    const connectToSSE = (userId)=>{\n        try {\n            if (sseConnection) {\n                sseConnection.close();\n            }\n            const sseUrl = (0,_utils_config__WEBPACK_IMPORTED_MODULE_2__.getSSEUrl)(userId);\n            const eventSource = new EventSource(sseUrl);\n            setSseConnection(eventSource);\n            eventSource.addEventListener(\"message\", (param)=>{\n                let { data } = param;\n                try {\n                    const contents = JSON.parse(data);\n                    handleSSEMessage(contents);\n                } catch (error) {\n                // Handle parsing error silently\n                }\n            });\n            eventSource.addEventListener(\"error\", ()=>{\n            // Handle connection error silently\n            });\n        } catch (error) {\n        // Handle SSE connection error silently\n        }\n    };\n    const handleSSEMessage = (data)=>{\n        const subtype = data.subType || data.subtype || data.type || \"UNKNOWN\";\n        const content = data.content || \"\";\n        const message = data.message || data.text || \"\";\n        if ([\n            \"TYPING\",\n            \"THINKING\",\n            \"BEHAVIOUR_MESSAGE\"\n        ].includes(subtype)) {\n            switch(subtype){\n                case \"TYPING\":\n                    setIsBotTyping(true);\n                    setIsBotThinking(false);\n                    break;\n                case \"THINKING\":\n                    setIsBotThinking(true);\n                    setIsBotTyping(false);\n                    break;\n                case \"BEHAVIOUR_MESSAGE\":\n                    setIsBotTyping(false);\n                    setIsBotThinking(false);\n                    break;\n            }\n            return;\n        }\n        const displayText = message || content;\n        if (!displayText) return;\n        const displayType = [\n            \"TEXT\",\n            \"MESSAGE\",\n            \"DATA_MESSAGE\"\n        ].includes(subtype) ? subtype : \"UNKNOWN\";\n        // Enhanced message pairing logic\n        const responseToId = data.queryId || data.responseToId || data.correlationId;\n        let queryId = responseToId;\n        // If no explicit queryId, find the most recent pending query\n        if (!queryId) {\n            const pendingEntries = Array.from(messagePairing.pendingQueries.entries()).sort((a, b)=>b[1].timestamp - a[1].timestamp);\n            if (pendingEntries.length > 0) {\n                queryId = pendingEntries[0][0];\n            }\n        }\n        // Create bot message with proper pairing information\n        const botMessage = messagePairing.addBotMessage(displayText, queryId, false);\n        // The messagePairing hook handles pending query cleanup automatically\n        setIsBotTyping(false);\n        setIsBotThinking(false);\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (message.trim()) {\n            const userMessage = message.trim();\n            const isFirstMsg = messages.length === 0;\n            // Generate unique ID for this query\n            const queryId = messagePairing.createMessageId();\n            const userMessageObj = messagePairing.addUserMessage(userMessage, queryId);\n            // The messagePairing hook handles message state automatically\n            // Track this query as pending response\n            setPendingQueries((prev)=>new Map(prev).set(queryId, {\n                    id: queryId,\n                    message: userMessage,\n                    timestamp: userMessageObj.timestamp,\n                    sentAt: Date.now()\n                }));\n            setMessage(\"\");\n            setIsTyping(false);\n            // For first message, set flag to scroll to top\n            if (isFirstMsg) {\n                setShouldScrollToTop(true);\n                setIsFirstMessage(false);\n            }\n            // Show loading dots immediately after sending message\n            setIsBotThinking(true);\n            setIsBotTyping(false);\n            // Reset textarea\n            if (inputRef.current) {\n                inputRef.current.style.height = \"104px\";\n                inputRef.current.scrollTop = 0;\n                inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n                inputRef.current.classList.add(\"reset-height\");\n            }\n            try {\n                const userId = localStorage.getItem(\"userID\");\n                if (userId) {\n                    await axios__WEBPACK_IMPORTED_MODULE_12__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_2__.API_ENDPOINTS.CHAT_SEND, {\n                        userId: userId,\n                        message: userMessage,\n                        queryId: queryId,\n                        typs: \"TEXT\",\n                        source: \"USER\",\n                        isTest: query.isTest === \"1\" ? true : false\n                    });\n                }\n            } catch (error) {\n                // Hide loading dots on error\n                setIsBotThinking(false);\n                setIsBotTyping(false);\n                const errorMessage = messagePairing.addBotMessage(\"Sorry, there was an error sending your message. Please try again.\", queryId, true // isError = true\n                );\n                // The messagePairing hook handles message state automatically\n                // Remove from pending queries since we got an error\n                setPendingQueries((prev)=>{\n                    const updated = new Map(prev);\n                    updated.delete(queryId);\n                    return updated;\n                });\n            }\n        }\n    };\n    // Input handling is now managed by InputSection component\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messagePairing.messages.length > 0 && inputRef.current && !message) {\n                const timer = setTimeout({\n                    \"ChatInterface.useEffect.timer\": ()=>{\n                        if (inputRef.current) {\n                            inputRef.current.style.height = \"104px\";\n                            inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n                            inputRef.current.classList.add(\"reset-height\");\n                        }\n                    }\n                }[\"ChatInterface.useEffect.timer\"], 50);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                })[\"ChatInterface.useEffect\"];\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messagePairing.messages,\n        message\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messagePairing.messages.length > 0) {\n                handleChatGPTScroll();\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messagePairing.messages,\n        isBotTyping,\n        isBotThinking\n    ]);\n    const handleChatGPTScroll = ()=>{\n        setTimeout(()=>{\n            // Determine screen type and get appropriate container\n            const screenWidth = window.innerWidth;\n            const isDesktopLayout = screenWidth >= 1024;\n            const isTabletLayout = screenWidth >= 768 && screenWidth < 1024;\n            let container;\n            if (isDesktopLayout) {\n                container = desktopMessagesContainerRef.current;\n            } else if (isTabletLayout) {\n                container = tabletMessagesContainerRef.current;\n            } else {\n                container = mobileMessagesContainerRef.current;\n            }\n            if (!container) return;\n            if (shouldScrollToTop) {\n                // For first message, scroll to top to show the message there\n                container.scrollTo({\n                    top: 0,\n                    behavior: \"smooth\"\n                });\n                setShouldScrollToTop(false);\n            } else {\n                // Different scrolling behavior for each screen type\n                const messageElements = container.querySelectorAll(\".message-pair\");\n                if (messageElements.length > 0) {\n                    const lastMessagePair = messageElements[messageElements.length - 1];\n                    const containerHeight = container.clientHeight;\n                    const pairHeight = lastMessagePair.offsetHeight;\n                    const pairTop = lastMessagePair.offsetTop;\n                    if (isTabletLayout) {\n                        // Tablet-specific scrolling: Keep messages visible by scrolling to bottom of container\n                        // This ensures both user message and AI response remain visible\n                        const scrollPosition = Math.max(0, pairTop + pairHeight - containerHeight + 100);\n                        container.scrollTo({\n                            top: scrollPosition,\n                            behavior: \"smooth\"\n                        });\n                    } else {\n                        // Desktop and mobile: Center the message pair on screen\n                        const scrollPosition = pairTop - (containerHeight - pairHeight) / 2;\n                        container.scrollTo({\n                            top: Math.max(0, scrollPosition),\n                            behavior: \"smooth\"\n                        });\n                    }\n                }\n            }\n        }, 150);\n    };\n    const renderMessagePairs = ()=>{\n        // Use the enhanced message pairing logic from custom hook\n        const messagePairs = messagePairing.getMessagePairs();\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MessagePairs__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            messagePairs: messagePairs,\n            isBotTyping: isBotTyping,\n            isBotThinking: isBotThinking,\n            isMobile: isMobile\n        }, void 0, false, {\n            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n            lineNumber: 483,\n            columnNumber: 7\n        }, undefined);\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSubmit(e);\n        }\n    };\n    const handleSuggestionClick = async (cardTitle)=>{\n        const isFirstMsg = messagePairing.messages.length === 0;\n        // Generate unique ID for this suggestion query\n        const queryId = messagePairing.createMessageId();\n        const userMessageObj = messagePairing.addUserMessage(cardTitle, queryId);\n        // Mark as suggestion\n        userMessageObj.isSuggestion = true;\n        // The messagePairing hook handles message state and pending queries automatically\n        setMessage(\"\");\n        setIsTyping(false);\n        // For first message, set flag to scroll to top\n        if (isFirstMsg) {\n            setShouldScrollToTop(true);\n            setIsFirstMessage(false);\n        }\n        // Show loading dots immediately after sending message\n        setIsBotThinking(true);\n        setIsBotTyping(false);\n        if (inputRef.current) {\n            inputRef.current.style.height = \"104px\";\n            inputRef.current.scrollTop = 0;\n            inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n            inputRef.current.classList.add(\"reset-height\");\n        }\n        try {\n            const userId = localStorage.getItem(\"userID\");\n            if (userId) {\n                await axios__WEBPACK_IMPORTED_MODULE_12__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_2__.API_ENDPOINTS.CHAT_SEND, {\n                    userId: userId,\n                    message: cardTitle,\n                    queryId: queryId,\n                    customerName: username,\n                    isSuggestion: true\n                });\n            }\n        } catch (error) {\n            // Hide loading dots on error\n            setIsBotThinking(false);\n            setIsBotTyping(false);\n            const errorMessage = messagePairing.addBotMessage(\"Sorry, there was an error sending your message. Please try again.\", queryId, true // isError = true\n            );\n        // The messagePairing hook handles message state and pending queries automatically\n        }\n    };\n    const getMaxSlides = ()=>{\n        if (isMobile) return settingData === null || settingData === void 0 ? void 0 : settingData.suggestedTopics.length;\n        return Math.ceil((settingData === null || settingData === void 0 ? void 0 : settingData.suggestedTopics.length) / 2);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messagePairing.messages.length === 0) {\n                setShowInitialUI(false);\n                const timer = setTimeout({\n                    \"ChatInterface.useEffect.timer\": ()=>setShowInitialUI(true)\n                }[\"ChatInterface.useEffect.timer\"], 60);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                })[\"ChatInterface.useEffect\"];\n            } else {\n                setShowInitialUI(false);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messagePairing.messages.length\n    ]);\n    // Screen size detection is now handled by useDeviceDetection hook\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (inputRef.current && messagePairing.messages.length === 0) {\n                const shouldAutoFocus = window.innerWidth >= 768;\n                if (shouldAutoFocus) {\n                    const timer = setTimeout({\n                        \"ChatInterface.useEffect.timer\": ()=>{\n                            var _inputRef_current;\n                            (_inputRef_current = inputRef.current) === null || _inputRef_current === void 0 ? void 0 : _inputRef_current.focus();\n                        }\n                    }[\"ChatInterface.useEffect.timer\"], 100);\n                    return ({\n                        \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                    })[\"ChatInterface.useEffect\"];\n                }\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messagePairing.messages.length\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            return ({\n                \"ChatInterface.useEffect\": ()=>{\n                    if (sseConnection) {\n                        sseConnection.close();\n                    }\n                }\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], [\n        sseConnection\n    ]);\n    // Check if settingData has been populated with actual data\n    const hasSettingData = settingData && Object.keys(settingData).some((key)=>settingData[key] !== null);\n    // Show loading state while waiting for data\n    // if (!hasSettingData) {\n    //   return (\n    //     <div className=\"bg-white flex flex-col min-h-screen items-center justify-center\">\n    //       <div className=\"text-center\">\n    //         <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4\"></div>\n    //         <p className=\"text-gray-600\">Loading chat settings...</p>\n    //         {contextError && (\n    //           <p className=\"text-red-500 mt-2\">Error: {contextError}</p>\n    //         )}\n    //       </div>\n    //     </div>\n    //   );\n    // }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:flex flex-1 flex-col px-4 \",\n                children: messagePairing.messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-1 items-center justify-center min-h-[calc(100vh-64px)] mt-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center w-[768px] justify-center mx-auto \",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl text-gray-900 mb-6 text-center transition-opacity duration-500 \".concat(showInitialUI ? \"opacity-100\" : \"opacity-0\"),\n                                style: {\n                                    transitionDelay: showInitialUI ? \"40ms\" : \"0ms\"\n                                },\n                                children: \"How can I help you?\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 621,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-full mb-6 flex flex-col items-center transition-all duration-500 \".concat(showInitialUI ? \"opacity-100 \" : \"opacity-0\"),\n                                style: {\n                                    transitionDelay: showInitialUI ? \"80ms\" : \"0ms\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InputSection__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    message: message,\n                                    onMessageChange: setMessage,\n                                    onSubmit: handleSubmit,\n                                    isDisabled: false,\n                                    isMobile: isMobile,\n                                    placeholder: \"Ask anything\",\n                                    showPoweredBy: false\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 638,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 630,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-full max-w-2xl transition-all duration-200 ease-in-out \".concat(isTyping ? \"opacity-100 pointer-events-none\" // TC1\n                                 : showInitialUI ? \"opacity-100 \" : \"opacity-0\"),\n                                style: {\n                                    transitionDelay: showInitialUI ? \"40ms\" : \"0ms\",\n                                    zIndex: 10\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CarouselSection__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    settingData: settingData,\n                                    currentSlide: carousel.currentSlide,\n                                    onCardClick: handleSuggestionClick,\n                                    carouselRef: carouselRef,\n                                    onTouchStart: carousel.handleTouchStart,\n                                    onTouchMove: carousel.handleTouchMove,\n                                    onTouchEnd: carousel.handleTouchEnd,\n                                    onPrevSlide: carousel.prevSlide,\n                                    onNextSlide: carousel.nextSlide,\n                                    isAtStart: carousel.isAtStart,\n                                    isAtEnd: carousel.isAtEnd,\n                                    isMobile: isMobile\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 662,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 649,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 620,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                    lineNumber: 619,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: desktopMessagesContainerRef,\n                            className: \"flex-1 overflow-y-auto max-h-[calc(100vh-200px)] mt-14\",\n                            style: {\n                                overscrollBehavior: \"contain\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full lg:w-[768px] mx-auto px-4 h-fit \",\n                                children: [\n                                    renderMessagePairs(),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: messagesEndRef\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 688,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 686,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 681,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed bottom-0 left-0 right-0 p-4 mt-5\",\n                            style: {\n                                zIndex: 1000,\n                                marginTop: \"20px\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InputSection__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                message: message,\n                                onMessageChange: setMessage,\n                                onSubmit: handleSubmit,\n                                isDisabled: false,\n                                isMobile: isMobile,\n                                placeholder: \"Ask anything\",\n                                showPoweredBy: true\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 696,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 692,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 617,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden overflow-hidden fixed inset-0 flex flex-col  mt-10 \",\n                children: [\n                    messagePairing.messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center pt-15 pb-4 px-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden md:flex lg:hidden flex-col items-center\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 715,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 714,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center justify-center flex-1 px-4\",\n                                style: {\n                                    paddingBottom: \"260px\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl md:text-4xl text-gray-900 text-center leading-relaxed transition-all duration-700 \".concat(showInitialUI ? \"opacity-100 \" : \"opacity-0 \"),\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"40ms\" : \"0ms\"\n                                    },\n                                    children: \"How can I help you?\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 721,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 717,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: mobileMessagesContainerRef,\n                                className: \"md:hidden flex-1 overflow-y-auto max-h-[calc(100vh-190px)] hide-scrollbar\",\n                                style: {\n                                    overscrollBehavior: \"contain\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full max-w-[803px] mx-auto px-4  pt-12\",\n                                    children: [\n                                        renderMessagePairs(),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: messagesEndRef\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 743,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 741,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 736,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: tabletMessagesContainerRef,\n                                className: \"hidden md:block lg:hidden flex-1 overflow-y-auto max-h-[calc(100vh-100px)] pb-[140px] hide-scrollbar\",\n                                style: {\n                                    overscrollBehavior: \"contain\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full max-w-[803px] mx-auto px-4 pb-6 pt-12\",\n                                    children: [\n                                        renderMessagePairs(),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: messagesEndRef\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 755,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 753,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 748,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed bottom-0 left-0 right-0 bg-white\",\n                                style: {\n                                    minHeight: \"160px\",\n                                    zIndex: 1000,\n                                    paddingBottom: \"env(safe-area-inset-bottom, 0)\",\n                                    transform: \"translateZ(0)\",\n                                    backfaceVisibility: \"hidden\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InputSection__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    message: message,\n                                    onMessageChange: setMessage,\n                                    onSubmit: handleSubmit,\n                                    isDisabled: false,\n                                    isMobile: isMobile,\n                                    placeholder: \"Ask anything\",\n                                    showPoweredBy: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 769,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 759,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-0 left-0 right-0 bg-white\",\n                        children: messagePairing.messages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"\".concat(isMobile ? \"px-0\" : \"px-4\", \" pt-2 pb-2 transition-all duration-500 ease-in-out \").concat(isTyping ? \"opacity-0 pointer-events-none\" : showInitialUI ? \"opacity-100\" : \"opacity-0\"),\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"120ms\" : \"0ms\",\n                                        position: \"relative\",\n                                        zIndex: 10,\n                                        backgroundColor: \"white\",\n                                        paddingBottom: \"10px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"overflow-hidden px-4\",\n                                            children: !(settingData === null || settingData === void 0 ? void 0 : settingData.suggestedTopics) || settingData.suggestedTopics.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center items-center py-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                    lineNumber: 809,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 808,\n                                                columnNumber: 21\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                ref: carouselRef,\n                                                className: \"flex gap-4 transition-transform duration-300 ease-in-out\",\n                                                style: {\n                                                    transform: \"translateX(-\".concat(carousel.currentSlide * 100, \"%)\")\n                                                },\n                                                onTouchStart: carousel.handleTouchStart,\n                                                onTouchMove: carousel.handleTouchMove,\n                                                onTouchEnd: carousel.handleTouchEnd,\n                                                children: settingData.suggestedTopics.map((card, cardIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleSuggestionClick(card.question),\n                                                        className: \"\".concat(isMobile ? \"py-3 mt-3\" : \"py-3\", \" px-4 rounded-[8px] bg-[#F6F6F6] text-left hover:bg-[#D6EDFF] transition-all duration-200 flex-shrink-0 touch-manipulation\"),\n                                                        style: {\n                                                            // Calculate width accounting for gap (gap-4 = 16px)\n                                                            width: \"calc(100% - 16px)\",\n                                                            minHeight: isMobile ? \"90px\" : \"80px\"\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"\".concat(isMobile ? \"text-[16px]\" : \"text-[16px]\", \" font-[600] text-black mb-1 leading-snug break-words word-wrap overflow-wrap-anywhere\"),\n                                                                children: card.question\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                lineNumber: 835,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"\".concat(isMobile ? \"text-[14px]\" : \"text-[14px]\", \" text-gray-500 leading-snug break-words word-wrap overflow-wrap-anywhere\"),\n                                                                children: card.subtitle\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                lineNumber: 842,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, cardIndex, true, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 823,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 812,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 805,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        (settingData === null || settingData === void 0 ? void 0 : settingData.suggestedTopics) && settingData.suggestedTopics.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handlePrevSlide,\n                                                    disabled: isAtStart,\n                                                    className: \"hidden md:flex absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-10 w-8 h-8 border rounded-full items-center justify-center shadow-md transition-all duration-200 touch-manipulation z-10 \".concat(isAtStart ? \"bg-gray-100 border-gray-300 cursor-not-allowed\" : \"bg-white border-gray-200 hover:bg-gray-50 cursor-pointer\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FaChevronLeft, {\n                                                        className: \"w-3 h-3 \".concat(isAtStart ? \"text-gray-400\" : \"text-gray-600\")\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 867,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                    lineNumber: 858,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleNextSlide,\n                                                    disabled: isAtEnd(),\n                                                    className: \"hidden md:flex absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-10 w-8 h-8 border rounded-full items-center justify-center shadow-md transition-all duration-200 touch-manipulation z-10 \".concat(isAtEnd() ? \"bg-gray-100 border-gray-300 cursor-not-allowed\" : \"bg-white border-gray-200 hover:bg-gray-50 cursor-pointer\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FaChevronRight, {\n                                                        className: \"w-3 h-3 \".concat(isAtEnd() ? \"text-gray-400\" : \"text-gray-600\")\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 882,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                    lineNumber: 873,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 787,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-4 bg-white transition-all duration-500 \".concat(showInitialUI ? \"opacity-100 translate-y-0\" : \"translate-y-4\"),\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"200ms\" : \"0ms\",\n                                        position: \"relative\",\n                                        zIndex: 5,\n                                        paddingBottom: \"env(safe-area-inset-bottom)\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InputSection__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        message: message,\n                                        onMessageChange: setMessage,\n                                        onSubmit: handleSubmit,\n                                        isDisabled: false,\n                                        isMobile: isMobile,\n                                        placeholder: \"Ask anything\",\n                                        showPoweredBy: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 904,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 893,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 783,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 711,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n        lineNumber: 615,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatInterface, \"Z14ZI+MxLPucKI6s+BOZo/aMs84=\", false, function() {\n    return [\n        _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_3__.useChatContext,\n        _hooks_useCarousel__WEBPACK_IMPORTED_MODULE_4__.useCarousel,\n        _hooks_useDeviceDetection__WEBPACK_IMPORTED_MODULE_5__.useDeviceDetection,\n        _hooks_useMessagePairing__WEBPACK_IMPORTED_MODULE_6__.useMessagePairing\n    ];\n});\n_c = ChatInterface;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatInterface);\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ChatInterface.jsx\n"));

/***/ })

});