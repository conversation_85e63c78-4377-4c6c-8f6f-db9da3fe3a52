"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[slug]/page",{

/***/ "(app-pages-browser)/./src/components/ChatInterface.jsx":
/*!******************************************!*\
  !*** ./src/components/ChatInterface.jsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/constants */ \"(app-pages-browser)/./src/utils/constants.js\");\n/* harmony import */ var _utils_config__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/config */ \"(app-pages-browser)/./src/utils/config.js\");\n/* harmony import */ var _barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FaArrowUp,FaChevronLeft,FaChevronRight!=!react-icons/fa6 */ \"(app-pages-browser)/./node_modules/react-icons/fa6/index.mjs\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../contexts/ChatContext */ \"(app-pages-browser)/./src/contexts/ChatContext.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst ChatInterface = (param)=>{\n    let { slug, query } = param;\n    _s();\n    // Context API\n    const { metadata, updateMetadata, setLoadingState, setErrorState, clearError, settingData, updateSettingData, getCustomerName, getBusinessName, hasMetadata, loading: contextLoading, error: contextError } = (0,_contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__.useChatContext)();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [currentSlide, setCurrentSlide] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [touchStart, setTouchStart] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [touchEnd, setTouchEnd] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isTablet, setIsTablet] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showInitialUI, setShowInitialUI] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isBotTyping, setIsBotTyping] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isBotThinking, setIsBotThinking] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [sseConnection, setSseConnection] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isFirstMessage, setIsFirstMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [shouldScrollToTop, setShouldScrollToTop] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Enhanced message pairing state\n    const [pendingQueries, setPendingQueries] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(new Map()); // Track queries waiting for responses\n    const [messageSequence, setMessageSequence] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0); // Global sequence counter\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const carouselRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const desktopMessagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const mobileMessagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const tabletMessagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // Debug: Log state updates when they actually happen\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (metadata && Object.keys(metadata).some({\n                \"ChatInterface.useEffect\": (key)=>metadata[key] !== null\n            }[\"ChatInterface.useEffect\"])) {\n                console.log(\"✅ METADATA STATE UPDATED:\", metadata);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        metadata\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (settingData && Object.keys(settingData).some({\n                \"ChatInterface.useEffect\": (key)=>settingData[key] !== null\n            }[\"ChatInterface.useEffect\"])) {\n                console.log(\"✅ SETTING DATA STATE UPDATED:\", settingData);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        settingData\n    ]);\n    // Utility functions for message pairing\n    const generateMessageId = ()=>{\n        const sequence = messageSequence + 1;\n        setMessageSequence(sequence);\n        return \"msg_\".concat(Date.now(), \"_\").concat(sequence, \"_\").concat(Math.random().toString(36).substr(2, 9));\n    };\n    const createMessagePairs = (messages)=>{\n        const pairs = [];\n        const processedMessages = new Set();\n        // Sort messages by timestamp to handle out-of-order arrivals\n        const sortedMessages = [\n            ...messages\n        ].sort((a, b)=>a.timestamp - b.timestamp);\n        // Group messages by queryId for proper pairing\n        const messageGroups = new Map();\n        sortedMessages.forEach((msg)=>{\n            if (msg.source === \"USER\") {\n                // User message starts a new conversation pair\n                const queryId = msg.queryId || msg.id || \"fallback_\".concat(msg.timestamp);\n                if (!messageGroups.has(queryId)) {\n                    messageGroups.set(queryId, {\n                        user: null,\n                        bot: null,\n                        timestamp: msg.timestamp\n                    });\n                }\n                messageGroups.get(queryId).user = msg;\n            } else if (msg.source === \"BOT\") {\n                // Bot message should be paired with corresponding user message\n                const queryId = msg.queryId || msg.responseToId;\n                if (queryId && messageGroups.has(queryId)) {\n                    messageGroups.get(queryId).bot = msg;\n                } else {\n                    // Fallback: pair with most recent unpaired user message\n                    const unpairedGroups = Array.from(messageGroups.entries()).filter((param)=>{\n                        let [_, group] = param;\n                        return group.user && !group.bot;\n                    }).sort((a, b)=>b[1].timestamp - a[1].timestamp);\n                    if (unpairedGroups.length > 0) {\n                        const [groupId, group] = unpairedGroups[0];\n                        group.bot = msg;\n                        // Update the message with proper queryId for future reference\n                        msg.queryId = groupId;\n                    }\n                }\n            }\n        });\n        // Convert groups to pairs array, sorted by timestamp\n        const sortedGroups = Array.from(messageGroups.entries()).sort((a, b)=>a[1].timestamp - b[1].timestamp);\n        return sortedGroups.map((param)=>{\n            let [queryId, group] = param;\n            return {\n                id: queryId,\n                user: group.user,\n                bot: group.bot,\n                timestamp: group.timestamp,\n                isComplete: !!(group.user && group.bot),\n                isPending: !!(group.user && !group.bot)\n            };\n        });\n    };\n    // Detect iOS for targeted fixes\n    const isIOS = ()=>{\n        if (true) {\n            return /iPhone|iPad|iPod/i.test(navigator.userAgent);\n        }\n        return false;\n    };\n    const username = slug;\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (username && \"object\" !== \"undefined\") {\n                fetchCustomer(username);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        username\n    ]);\n    const fetchCustomer = async (customerName)=>{\n        try {\n            setLoadingState(true);\n            clearError();\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"\".concat(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_SETTINGS, \"?customerName=\").concat(customerName));\n            // Extract data from axios response\n            const responseData = response.data;\n            const metadataFromResponse = responseData.metaData;\n            updateSettingData(responseData);\n            updateMetadata(metadataFromResponse);\n            if (responseData && responseData.customerName) {\n                //  localStorage for backward compatibility\n                localStorage.setItem(\"customerName_userId\", responseData.customerName);\n                localStorage.setItem(\"BusinessName\", responseData.businessName);\n                window.dispatchEvent(new CustomEvent(\"businessNameLoaded\", {\n                    detail: {\n                        businessName: responseData.businessName\n                    }\n                }));\n                const existingUserId = localStorage.getItem(\"userID\");\n                if (existingUserId) {\n                    await fetchExistingMessages(existingUserId);\n                } else {\n                    await initializeChatSession();\n                }\n            } else {\n                await initializeChatSession();\n            }\n        } catch (error) {\n            console.error(\"Error fetching customer data:\", error);\n            setErrorState(\"Failed to load customer settings\");\n            // Fallback metadata\n            updateMetadata({\n                customerName: customerName,\n                businessName: \"Driply\"\n            });\n            localStorage.setItem(\"BusinessName\", \"Driply\");\n            window.dispatchEvent(new CustomEvent(\"businessNameLoaded\", {\n                detail: {\n                    businessName: \"Driply\"\n                }\n            }));\n            await initializeChatSession();\n        } finally{\n            setLoadingState(false);\n        }\n    };\n    const fetchExistingMessages = async (userId)=>{\n        // console.log(\"fetchExistingMessages\");\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"\".concat(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_MESSAGES, \"?userId=\").concat(userId));\n            if (response.data && response.data.length > 0) {\n                setMessages(response.data.map((msg)=>({\n                        text: msg.message,\n                        timestamp: new Date(msg.createdAt).getTime(),\n                        type: msg.type,\n                        source: msg.source\n                    })));\n            }\n            connectToSSE(userId);\n        } catch (error) {\n            await initializeChatSession();\n        }\n    };\n    const initializeChatSession = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_INIT, {\n                customerName: username\n            });\n            if (response.data && response.data.userId) {\n                localStorage.setItem(\"userId\", response.data.userId);\n                localStorage.setItem(\"userID\", response.data._id);\n                await fetchExistingMessages(response.data._id);\n            }\n        } catch (error) {\n            if (!localStorage.getItem(\"BusinessName\")) {\n                localStorage.setItem(\"BusinessName\", \"Driply\");\n                window.dispatchEvent(new CustomEvent(\"businessNameLoaded\", {\n                    detail: {\n                        businessName: \"Driply\"\n                    }\n                }));\n            }\n        }\n    };\n    const connectToSSE = (userId)=>{\n        try {\n            if (sseConnection) {\n                sseConnection.close();\n            }\n            const sseUrl = (0,_utils_config__WEBPACK_IMPORTED_MODULE_4__.getSSEUrl)(userId);\n            const eventSource = new EventSource(sseUrl);\n            setSseConnection(eventSource);\n            eventSource.addEventListener(\"message\", (param)=>{\n                let { data } = param;\n                try {\n                    const contents = JSON.parse(data);\n                    handleSSEMessage(contents);\n                } catch (error) {\n                // Handle parsing error silently\n                }\n            });\n            eventSource.addEventListener(\"error\", ()=>{\n            // Handle connection error silently\n            });\n        } catch (error) {\n        // Handle SSE connection error silently\n        }\n    };\n    const handleSSEMessage = (data)=>{\n        const subtype = data.subType || data.subtype || data.type || \"UNKNOWN\";\n        const content = data.content || \"\";\n        const message = data.message || data.text || \"\";\n        if ([\n            \"TYPING\",\n            \"THINKING\",\n            \"BEHAVIOUR_MESSAGE\"\n        ].includes(subtype)) {\n            switch(subtype){\n                case \"TYPING\":\n                    setIsBotTyping(true);\n                    setIsBotThinking(false);\n                    break;\n                case \"THINKING\":\n                    setIsBotThinking(true);\n                    setIsBotTyping(false);\n                    break;\n                case \"BEHAVIOUR_MESSAGE\":\n                    setIsBotTyping(false);\n                    setIsBotThinking(false);\n                    break;\n            }\n            return;\n        }\n        const displayText = message || content;\n        if (!displayText) return;\n        const displayType = [\n            \"TEXT\",\n            \"MESSAGE\",\n            \"DATA_MESSAGE\"\n        ].includes(subtype) ? subtype : \"UNKNOWN\";\n        // Enhanced message pairing logic\n        const responseToId = data.queryId || data.responseToId || data.correlationId;\n        let queryId = responseToId;\n        // If no explicit queryId, find the most recent pending query\n        if (!queryId) {\n            const pendingEntries = Array.from(pendingQueries.entries()).sort((a, b)=>b[1].timestamp - a[1].timestamp);\n            if (pendingEntries.length > 0) {\n                queryId = pendingEntries[0][0];\n            }\n        }\n        // Create bot message with proper pairing information\n        const botMessage = {\n            id: generateMessageId(),\n            text: displayText,\n            timestamp: Date.now(),\n            type: displayType,\n            source: \"BOT\",\n            queryId: queryId,\n            responseToId: queryId // Explicit response relationship\n        };\n        console.log(\"🤖 BOT RESPONSE:\", {\n            botMessageId: botMessage.id,\n            queryId: queryId,\n            responseToId: responseToId,\n            pendingQueries: Array.from(pendingQueries.keys()),\n            message: displayText.substring(0, 50) + \"...\"\n        });\n        setMessages((prev)=>[\n                ...prev,\n                botMessage\n            ]);\n        // Remove from pending queries if we found a match\n        if (queryId && pendingQueries.has(queryId)) {\n            setPendingQueries((prev)=>{\n                const updated = new Map(prev);\n                updated.delete(queryId);\n                return updated;\n            });\n        }\n        setIsBotTyping(false);\n        setIsBotThinking(false);\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (message.trim()) {\n            const userMessage = message.trim();\n            const isFirstMsg = messages.length === 0;\n            // Generate unique ID for this query\n            const queryId = generateMessageId();\n            const userMessageObj = {\n                id: queryId,\n                queryId: queryId,\n                text: userMessage,\n                timestamp: Date.now(),\n                type: \"TEXT\",\n                source: \"USER\"\n            };\n            console.log(\"👤 USER QUERY:\", {\n                queryId: queryId,\n                message: userMessage.substring(0, 50) + \"...\",\n                timestamp: userMessageObj.timestamp\n            });\n            setMessages((prev)=>[\n                    ...prev,\n                    userMessageObj\n                ]);\n            // Track this query as pending response\n            setPendingQueries((prev)=>new Map(prev).set(queryId, {\n                    id: queryId,\n                    message: userMessage,\n                    timestamp: userMessageObj.timestamp,\n                    sentAt: Date.now()\n                }));\n            setMessage(\"\");\n            setIsTyping(false);\n            // For first message, set flag to scroll to top\n            if (isFirstMsg) {\n                setShouldScrollToTop(true);\n                setIsFirstMessage(false);\n            }\n            // Show loading dots immediately after sending message\n            setIsBotThinking(true);\n            setIsBotTyping(false);\n            // Reset textarea\n            if (inputRef.current) {\n                inputRef.current.style.height = \"104px\";\n                inputRef.current.scrollTop = 0;\n                inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n                inputRef.current.classList.add(\"reset-height\");\n            }\n            try {\n                const userId = localStorage.getItem(\"userID\");\n                if (userId) {\n                    await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_SEND, {\n                        userId: userId,\n                        message: userMessage,\n                        queryId: queryId,\n                        typs: \"TEXT\",\n                        source: \"USER\",\n                        isTest: query.isTest === \"1\" ? true : false\n                    });\n                }\n            } catch (error) {\n                // Hide loading dots on error\n                setIsBotThinking(false);\n                setIsBotTyping(false);\n                const errorMessage = {\n                    id: generateMessageId(),\n                    text: \"Sorry, there was an error sending your message. Please try again.\",\n                    timestamp: Date.now(),\n                    type: \"TEXT\",\n                    source: \"BOT\",\n                    queryId: queryId,\n                    isError: true\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        errorMessage\n                    ]);\n                // Remove from pending queries since we got an error\n                setPendingQueries((prev)=>{\n                    const updated = new Map(prev);\n                    updated.delete(queryId);\n                    return updated;\n                });\n            }\n        }\n    };\n    const handleTextareaResize = (textarea)=>{\n        if (textarea) {\n            textarea.style.height = \"104px\";\n            const scrollHeight = textarea.scrollHeight;\n            if (isIOS()) {\n                if (scrollHeight > 48) {\n                    textarea.style.overflowY = \"auto\";\n                } else {\n                    textarea.style.overflowY = \"hidden\";\n                }\n            } else {\n                const newHeight = Math.min(250, Math.max(104, scrollHeight));\n                textarea.style.height = \"\".concat(newHeight, \"px\");\n                textarea.style.overflowY = scrollHeight > newHeight ? \"auto\" : \"hidden\";\n            }\n        }\n    };\n    const handleInputChange = (e)=>{\n        const value = e.target.value;\n        setMessage(value);\n        handleTextareaResize(e.target);\n        if (messages.length === 0) {\n            setIsTyping(value.length > 0);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length > 0 && inputRef.current && !message) {\n                const timer = setTimeout({\n                    \"ChatInterface.useEffect.timer\": ()=>{\n                        if (inputRef.current) {\n                            inputRef.current.style.height = \"104px\";\n                            inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n                            inputRef.current.classList.add(\"reset-height\");\n                        }\n                    }\n                }[\"ChatInterface.useEffect.timer\"], 50);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                })[\"ChatInterface.useEffect\"];\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages,\n        message\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length > 0) {\n                handleChatGPTScroll();\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages,\n        isBotTyping,\n        isBotThinking\n    ]);\n    const handleChatGPTScroll = ()=>{\n        setTimeout(()=>{\n            // Determine screen type and get appropriate container\n            const screenWidth = window.innerWidth;\n            const isDesktopLayout = screenWidth >= 1024;\n            const isTabletLayout = screenWidth >= 768 && screenWidth < 1024;\n            let container;\n            if (isDesktopLayout) {\n                container = desktopMessagesContainerRef.current;\n            } else if (isTabletLayout) {\n                container = tabletMessagesContainerRef.current;\n            } else {\n                container = mobileMessagesContainerRef.current;\n            }\n            if (!container) return;\n            if (shouldScrollToTop) {\n                // For first message, scroll to top to show the message there\n                container.scrollTo({\n                    top: 0,\n                    behavior: \"smooth\"\n                });\n                setShouldScrollToTop(false);\n            } else {\n                // Different scrolling behavior for each screen type\n                const messageElements = container.querySelectorAll(\".message-pair\");\n                if (messageElements.length > 0) {\n                    const lastMessagePair = messageElements[messageElements.length - 1];\n                    const containerHeight = container.clientHeight;\n                    const pairHeight = lastMessagePair.offsetHeight;\n                    const pairTop = lastMessagePair.offsetTop;\n                    if (isTabletLayout) {\n                        // Tablet-specific scrolling: Keep messages visible by scrolling to bottom of container\n                        // This ensures both user message and AI response remain visible\n                        const scrollPosition = Math.max(0, pairTop + pairHeight - containerHeight + 100);\n                        container.scrollTo({\n                            top: scrollPosition,\n                            behavior: \"smooth\"\n                        });\n                    } else {\n                        // Desktop and mobile: Center the message pair on screen\n                        const scrollPosition = pairTop - (containerHeight - pairHeight) / 2;\n                        container.scrollTo({\n                            top: Math.max(0, scrollPosition),\n                            behavior: \"smooth\"\n                        });\n                    }\n                }\n            }\n        }, 150);\n    };\n    const renderMessagePairs = ()=>{\n        // Use the enhanced message pairing logic\n        const messagePairs = createMessagePairs(messages);\n        console.log(\"🔄 RENDERING MESSAGE PAIRS:\", {\n            totalMessages: messages.length,\n            totalPairs: messagePairs.length,\n            pendingQueries: Array.from(pendingQueries.keys()),\n            pairs: messagePairs.map((pair)=>({\n                    id: pair.id,\n                    hasUser: !!pair.user,\n                    hasBot: !!pair.bot,\n                    isComplete: pair.isComplete,\n                    isPending: pair.isPending\n                }))\n        });\n        return messagePairs.map((pair, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"message-pair flex flex-col justify-start\",\n                \"data-pair-id\": pair.id,\n                \"data-is-complete\": pair.isComplete,\n                \"data-is-pending\": pair.isPending,\n                style: {\n                    minHeight: i === messagePairs.length - 1 ? isMobile ? \"calc(100vh - 200px)\" // Mobile-specific height for newest message\n                     : \"calc(100vh - 200px)\" // Desktop/tablet height for newest message\n                     : \"\",\n                    paddingTop: i === 0 ? \"1rem\" : \"1rem\",\n                    paddingBottom: \"0rem\"\n                },\n                children: [\n                    pair.user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-end mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-[16px] font-[400] rounded-3xl max-w-xs lg:max-w-lg break-words hyphens-auto bg-gray-100 \".concat(isMobile ? \"rounded-[15px] px-3 py-2\" : \"px-4 py-3\"),\n                            \"data-message-id\": pair.user.id,\n                            \"data-query-id\": pair.user.queryId,\n                            style: {\n                                overflowWrap: \"break-word\",\n                                wordBreak: \"break-word\",\n                                maxWidth: isMobile ? \"90%\" : undefined\n                            },\n                            children: [\n                                pair.user.text,\n                                pair.user.isSuggestion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-500 ml-2\",\n                                    children: \"\\uD83D\\uDCA1\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 619,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 605,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 604,\n                        columnNumber: 11\n                    }, undefined),\n                    pair.bot && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-start\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-[16px] font-[400] rounded-3xl max-w-xs lg:max-w-lg break-words hyphens-auto bg-white \".concat(isMobile ? \"rounded-[15px] px-3 py-2\" : \"px-4 py-3\", \" \").concat(pair.bot.isError ? \"border-red-200 bg-red-50\" : \"\"),\n                            \"data-message-id\": pair.bot.id,\n                            \"data-query-id\": pair.bot.queryId,\n                            \"data-response-to\": pair.bot.responseToId,\n                            style: {\n                                overflowWrap: \"break-word\",\n                                wordBreak: \"break-word\",\n                                maxWidth: isMobile ? \"90%\" : undefined\n                            },\n                            children: [\n                                pair.bot.text,\n                                pair.bot.isError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-red-500 ml-2\",\n                                    children: \"⚠️\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 643,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 628,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 627,\n                        columnNumber: 11\n                    }, undefined),\n                    pair.isPending && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-start mt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-gray-400 text-sm flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-pulse flex space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-gray-400 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 654,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-gray-400 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 655,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-gray-400 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 656,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 653,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2\",\n                                    children: \"Waiting for response...\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 658,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 652,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 651,\n                        columnNumber: 11\n                    }, undefined),\n                    i === messagePairs.length - 1 && (isBotTyping || isBotThinking) && !pair.bot && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-start\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white \".concat(isMobile ? \"px-3 py-2 rounded-[15px]\" : \"px-4 py-3 rounded-3xl\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-black rounded-full loading-dot\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 674,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-black rounded-full loading-dot\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 675,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-black rounded-full loading-dot\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 676,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 673,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 666,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 665,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, \"pair-\".concat(pair.id), true, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 585,\n                columnNumber: 7\n            }, undefined));\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSubmit(e);\n        }\n    };\n    const handleSuggestionClick = async (cardTitle)=>{\n        const isFirstMsg = messages.length === 0;\n        // Generate unique ID for this suggestion query\n        const queryId = generateMessageId();\n        const userMessageObj = {\n            id: queryId,\n            queryId: queryId,\n            text: cardTitle,\n            timestamp: Date.now(),\n            type: \"TEXT\",\n            source: \"USER\",\n            isSuggestion: true\n        };\n        console.log(\"💡 SUGGESTION QUERY:\", {\n            queryId: queryId,\n            suggestion: cardTitle,\n            timestamp: userMessageObj.timestamp\n        });\n        setMessages((prev)=>[\n                ...prev,\n                userMessageObj\n            ]);\n        // Track this suggestion query as pending response\n        setPendingQueries((prev)=>new Map(prev).set(queryId, {\n                id: queryId,\n                message: cardTitle,\n                timestamp: userMessageObj.timestamp,\n                sentAt: Date.now(),\n                isSuggestion: true\n            }));\n        setMessage(\"\");\n        setIsTyping(false);\n        // For first message, set flag to scroll to top\n        if (isFirstMsg) {\n            setShouldScrollToTop(true);\n            setIsFirstMessage(false);\n        }\n        // Show loading dots immediately after sending message\n        setIsBotThinking(true);\n        setIsBotTyping(false);\n        if (inputRef.current) {\n            inputRef.current.style.height = \"104px\";\n            inputRef.current.scrollTop = 0;\n            inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n            inputRef.current.classList.add(\"reset-height\");\n        }\n        try {\n            const userId = localStorage.getItem(\"userID\");\n            if (userId) {\n                await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_SEND, {\n                    userId: userId,\n                    message: cardTitle,\n                    queryId: queryId,\n                    customerName: username,\n                    isSuggestion: true\n                });\n            }\n        } catch (error) {\n            // Hide loading dots on error\n            setIsBotThinking(false);\n            setIsBotTyping(false);\n            const errorMessage = {\n                id: generateMessageId(),\n                text: \"Sorry, there was an error sending your message. Please try again.\",\n                timestamp: Date.now(),\n                type: \"TEXT\",\n                source: \"BOT\",\n                queryId: queryId,\n                isError: true\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n            // Remove from pending queries since we got an error\n            setPendingQueries((prev)=>{\n                const updated = new Map(prev);\n                updated.delete(queryId);\n                return updated;\n            });\n        }\n    };\n    const getMaxSlides = ()=>{\n        if (isMobile) return settingData === null || settingData === void 0 ? void 0 : settingData.suggestedTopics.length;\n        return Math.ceil((settingData === null || settingData === void 0 ? void 0 : settingData.suggestedTopics.length) / 2);\n    };\n    const nextSlide = ()=>{\n        if (isMobile) {\n            setCurrentSlide((prev)=>prev < (settingData === null || settingData === void 0 ? void 0 : settingData.suggestedTopics.length) - 1 ? prev + 1 : prev);\n        } else {\n            const maxSlides = getMaxSlides();\n            setCurrentSlide((prev)=>(prev + 1) % maxSlides);\n        }\n    };\n    const prevSlide = ()=>{\n        if (isMobile) {\n            setCurrentSlide((prev)=>prev > 0 ? prev - 1 : prev);\n        } else {\n            const maxSlides = getMaxSlides();\n            setCurrentSlide((prev)=>(prev - 1 + maxSlides) % maxSlides);\n        }\n    };\n    const handleNextSlide = ()=>{\n        nextSlide();\n    };\n    const handlePrevSlide = ()=>{\n        prevSlide();\n    };\n    const handleTouchStart = (e)=>{\n        setTouchStart(e.targetTouches[0].clientX);\n    };\n    const handleTouchMove = (e)=>{\n        setTouchEnd(e.targetTouches[0].clientX);\n    };\n    const handleTouchEnd = ()=>{\n        if (!touchStart || !touchEnd) return;\n        const swipeDistance = touchStart - touchEnd;\n        const minSwipeDistance = 50;\n        if (swipeDistance > minSwipeDistance) {\n            nextSlide();\n        }\n        if (swipeDistance < -minSwipeDistance) {\n            prevSlide();\n        }\n        setTouchStart(0);\n        setTouchEnd(0);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length === 0) {\n                setShowInitialUI(false);\n                const timer = setTimeout({\n                    \"ChatInterface.useEffect.timer\": ()=>setShowInitialUI(true)\n                }[\"ChatInterface.useEffect.timer\"], 60);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                })[\"ChatInterface.useEffect\"];\n            } else {\n                setShowInitialUI(false);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages.length\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const checkScreenSize = {\n                \"ChatInterface.useEffect.checkScreenSize\": ()=>{\n                    if (true) {\n                        const width = window.innerWidth;\n                        setIsMobile(width < 768);\n                        setIsTablet(width >= 768 && width < 1024);\n                    }\n                }\n            }[\"ChatInterface.useEffect.checkScreenSize\"];\n            checkScreenSize();\n            const handleResize = {\n                \"ChatInterface.useEffect.handleResize\": ()=>{\n                    setCurrentSlide(0);\n                    checkScreenSize();\n                }\n            }[\"ChatInterface.useEffect.handleResize\"];\n            if (true) {\n                window.addEventListener(\"resize\", handleResize);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>window.removeEventListener(\"resize\", handleResize)\n                })[\"ChatInterface.useEffect\"];\n            }\n        }\n    }[\"ChatInterface.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (inputRef.current && messages.length === 0) {\n                const shouldAutoFocus = window.innerWidth >= 768;\n                if (shouldAutoFocus) {\n                    const timer = setTimeout({\n                        \"ChatInterface.useEffect.timer\": ()=>{\n                            var _inputRef_current;\n                            (_inputRef_current = inputRef.current) === null || _inputRef_current === void 0 ? void 0 : _inputRef_current.focus();\n                        }\n                    }[\"ChatInterface.useEffect.timer\"], 100);\n                    return ({\n                        \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                    })[\"ChatInterface.useEffect\"];\n                }\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages.length\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            return ({\n                \"ChatInterface.useEffect\": ()=>{\n                    if (sseConnection) {\n                        sseConnection.close();\n                    }\n                }\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], [\n        sseConnection\n    ]);\n    // Check if settingData has been populated with actual data\n    const hasSettingData = settingData && Object.keys(settingData).some((key)=>settingData[key] !== null);\n    console.log(\"🔍 RENDER CHECK:\", {\n        settingData,\n        hasSettingData,\n        contextLoading,\n        contextError\n    });\n    // Show loading state while waiting for data\n    if (!hasSettingData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white flex flex-col min-h-screen items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 912,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading chat settings...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 913,\n                        columnNumber: 11\n                    }, undefined),\n                    contextError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-500 mt-2\",\n                        children: [\n                            \"Error: \",\n                            contextError\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 915,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 911,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n            lineNumber: 910,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"bg-white flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"hidden lg:flex flex-1 flex-col px-4 \",\n                children: messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex flex-1 items-center justify-center min-h-[calc(100vh-64px)] mt-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex flex-col items-center w-[768px] justify-center mx-auto \",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    transitionDelay: showInitialUI ? \"40ms\" : \"0ms\"\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-4xl text-gray-900 mb-6 text-center transition-all duration-700 \".concat(showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                children: \"How can I help you?\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 929,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                style: {\n                                    transitionDelay: showInitialUI ? \"120ms\" : \"0ms\"\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"relative w-full mb-6 flex flex-col items-center transition-all duration-500 \".concat(showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        ref: messages.length === 0 ? inputRef : null,\n                                        value: message,\n                                        onChange: handleInputChange,\n                                        onKeyDown: handleKeyPress,\n                                        placeholder: \"Ask anything\",\n                                        autoFocus: messages.length === 0,\n                                        rows: 1,\n                                        style: {\n                                            boxSizing: \"border-box\",\n                                            zIndex: 1001,\n                                            position: \"relative\"\n                                        },\n                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"pt-3 px-4 lg:p-4 pb-16 pr-12 w-full min-h-[104px] max-h-[250px] overflow-y-auto hide-scrollbar text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base shadow-md resize-none reset-height\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 949,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: message.trim().length === 0,\n                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"absolute right-3 bottom-3 w-9 h-9 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation \".concat(message.trim().length === 0 ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                            className: \"w-4 h-4 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 973,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 964,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 940,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    transitionDelay: showInitialUI ? \"200ms\" : \"0ms\",\n                                    zIndex: 10\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"relative w-full max-w-2xl transition-all duration-500 ease-in-out \".concat(isTyping ? \"opacity-0 pointer-events-none\" : showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: carouselRef,\n                                            style: {\n                                                transform: \"translateX(-\".concat(currentSlide * 100, \"%)\")\n                                            },\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex transition-transform duration-300 ease-in-out\",\n                                            children: Array.from({\n                                                length: Math.ceil((settingData === null || settingData === void 0 ? void 0 : settingData.suggestedTopics.length) / 2)\n                                            }).map((_, slideIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"w-full flex-shrink-0 flex gap-2 justify-center\",\n                                                    children: settingData === null || settingData === void 0 ? void 0 : settingData.suggestedTopics.slice(slideIndex * 2, slideIndex * 2 + 2).map((card, cardIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleSuggestionClick(card === null || card === void 0 ? void 0 : card.question),\n                                                            style: {\n                                                                width: \"fit-content\"\n                                                            },\n                                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"py-2 px-3 rounded-[8px] bg-[#f6f6f6] text-left group transition-all duration-200 touch-manipulation\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-[16px] font-[600] text-black mb-0.5 leading-tight whitespace-nowrap\",\n                                                                    children: card.question\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                    lineNumber: 1017,\n                                                                    columnNumber: 31\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-[16px] text-gray-500 leading-tight whitespace-nowrap\",\n                                                                    children: card.subtitle\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                    lineNumber: 1020,\n                                                                    columnNumber: 31\n                                                                }, undefined)\n                                                            ]\n                                                        }, slideIndex * 2 + cardIndex, true, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 1009,\n                                                            columnNumber: 29\n                                                        }, undefined))\n                                                }, slideIndex, false, {\n                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                    lineNumber: 1002,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 990,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 989,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handlePrevSlide,\n                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"absolute left-28 top-1/2 transform -translate-y-1/2 -translate-x-6 w-8 h-8 bg-white border border-gray-200 rounded-full flex items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronLeft, {\n                                            className: \"w-3 h-3 text-gray-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1033,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 1029,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleNextSlide,\n                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"absolute right-28 top-1/2 transform -translate-y-1/2 translate-x-6 w-8 h-8 bg-white border border-gray-200 rounded-full flex items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronRight, {\n                                            className: \"w-3 h-3 text-gray-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1039,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 1035,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 976,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 928,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                    lineNumber: 927,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: desktopMessagesContainerRef,\n                            style: {\n                                overscrollBehavior: \"contain\"\n                            },\n                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex-1 overflow-y-auto max-h-[calc(100vh-200px)] mt-14\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"w-full lg:w-[768px] mx-auto px-4 h-fit \",\n                                children: [\n                                    renderMessagePairs(),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: messagesEndRef,\n                                        className: \"jsx-69ac67aa0147cd65\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 1053,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 1051,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 1046,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                zIndex: 1000,\n                                marginTop: \"20px\"\n                            },\n                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"fixed bottom-0 left-0 right-0 p-4 mt-5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSubmit,\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"relative w-full lg:w-[768px] mx-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            ref: messages.length > 0 ? inputRef : null,\n                                            value: message,\n                                            onChange: handleInputChange,\n                                            onKeyDown: handleKeyPress,\n                                            placeholder: \"Ask anything\",\n                                            rows: 1,\n                                            style: {\n                                                boxSizing: \"border-box\",\n                                                zIndex: 1001,\n                                                position: \"relative\"\n                                            },\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"pt-3 px-4 md:px-5 pb-10 pr-12 w-full min-h-[104px] max-h-[250px] overflow-y-auto text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base shadow-lg resize-none reset-height hide-scrollbar\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1065,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: message.trim().length === 0,\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation \".concat(message.trim().length === 0 ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                                className: \"w-4 h-4 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1088,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1079,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1061,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-center mt-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-xs text-gray-500\",\n                                        children: [\n                                            \"This chat is powered by\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-black\",\n                                                children: \"Driply.me\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1094,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 1092,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1091,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 1057,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 925,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"lg:hidden overflow-hidden fixed inset-0 flex flex-col  mt-10 \",\n                children: [\n                    messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex flex-col items-center pt-15 pb-4 px-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"hidden md:flex lg:hidden flex-col items-center\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1107,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 1106,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    paddingBottom: \"260px\"\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex flex-col items-center justify-center flex-1 px-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"40ms\" : \"0ms\"\n                                    },\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-2xl md:text-4xl text-gray-900 text-center leading-relaxed transition-all duration-700 \".concat(showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                    children: \"How can I help you?\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1113,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 1109,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: mobileMessagesContainerRef,\n                                style: {\n                                    overscrollBehavior: \"contain\"\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"md:hidden flex-1 overflow-y-auto max-h-[calc(100vh-190px)] hide-scrollbar\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"w-full max-w-[803px] mx-auto px-4  pt-12\",\n                                    children: [\n                                        renderMessagePairs(),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: messagesEndRef,\n                                            className: \"jsx-69ac67aa0147cd65\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1135,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1133,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 1128,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: tabletMessagesContainerRef,\n                                style: {\n                                    overscrollBehavior: \"contain\"\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"hidden md:block lg:hidden flex-1 overflow-y-auto max-h-[calc(100vh-100px)]  pb-20 hide-scrollbar\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"w-full max-w-[803px] mx-auto px-4 pb-6 pt-12\",\n                                    children: [\n                                        renderMessagePairs(),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: messagesEndRef,\n                                            className: \"jsx-69ac67aa0147cd65\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1147,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1145,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 1140,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    minHeight: \"160px\",\n                                    zIndex: 1000,\n                                    paddingBottom: \"env(safe-area-inset-bottom, 0)\",\n                                    transform: \"translateZ(0)\",\n                                    backfaceVisibility: \"hidden\"\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"fixed bottom-0 left-0 right-0 bg-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        transform: \"translateZ(0)\"\n                                    },\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"px-4 py-4 bg-white\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            style: {\n                                                zIndex: 1001\n                                            },\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"relative bg-white\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    transform: \"translateZ(0)\"\n                                                },\n                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"relative w-full max-w-[890px] mx-auto bg-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        ref: messages.length > 0 ? inputRef : null,\n                                                        value: message,\n                                                        onChange: handleInputChange,\n                                                        onKeyDown: handleKeyPress,\n                                                        placeholder: \"Ask anything\",\n                                                        rows: 1,\n                                                        style: {\n                                                            boxSizing: \"border-box\",\n                                                            maxHeight: \"250px\",\n                                                            zIndex: 1001,\n                                                            overscrollBehavior: \"none\",\n                                                            position: \"relative\"\n                                                        },\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"w-full min-h-[104px] pt-3 px-4 pb-10 pr-12 text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base resize-none overflow-y-auto reset-height\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1174,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        disabled: message.trim().length === 0,\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation \".concat(message.trim().length === 0 ? \"bg-[#E0E0E0] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                                            className: \"w-4 h-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 1199,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1190,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1170,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1165,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-center py-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"This chat is powered by\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-black\",\n                                                        children: \"Driply.me\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1206,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1204,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1203,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1161,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 1151,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"absolute bottom-0 left-0 right-0 bg-white\",\n                        children: messages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"120ms\" : \"0ms\",\n                                        position: \"relative\",\n                                        zIndex: 10,\n                                        backgroundColor: \"white\",\n                                        paddingBottom: \"10px\"\n                                    },\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"\".concat(isMobile ? \"px-0\" : \"px-4\", \" pt-2 pb-2 transition-all duration-500 ease-in-out \").concat(isTyping ? \"opacity-0 pointer-events-none\" : showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"overflow-hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                ref: carouselRef,\n                                                style: {\n                                                    transform: \"translateX(-\".concat(currentSlide * (isMobile ? 50 : 100), \"%)\"),\n                                                    paddingLeft: isMobile ? \"1rem\" : \"0\"\n                                                },\n                                                onTouchStart: handleTouchStart,\n                                                onTouchMove: handleTouchMove,\n                                                onTouchEnd: handleTouchEnd,\n                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex transition-transform duration-300 ease-in-out\",\n                                                children: isMobile ? (()=>{\n                                                    const circularCards = [\n                                                        ...settingData === null || settingData === void 0 ? void 0 : settingData.suggestedTopics,\n                                                        ...settingData === null || settingData === void 0 ? void 0 : settingData.suggestedTopics\n                                                    ];\n                                                    return circularCards.map((card, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex-shrink-0 mt-3 mr-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleSuggestionClick(card.question),\n                                                                style: {\n                                                                    width: \"fit-content\"\n                                                                },\n                                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"py-3 px-2.5 rounded-[8px] bg-[#F6F6F6] text-left hover:bg-[#D6EDFF] transition-all duration-200 inline-block touch-manipulation\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-[16px] font-[600] text-black mb-0.5 leading-tight whitespace-nowrap overflow-hidden text-ellipsis\",\n                                                                        children: card.question\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                        lineNumber: 1269,\n                                                                        columnNumber: 33\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-[16px] text-gray-500 leading-tight whitespace-nowrap overflow-hidden text-ellipsis\",\n                                                                        children: card.subtitle\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                        lineNumber: 1272,\n                                                                        columnNumber: 33\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                lineNumber: 1262,\n                                                                columnNumber: 31\n                                                            }, undefined)\n                                                        }, \"\".concat(index, \"-\").concat(card.question), false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 1258,\n                                                            columnNumber: 29\n                                                        }, undefined));\n                                                })() : Array.from({\n                                                    length: Math.ceil((settingData === null || settingData === void 0 ? void 0 : settingData.suggestedTopics.length) / 2)\n                                                }).map((_, slideIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"w-full flex-shrink-0 flex gap-2 justify-center\",\n                                                        children: settingData === null || settingData === void 0 ? void 0 : settingData.suggestedTopics.slice(slideIndex * 2, slideIndex * 2 + 2).map((card, cardIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleSuggestionClick(card.question),\n                                                                style: {\n                                                                    width: \"fit-content\"\n                                                                },\n                                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"py-2 px-2.5 rounded-[8px] bg-[#F6F6F6] text-left group hover:bg-[#D6EDFF] transition-all duration-200 inline-block touch-manipulation\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-[14px] font-[600] text-black mb-0.5 leading-tight whitespace-nowrap\",\n                                                                        children: card.question\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                        lineNumber: 1299,\n                                                                        columnNumber: 35\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-[16px] text-gray-500 leading-tight whitespace-nowrap\",\n                                                                        children: card.subtitle\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                        lineNumber: 1302,\n                                                                        columnNumber: 35\n                                                                    }, undefined)\n                                                                ]\n                                                            }, slideIndex * 2 + cardIndex, true, {\n                                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                lineNumber: 1291,\n                                                                columnNumber: 33\n                                                            }, undefined))\n                                                    }, slideIndex, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1284,\n                                                        columnNumber: 27\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1238,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1237,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handlePrevSlide,\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"hidden md:block absolute left-28 top-1/2 transform -translate-y-1/2 -translate-x-6 w-8 h-8 bg-white border border-gray-200 rounded-full items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronLeft, {\n                                                className: \"w-3 h-3 text-gray-600  ms-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1317,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1313,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleNextSlide,\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"hidden md:block absolute right-28 top-1/2 transform -translate-y-1/2 translate-x-6 w-8 h-8 bg-white border border-gray-200 rounded-full items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronRight, {\n                                                className: \"w-3 h-3 text-gray-600 ms-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1323,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1319,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1219,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"200ms\" : \"0ms\",\n                                        position: \"relative\",\n                                        zIndex: 5,\n                                        paddingBottom: \"env(safe-area-inset-bottom)\"\n                                    },\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"px-4 bg-white transition-all duration-500 \".concat(showInitialUI ? \"opacity-100 translate-y-0\" : \"translate-y-4\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"relative\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"relative w-full max-w-[890px] mx-auto\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        ref: messages.length === 0 ? inputRef : null,\n                                                        value: message,\n                                                        onChange: handleInputChange,\n                                                        onKeyDown: handleKeyPress,\n                                                        placeholder: \"Ask anything\",\n                                                        rows: 1,\n                                                        style: {\n                                                            boxSizing: \"border-box\",\n                                                            maxHeight: \"250px\",\n                                                            fontSize: \"16px\",\n                                                            zIndex: 1001,\n                                                            overscrollBehaviorY: \"contain\",\n                                                            position: \"relative\"\n                                                        },\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"w-full min-h-[104px] pt-3 px-4 pb-10 pr-12 text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base resize-none overflow-y-auto reset-height\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1341,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        disabled: message.trim().length === 0,\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation \".concat(message.trim().length === 0 ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                                            className: \"w-4 h-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 1367,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1358,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1340,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1339,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-center py-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"This chat is powered by\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-black\",\n                                                        children: \"Driply.me\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1374,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1372,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1371,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1328,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 1215,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 1103,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"69ac67aa0147cd65\",\n                children: '.loading-dot{-webkit-animation:loading-dots 1.4s infinite ease-in-out both;-moz-animation:loading-dots 1.4s infinite ease-in-out both;-o-animation:loading-dots 1.4s infinite ease-in-out both;animation:loading-dots 1.4s infinite ease-in-out both}.loading-dot:nth-child(1){-webkit-animation-delay:-.32s;-moz-animation-delay:-.32s;-o-animation-delay:-.32s;animation-delay:-.32s}.loading-dot:nth-child(2){-webkit-animation-delay:-.16s;-moz-animation-delay:-.16s;-o-animation-delay:-.16s;animation-delay:-.16s}.loading-dot:nth-child(3){-webkit-animation-delay:0s;-moz-animation-delay:0s;-o-animation-delay:0s;animation-delay:0s}@-webkit-keyframes loading-dots{0%,80%,100%{-webkit-transform:scale(1);transform:scale(1);opacity:1}40%{-webkit-transform:scale(1.5);transform:scale(1.5);opacity:.7}}@-moz-keyframes loading-dots{0%,80%,100%{-moz-transform:scale(1);transform:scale(1);opacity:1}40%{-moz-transform:scale(1.5);transform:scale(1.5);opacity:.7}}@-o-keyframes loading-dots{0%,80%,100%{-o-transform:scale(1);transform:scale(1);opacity:1}40%{-o-transform:scale(1.5);transform:scale(1.5);opacity:.7}}@keyframes loading-dots{0%,80%,100%{-webkit-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1);opacity:1}40%{-webkit-transform:scale(1.5);-moz-transform:scale(1.5);-o-transform:scale(1.5);transform:scale(1.5);opacity:.7}}.hide-scrollbar{-ms-overflow-style:none!important;scrollbar-width:none!important}.hide-scrollbar::-webkit-scrollbar{display:none!important}@media only screen and (max-width:1023px){.lg\\\\\\\\.jsx-69ac67aa0147cd65:hidden .absolute.bottom-0.jsx-69ac67aa0147cd65{position:fixed!important;bottom:env(safe-area-inset-bottom,0);left:0;right:0;width:100%;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;z-index:1000;background-color:white}textarea.jsx-69ac67aa0147cd65{-webkit-user-select:auto!important;-moz-user-select:auto!important;-ms-user-select:auto!important;user-select:auto!important;-webkit-appearance:none;-moz-appearance:none;-ms-appearance:none;appearance:none;-ms-scroll-chaining:none;overscroll-behavior:none;line-height:24px;max-height:250px!important;position:relative!important;-webkit-transform:translatez(0);-moz-transform:translatez(0);-ms-transform:translatez(0);-o-transform:translatez(0);transform:translatez(0);-webkit-backface-visibility:hidden;-moz-backface-visibility:hidden;backface-visibility:hidden;-webkit-perspective:1000;-moz-perspective:1000;perspective:1000;z-index:1001!important;background-color:white}.lg\\\\\\\\.jsx-69ac67aa0147cd65:hidden form.jsx-69ac67aa0147cd65{position:relative!important;z-index:1001;background-color:white}button[type=\"submit\"].jsx-69ac67aa0147cd65{position:absolute!important;right:12px;bottom:12px;z-index:1002!important;-webkit-tap-highlight-color:transparent;-ms-touch-action:-ms-manipulation;-ms-touch-action:manipulation;touch-action:manipulation;-webkit-transform:translatez(0);-moz-transform:translatez(0);-ms-transform:translatez(0);-o-transform:translatez(0);transform:translatez(0)}.lg\\\\\\\\.jsx-69ac67aa0147cd65:hidden .bg-white.jsx-69ac67aa0147cd65{background-color:white!important}}@media only screen and (min-width:1024px){textarea.jsx-69ac67aa0147cd65{max-height:250px!important;overflow-y:auto!important;-ms-scroll-chaining:none;overscroll-behavior:contain}.reset-height.jsx-69ac67aa0147cd65{min-height:104px!important;max-height:250px!important;overflow-y:auto!important}}.fixed-bottom.jsx-69ac67aa0147cd65{position:fixed!important;bottom:0;left:0;right:0;background:white}'\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n        lineNumber: 923,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatInterface, \"10WFbjN3BPzzozjNUmeU8tjFmcw=\", false, function() {\n    return [\n        _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__.useChatContext\n    ];\n});\n_c = ChatInterface;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatInterface);\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ChatInterface.jsx\n"));

/***/ })

});