"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[slug]/page",{

/***/ "(app-pages-browser)/./src/components/ChatInterface.jsx":
/*!******************************************!*\
  !*** ./src/components/ChatInterface.jsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/constants */ \"(app-pages-browser)/./src/utils/constants.js\");\n/* harmony import */ var _utils_config__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/config */ \"(app-pages-browser)/./src/utils/config.js\");\n/* harmony import */ var _barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FaArrowUp,FaChevronLeft,FaChevronRight!=!react-icons/fa6 */ \"(app-pages-browser)/./node_modules/react-icons/fa6/index.mjs\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../contexts/ChatContext */ \"(app-pages-browser)/./src/contexts/ChatContext.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst ChatInterface = (param)=>{\n    let { slug, query } = param;\n    _s();\n    // Context API\n    const { metadata, updateMetadata, setLoadingState, setErrorState, clearError, getCustomerName, getBusinessName, hasMetadata, loading: contextLoading, error: contextError } = (0,_contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__.useChatContext)();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [currentSlide, setCurrentSlide] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [touchStart, setTouchStart] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [touchEnd, setTouchEnd] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isTablet, setIsTablet] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showInitialUI, setShowInitialUI] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isBotTyping, setIsBotTyping] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isBotThinking, setIsBotThinking] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [sseConnection, setSseConnection] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const carouselRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const messagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const messageElementsRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(new Map()); // Store refs to individual messages\n    // Detect iOS for targeted fixes\n    const isIOS = ()=>{\n        if (true) {\n            return /iPhone|iPad|iPod/i.test(navigator.userAgent);\n        }\n        return false;\n    };\n    const username = slug;\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (username && \"object\" !== \"undefined\") {\n                fetchCustomer(username);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        username\n    ]);\n    const fetchCustomer = async (customerName)=>{\n        try {\n            setLoadingState(true);\n            clearError();\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"\".concat(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_SETTINGS, \"?customerName=\").concat(customerName));\n            if (response.data && response.data.customerName) {\n                const metadataFromResponse = response.data.metaData;\n                console.log('Storing metadata in context:', metadataFromResponse);\n                updateMetadata(metadataFromResponse);\n                // Keep existing localStorage for backward compatibility\n                localStorage.setItem(\"customerName_userId\", response.data.customerName);\n                localStorage.setItem(\"BusinessName\", response.data.businessName);\n                window.dispatchEvent(new CustomEvent('businessNameLoaded', {\n                    detail: {\n                        businessName: response.data.businessName\n                    }\n                }));\n                const existingUserId = localStorage.getItem(\"userID\");\n                if (existingUserId) {\n                    await fetchExistingMessages(existingUserId);\n                } else {\n                    await initializeChatSession();\n                }\n            } else {\n                await initializeChatSession();\n            }\n        } catch (error) {\n            console.error('Error fetching customer data:', error);\n            setErrorState('Failed to load customer settings');\n            // Fallback metadata\n            updateMetadata({\n                customerName: customerName,\n                businessName: \"Driply\"\n            });\n            localStorage.setItem(\"BusinessName\", \"Driply\");\n            window.dispatchEvent(new CustomEvent('businessNameLoaded', {\n                detail: {\n                    businessName: \"Driply\"\n                }\n            }));\n            await initializeChatSession();\n        } finally{\n            setLoadingState(false);\n        }\n    };\n    const fetchExistingMessages = async (userId)=>{\n        console.log(\"metadata : \", metadata);\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"\".concat(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_MESSAGES, \"?userId=\").concat(userId));\n            if (response.data && response.data.length > 0) {\n                setMessages(response.data.map((msg)=>({\n                        text: msg.message,\n                        timestamp: new Date(msg.createdAt).getTime(),\n                        type: msg.type,\n                        source: msg.source\n                    })));\n            }\n            connectToSSE(userId);\n        } catch (error) {\n            await initializeChatSession();\n        }\n    };\n    const initializeChatSession = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_INIT, {\n                customerName: username\n            });\n            if (response.data && response.data.userId) {\n                localStorage.setItem(\"userId\", response.data.userId);\n                localStorage.setItem(\"userID\", response.data._id);\n                await fetchExistingMessages(response.data._id);\n            }\n        } catch (error) {\n            if (!localStorage.getItem(\"BusinessName\")) {\n                localStorage.setItem(\"BusinessName\", \"Driply\");\n                window.dispatchEvent(new CustomEvent('businessNameLoaded', {\n                    detail: {\n                        businessName: \"Driply\"\n                    }\n                }));\n            }\n        }\n    };\n    const connectToSSE = (userId)=>{\n        try {\n            if (sseConnection) {\n                sseConnection.close();\n            }\n            const sseUrl = (0,_utils_config__WEBPACK_IMPORTED_MODULE_4__.getSSEUrl)(userId);\n            const eventSource = new EventSource(sseUrl);\n            setSseConnection(eventSource);\n            eventSource.addEventListener(\"message\", (param)=>{\n                let { data } = param;\n                try {\n                    const contents = JSON.parse(data);\n                    handleSSEMessage(contents);\n                } catch (error) {\n                // Handle parsing error silently\n                }\n            });\n            eventSource.addEventListener(\"error\", ()=>{\n            // Handle connection error silently\n            });\n        } catch (error) {\n        // Handle SSE connection error silently\n        }\n    };\n    const handleSSEMessage = (data)=>{\n        const subtype = data.subType || data.subtype || data.type || \"UNKNOWN\";\n        const content = data.content || \"\";\n        const message = data.message || data.text || \"\";\n        if ([\n            \"TYPING\",\n            \"THINKING\",\n            \"BEHAVIOUR_MESSAGE\"\n        ].includes(subtype)) {\n            switch(subtype){\n                case \"TYPING\":\n                    setIsBotTyping(true);\n                    setIsBotThinking(false);\n                    break;\n                case \"THINKING\":\n                    setIsBotThinking(true);\n                    setIsBotTyping(false);\n                    break;\n                case \"BEHAVIOUR_MESSAGE\":\n                    setIsBotTyping(false);\n                    setIsBotThinking(false);\n                    break;\n            }\n            return;\n        }\n        const displayText = message || content;\n        if (!displayText) return;\n        const displayType = [\n            \"TEXT\",\n            \"MESSAGE\",\n            \"DATA_MESSAGE\"\n        ].includes(subtype) ? subtype : \"UNKNOWN\";\n        setMessages((prev)=>[\n                ...prev,\n                {\n                    text: displayText,\n                    timestamp: Date.now(),\n                    type: displayType,\n                    source: \"BOT\"\n                }\n            ]);\n        setIsBotTyping(false);\n        setIsBotThinking(false);\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (message.trim()) {\n            const userMessage = message.trim();\n            setMessages((prev)=>[\n                    ...prev,\n                    {\n                        text: userMessage,\n                        timestamp: Date.now(),\n                        type: \"TEXT\",\n                        source: \"USER\"\n                    }\n                ]);\n            setMessage(\"\");\n            setIsTyping(false);\n            // Immediate reset to \"first message\" view for user message\n            setTimeout(()=>resetToFirstMessageView(), 50);\n            // Follow up with full single-message positioning after DOM update\n            setTimeout(()=>scrollToSingleMessageView(), 150);\n            // Show loading dots immediately after sending message\n            setIsBotThinking(true);\n            setIsBotTyping(false);\n            // Reset textarea\n            if (inputRef.current) {\n                inputRef.current.style.height = \"104px\";\n                inputRef.current.scrollTop = 0;\n                inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n                inputRef.current.classList.add(\"reset-height\");\n            }\n            try {\n                const userId = localStorage.getItem(\"userID\");\n                if (userId) {\n                    await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_SEND, {\n                        userId: userId,\n                        message: userMessage,\n                        \"typs\": \"TEXT\",\n                        \"source\": \"USER\",\n                        \"isTest\": query.isTest === '1' ? true : false\n                    });\n                }\n            } catch (error) {\n                // Hide loading dots on error\n                setIsBotThinking(false);\n                setIsBotTyping(false);\n                setMessages((prev)=>[\n                        ...prev,\n                        {\n                            text: \"Sorry, there was an error sending your message. Please try again.\",\n                            timestamp: Date.now(),\n                            type: \"TEXT\",\n                            source: \"BOT\"\n                        }\n                    ]);\n            }\n        }\n    };\n    const handleTextareaResize = (textarea)=>{\n        if (textarea) {\n            textarea.style.height = \"104px\";\n            const scrollHeight = textarea.scrollHeight;\n            if (isIOS()) {\n                if (scrollHeight > 48) {\n                    textarea.style.overflowY = \"auto\";\n                } else {\n                    textarea.style.overflowY = \"hidden\";\n                }\n            } else {\n                const newHeight = Math.min(250, Math.max(104, scrollHeight));\n                textarea.style.height = \"\".concat(newHeight, \"px\");\n                textarea.style.overflowY = scrollHeight > newHeight ? \"auto\" : \"hidden\";\n            }\n        }\n    };\n    const handleInputChange = (e)=>{\n        const value = e.target.value;\n        setMessage(value);\n        handleTextareaResize(e.target);\n        if (messages.length === 0) {\n            setIsTyping(value.length > 0);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length > 0 && inputRef.current && !message) {\n                const timer = setTimeout({\n                    \"ChatInterface.useEffect.timer\": ()=>{\n                        if (inputRef.current) {\n                            inputRef.current.style.height = \"104px\";\n                            inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n                            inputRef.current.classList.add(\"reset-height\");\n                        }\n                    }\n                }[\"ChatInterface.useEffect.timer\"], 50);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                })[\"ChatInterface.useEffect\"];\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages,\n        message\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length > 0) {\n                // Trigger single-message focus view for each new message\n                scrollToSingleMessageView();\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages,\n        isBotTyping,\n        isBotThinking\n    ]);\n    // Single-message focus scrolling system\n    const calculateMessageHeights = ()=>{\n        var _messagesContainerRef_current;\n        const messageHeights = [];\n        const messageElements = (_messagesContainerRef_current = messagesContainerRef.current) === null || _messagesContainerRef_current === void 0 ? void 0 : _messagesContainerRef_current.querySelectorAll('.message-item');\n        if (!messageElements) return [];\n        messageElements.forEach((element, index)=>{\n            const height = element.getBoundingClientRect().height;\n            messageHeights.push({\n                index,\n                height,\n                element\n            });\n        });\n        return messageHeights;\n    };\n    const calculateFirstMessagePosition = ()=>{\n        const container = messagesContainerRef.current;\n        if (!container) return 0;\n        const messageElements = container.querySelectorAll('.message-item');\n        if (messageElements.length === 0) return 0;\n        // CRITICAL FIX: For \"first message\" appearance, we need to scroll to show\n        // the LATEST message at the TOP of the container, not at the bottom!\n        // Find the last message element (current exchange)\n        const lastMessageElement = messageElements[messageElements.length - 1];\n        if (!lastMessageElement) return 0;\n        // Get the position of the last message relative to the container\n        const containerRect = container.getBoundingClientRect();\n        const messageRect = lastMessageElement.getBoundingClientRect();\n        // Calculate how much we need to scroll to bring the last message to the TOP\n        const messageOffsetFromContainerTop = messageRect.top - containerRect.top;\n        const currentScrollTop = container.scrollTop;\n        // Target: Position the latest message at the very top of the visible area\n        const targetScrollTop = currentScrollTop + messageOffsetFromContainerTop;\n        console.log('🎯 First Message Positioning:', {\n            messageOffsetFromContainerTop,\n            currentScrollTop,\n            targetScrollTop,\n            action: 'Moving latest message to TOP of container'\n        });\n        return Math.max(0, targetScrollTop);\n    };\n    const getCurrentExchangeHeight = (messageHeights)=>{\n        if (messageHeights.length === 0) return 0;\n        // Determine what constitutes the \"current exchange\" based on message state\n        let exchangeHeight = 0;\n        const spacingBetweenMessages = 24; // matches space-y-6 (1.5rem)\n        // If bot is typing or thinking, include typing indicator\n        if (isBotTyping || isBotThinking) {\n            // Show: last user message + typing indicator\n            if (messageHeights.length >= 1) {\n                var _messageHeights_;\n                const lastUserMessageHeight = ((_messageHeights_ = messageHeights[messageHeights.length - 1]) === null || _messageHeights_ === void 0 ? void 0 : _messageHeights_.height) || 0;\n                const typingIndicatorHeight = 60; // estimated height for typing dots\n                exchangeHeight = lastUserMessageHeight + typingIndicatorHeight + spacingBetweenMessages;\n            }\n        } else {\n            // Show: current user-bot message pair\n            if (messageHeights.length >= 2) {\n                var _messageHeights_1, _messageHeights_2;\n                // Last two messages (user + bot response)\n                const lastBotHeight = ((_messageHeights_1 = messageHeights[messageHeights.length - 1]) === null || _messageHeights_1 === void 0 ? void 0 : _messageHeights_1.height) || 0;\n                const lastUserHeight = ((_messageHeights_2 = messageHeights[messageHeights.length - 2]) === null || _messageHeights_2 === void 0 ? void 0 : _messageHeights_2.height) || 0;\n                exchangeHeight = lastUserHeight + lastBotHeight + spacingBetweenMessages;\n            } else if (messageHeights.length === 1) {\n                // Only user message (bot hasn't responded yet)\n                exchangeHeight = messageHeights[0].height;\n            }\n        }\n        // Add some padding at the top for better visual appearance\n        const topPadding = 20;\n        return exchangeHeight + topPadding;\n    };\n    const scrollToFirstMessageView = ()=>{\n        setTimeout(()=>{\n            const container = messagesContainerRef.current;\n            if (!container) {\n                console.log('❌ Container not found');\n                return;\n            }\n            const targetScrollTop = calculateFirstMessagePosition();\n            console.log('🎯 FIRST MESSAGE VIEW - Latest message moving to TOP:', {\n                containerHeight: container.clientHeight,\n                totalMessages: messages.length,\n                targetScrollTop,\n                currentScrollTop: container.scrollTop,\n                action: '⬆️ Moving latest message to TOP of visible area (like first message)'\n            });\n            container.scrollTo({\n                top: targetScrollTop,\n                behavior: 'smooth'\n            });\n        }, 200); // Allow DOM to update\n    };\n    // Immediate positioning for \"first message\" appearance\n    const resetToTopPosition = ()=>{\n        const container = messagesContainerRef.current;\n        if (!container) return;\n        // Quick positioning to move latest message toward top\n        const targetScrollTop = calculateFirstMessagePosition();\n        console.log('⚡ IMMEDIATE TOP POSITIONING:', {\n            targetScrollTop,\n            totalMessages: messages.length,\n            action: '🚀 Quick move to top position'\n        });\n        container.scrollTo({\n            top: targetScrollTop,\n            behavior: 'smooth'\n        });\n    };\n    const renderMessages = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                messages.map((msg, index)=>{\n                    const messageId = \"\".concat(msg.timestamp, \"-\").concat(index);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: (el)=>{\n                            if (el) {\n                                messageElementsRef.current.set(messageId, el);\n                            } else {\n                                messageElementsRef.current.delete(messageId);\n                            }\n                        },\n                        className: \"message-item flex \".concat(msg.source === 'USER' ? 'justify-end' : 'justify-start'),\n                        \"data-message-index\": index,\n                        \"data-message-id\": messageId,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-[16px] font-[400] rounded-3xl max-w-xs lg:max-w-lg break-words hyphens-auto \".concat(msg.source === 'USER' ? 'bg-gray-100' : 'bg-white', \" \").concat(isMobile ? 'rounded-[15px] px-3 py-2' : 'px-4 py-3'),\n                            style: {\n                                overflowWrap: \"break-word\",\n                                wordBreak: \"break-word\",\n                                maxWidth: isMobile ? \"90%\" : undefined\n                            },\n                            children: msg.text\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 493,\n                            columnNumber: 15\n                        }, undefined)\n                    }, messageId, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 480,\n                        columnNumber: 13\n                    }, undefined);\n                }),\n                (isBotTyping || isBotThinking) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"message-item flex justify-start\",\n                    \"data-message-type\": \"loading\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white \".concat(isMobile ? 'px-3 py-2 rounded-[15px]' : 'px-4 py-3 rounded-3xl'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-black rounded-full loading-dot\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 519,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-black rounded-full loading-dot\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-black rounded-full loading-dot\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 521,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 518,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 517,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                    lineNumber: 513,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n            lineNumber: 476,\n            columnNumber: 7\n        }, undefined);\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSubmit(e);\n        }\n    };\n    const handleSuggestionClick = async (cardTitle)=>{\n        setMessages((prev)=>[\n                ...prev,\n                {\n                    text: cardTitle,\n                    timestamp: Date.now(),\n                    type: \"TEXT\",\n                    source: \"USER\"\n                }\n            ]);\n        setMessage(\"\");\n        setIsTyping(false);\n        // Immediate reset to \"first message\" view for suggestion click\n        setTimeout(()=>resetToFirstMessageView(), 50);\n        // Follow up with full single-message positioning\n        setTimeout(()=>scrollToSingleMessageView(), 150);\n        // Show loading dots immediately after sending message\n        setIsBotThinking(true);\n        setIsBotTyping(false);\n        if (inputRef.current) {\n            inputRef.current.style.height = \"104px\";\n            inputRef.current.scrollTop = 0;\n            inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n            inputRef.current.classList.add(\"reset-height\");\n        }\n        try {\n            const userId = localStorage.getItem(\"userID\");\n            if (userId) {\n                await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_SEND, {\n                    userId: userId,\n                    message: cardTitle,\n                    customerName: username\n                });\n            }\n        } catch (error) {\n            // Hide loading dots on error\n            setIsBotThinking(false);\n            setIsBotTyping(false);\n            setMessages((prev)=>[\n                    ...prev,\n                    {\n                        text: \"Sorry, there was an error sending your message. Please try again.\",\n                        timestamp: Date.now(),\n                        type: \"TEXT\",\n                        source: \"BOT\"\n                    }\n                ]);\n        }\n    };\n    const getMaxSlides = ()=>{\n        if (isMobile) return _utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.length;\n        return Math.ceil(_utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.length / 2);\n    };\n    const nextSlide = ()=>{\n        if (isMobile) {\n            setCurrentSlide((prev)=>prev < _utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.length - 1 ? prev + 1 : prev);\n        } else {\n            const maxSlides = getMaxSlides();\n            setCurrentSlide((prev)=>(prev + 1) % maxSlides);\n        }\n    };\n    const prevSlide = ()=>{\n        if (isMobile) {\n            setCurrentSlide((prev)=>prev > 0 ? prev - 1 : prev);\n        } else {\n            const maxSlides = getMaxSlides();\n            setCurrentSlide((prev)=>(prev - 1 + maxSlides) % maxSlides);\n        }\n    };\n    const handleNextSlide = ()=>{\n        nextSlide();\n    };\n    const handlePrevSlide = ()=>{\n        prevSlide();\n    };\n    const handleTouchStart = (e)=>{\n        setTouchStart(e.targetTouches[0].clientX);\n    };\n    const handleTouchMove = (e)=>{\n        setTouchEnd(e.targetTouches[0].clientX);\n    };\n    const handleTouchEnd = ()=>{\n        if (!touchStart || !touchEnd) return;\n        const swipeDistance = touchStart - touchEnd;\n        const minSwipeDistance = 50;\n        if (swipeDistance > minSwipeDistance) {\n            nextSlide();\n        }\n        if (swipeDistance < -minSwipeDistance) {\n            prevSlide();\n        }\n        setTouchStart(0);\n        setTouchEnd(0);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length === 0) {\n                setShowInitialUI(false);\n                const timer = setTimeout({\n                    \"ChatInterface.useEffect.timer\": ()=>setShowInitialUI(true)\n                }[\"ChatInterface.useEffect.timer\"], 60);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                })[\"ChatInterface.useEffect\"];\n            } else {\n                setShowInitialUI(false);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages.length\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const checkScreenSize = {\n                \"ChatInterface.useEffect.checkScreenSize\": ()=>{\n                    if (true) {\n                        const width = window.innerWidth;\n                        setIsMobile(width < 768);\n                        setIsTablet(width >= 768 && width < 1024);\n                    }\n                }\n            }[\"ChatInterface.useEffect.checkScreenSize\"];\n            checkScreenSize();\n            const handleResize = {\n                \"ChatInterface.useEffect.handleResize\": ()=>{\n                    setCurrentSlide(0);\n                    checkScreenSize();\n                }\n            }[\"ChatInterface.useEffect.handleResize\"];\n            if (true) {\n                window.addEventListener(\"resize\", handleResize);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>window.removeEventListener(\"resize\", handleResize)\n                })[\"ChatInterface.useEffect\"];\n            }\n        }\n    }[\"ChatInterface.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (inputRef.current && messages.length === 0) {\n                const shouldAutoFocus = window.innerWidth >= 768;\n                if (shouldAutoFocus) {\n                    const timer = setTimeout({\n                        \"ChatInterface.useEffect.timer\": ()=>{\n                            var _inputRef_current;\n                            (_inputRef_current = inputRef.current) === null || _inputRef_current === void 0 ? void 0 : _inputRef_current.focus();\n                        }\n                    }[\"ChatInterface.useEffect.timer\"], 100);\n                    return ({\n                        \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                    })[\"ChatInterface.useEffect\"];\n                }\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages.length\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            return ({\n                \"ChatInterface.useEffect\": ()=>{\n                    if (sseConnection) {\n                        sseConnection.close();\n                    }\n                }\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], [\n        sseConnection\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"bg-white flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"hidden lg:flex flex-1 flex-col px-4 \",\n                children: messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"flex flex-1 items-center justify-center min-h-[calc(100vh-64px)] mt-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"flex flex-col items-center w-[768px] justify-center mx-auto \",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    transitionDelay: showInitialUI ? \"40ms\" : \"0ms\"\n                                },\n                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-4xl text-gray-900 mb-6 text-center transition-all duration-700 \".concat(showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                children: \"How can I help you?\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 707,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                style: {\n                                    transitionDelay: showInitialUI ? \"120ms\" : \"0ms\"\n                                },\n                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"relative w-full mb-6 flex flex-col items-center transition-all duration-500 \".concat(showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        ref: messages.length === 0 ? inputRef : null,\n                                        value: message,\n                                        onChange: handleInputChange,\n                                        onKeyDown: handleKeyPress,\n                                        placeholder: \"Ask anything\",\n                                        autoFocus: messages.length === 0,\n                                        rows: 1,\n                                        style: {\n                                            boxSizing: \"border-box\",\n                                            zIndex: 1001,\n                                            position: \"relative\"\n                                        },\n                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"pt-3 px-4 lg:p-4 pb-16 pr-12 w-full min-h-[104px] max-h-[250px] overflow-y-auto hide-scrollbar text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base shadow-md resize-none reset-height\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 726,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: message.trim().length === 0,\n                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"absolute right-3 bottom-3 w-9 h-9 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation \".concat(message.trim().length === 0 ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                            className: \"w-4 h-4 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 749,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 741,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 718,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    transitionDelay: showInitialUI ? \"200ms\" : \"0ms\",\n                                    zIndex: 10\n                                },\n                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"relative w-full max-w-2xl transition-all duration-500 ease-in-out \".concat(isTyping ? \"opacity-0 pointer-events-none\" : showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: carouselRef,\n                                            style: {\n                                                transform: \"translateX(-\".concat(currentSlide * 100, \"%)\")\n                                            },\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"flex transition-transform duration-300 ease-in-out\",\n                                            children: Array.from({\n                                                length: Math.ceil(_utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.length / 2)\n                                            }).map((_, slideIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"w-full flex-shrink-0 flex gap-2 justify-center\",\n                                                    children: _utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.slice(slideIndex * 2, slideIndex * 2 + 2).map((card, cardIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleSuggestionClick(card.title),\n                                                            style: {\n                                                                width: \"fit-content\"\n                                                            },\n                                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"py-2 px-3 rounded-[8px] bg-[#f6f6f6] text-left group transition-all duration-200 touch-manipulation\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-[16px] font-[600] text-black mb-0.5 leading-tight whitespace-nowrap\",\n                                                                    children: card.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                    lineNumber: 787,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-[16px] text-gray-500 leading-tight whitespace-nowrap\",\n                                                                    children: card.subtitle\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                    lineNumber: 790,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, slideIndex * 2 + cardIndex, true, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 781,\n                                                            columnNumber: 27\n                                                        }, undefined))\n                                                }, slideIndex, false, {\n                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                    lineNumber: 773,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 765,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 764,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handlePrevSlide,\n                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"absolute left-28 top-1/2 transform -translate-y-1/2 -translate-x-6 w-8 h-8 bg-white border border-gray-200 rounded-full flex items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronLeft, {\n                                            className: \"w-3 h-3 text-gray-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 803,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 799,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleNextSlide,\n                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"absolute right-28 top-1/2 transform -translate-y-1/2 translate-x-6 w-8 h-8 bg-white border border-gray-200 rounded-full flex items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronRight, {\n                                            className: \"w-3 h-3 text-gray-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 809,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 805,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 752,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 706,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                    lineNumber: 705,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"h-screen flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: messagesContainerRef,\n                            style: {\n                                height: \"calc(100vh - 60px - 180px)\",\n                                marginTop: \"60px\",\n                                overscrollBehavior: \"contain\"\n                            },\n                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"overflow-y-auto hide-scrollbar bg-white\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"w-full lg:w-[768px] mx-auto px-4 py-4\",\n                                children: [\n                                    renderMessages(),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: messagesEndRef,\n                                        className: \"jsx-4bbd75c2920167f7\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 828,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 826,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 817,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                height: \"180px\",\n                                position: \"relative\",\n                                zIndex: 1000,\n                                flexShrink: 0\n                            },\n                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"bg-white border-t border-gray-100\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSubmit,\n                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"relative w-full lg:w-[768px] mx-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                ref: messages.length > 0 ? inputRef : null,\n                                                value: message,\n                                                onChange: handleInputChange,\n                                                onKeyDown: handleKeyPress,\n                                                placeholder: \"Ask anything\",\n                                                rows: 1,\n                                                style: {\n                                                    boxSizing: \"border-box\",\n                                                    zIndex: 1001,\n                                                    position: \"relative\"\n                                                },\n                                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"pt-3 px-4 md:px-5 pb-10 pr-12 w-full min-h-[104px] max-h-[250px] overflow-y-auto text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base shadow-lg resize-none reset-height hide-scrollbar\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 847,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"submit\",\n                                                disabled: message.trim().length === 0,\n                                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation \".concat(message.trim().length === 0 ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                                    className: \"w-4 h-4 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                    lineNumber: 869,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 861,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 843,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-center mt-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-xs text-gray-500\",\n                                            children: [\n                                                \"This chat is powered by\",\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-black\",\n                                                    children: \"Driply.me\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                    lineNumber: 875,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 873,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 872,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 842,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 833,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                    lineNumber: 815,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 703,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"lg:hidden h-screen flex flex-col\",\n                children: [\n                    messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"flex flex-col items-center pt-15 pb-4 px-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"hidden md:flex lg:hidden flex-col items-center\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 889,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 888,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    paddingBottom: \"260px\"\n                                },\n                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"flex flex-col items-center justify-center flex-1 px-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"40ms\" : \"0ms\"\n                                    },\n                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-2xl md:text-4xl text-gray-900 text-center leading-relaxed transition-all duration-700 \".concat(showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                    children: \"How can I help you?\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 895,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 891,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"h-screen flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: messagesContainerRef,\n                                style: {\n                                    height: isMobile ? \"calc(100vh - 60px - 280px)\" // mobile: navbar(60px) + input(280px)\n                                     : \"calc(100vh - 60px - 200px)\",\n                                    marginTop: \"60px\",\n                                    overscrollBehavior: \"contain\"\n                                },\n                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"overflow-y-auto hide-scrollbar bg-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"w-full max-w-[803px] mx-auto px-4 py-4\",\n                                    children: [\n                                        renderMessages(),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: messagesEndRef,\n                                            className: \"jsx-4bbd75c2920167f7\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 923,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 921,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 910,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    height: isMobile ? \"280px\" : \"200px\",\n                                    position: \"relative\",\n                                    zIndex: 1000,\n                                    flexShrink: 0,\n                                    paddingBottom: \"env(safe-area-inset-bottom, 0)\"\n                                },\n                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"bg-white border-t border-gray-100\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        transform: \"translateZ(0)\"\n                                    },\n                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"px-4 py-4 bg-white\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            style: {\n                                                zIndex: 1001\n                                            },\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"relative bg-white\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    transform: \"translateZ(0)\"\n                                                },\n                                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"relative w-full max-w-[890px] mx-auto bg-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        ref: messages.length > 0 ? inputRef : null,\n                                                        value: message,\n                                                        onChange: handleInputChange,\n                                                        onKeyDown: handleKeyPress,\n                                                        placeholder: \"Ask anything\",\n                                                        rows: 1,\n                                                        style: {\n                                                            boxSizing: \"border-box\",\n                                                            maxHeight: \"250px\",\n                                                            zIndex: 1001,\n                                                            overscrollBehavior: \"none\",\n                                                            position: \"relative\"\n                                                        },\n                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"w-full min-h-[104px] pt-3 px-4 pb-10 pr-12 text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base resize-none overflow-y-auto reset-height\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 951,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        disabled: message.trim().length === 0,\n                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation \".concat(message.trim().length === 0 ? \"bg-[#E0E0E0] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                                            className: \"w-4 h-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 975,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 967,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 947,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 942,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-center py-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"This chat is powered by\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-black\",\n                                                        children: \"Driply.me\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 982,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 980,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 979,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 938,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 928,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 908,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"absolute bottom-0 left-0 right-0 bg-white\",\n                        children: messages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"120ms\" : \"0ms\",\n                                        position: \"relative\",\n                                        zIndex: 10,\n                                        backgroundColor: \"white\",\n                                        paddingBottom: \"10px\"\n                                    },\n                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"\".concat(isMobile ? \"px-0\" : \"px-4\", \" pt-2 pb-2 transition-all duration-500 ease-in-out \").concat(isTyping ? \"opacity-0 pointer-events-none\" : showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"overflow-hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                ref: carouselRef,\n                                                style: {\n                                                    transform: \"translateX(-\".concat(currentSlide * (isMobile ? 50 : 100), \"%)\"),\n                                                    paddingLeft: isMobile ? \"1rem\" : \"0\"\n                                                },\n                                                onTouchStart: handleTouchStart,\n                                                onTouchMove: handleTouchMove,\n                                                onTouchEnd: handleTouchEnd,\n                                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"flex transition-transform duration-300 ease-in-out\",\n                                                children: isMobile ? (()=>{\n                                                    const circularCards = [\n                                                        ..._utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS,\n                                                        ..._utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS\n                                                    ];\n                                                    return circularCards.map((card, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"flex-shrink-0 mt-3 mr-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleSuggestionClick(card.title),\n                                                                style: {\n                                                                    width: \"fit-content\"\n                                                                },\n                                                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"py-3 px-2.5 rounded-[8px] bg-[#F6F6F6] text-left hover:bg-[#D6EDFF] transition-all duration-200 inline-block touch-manipulation\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-[16px] font-[600] text-black mb-0.5 leading-tight whitespace-nowrap overflow-hidden text-ellipsis\",\n                                                                        children: card.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                        lineNumber: 1042,\n                                                                        columnNumber: 31\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-[16px] text-gray-500 leading-tight whitespace-nowrap overflow-hidden text-ellipsis\",\n                                                                        children: card.subtitle\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                        lineNumber: 1045,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                lineNumber: 1035,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        }, \"\".concat(index, \"-\").concat(card.title), false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 1031,\n                                                            columnNumber: 27\n                                                        }, undefined));\n                                                })() : Array.from({\n                                                    length: Math.ceil(_utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.length / 2)\n                                                }).map((_, slideIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"w-full flex-shrink-0 flex gap-2 justify-center\",\n                                                        children: _utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.slice(slideIndex * 2, slideIndex * 2 + 2).map((card, cardIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleSuggestionClick(card.title),\n                                                                style: {\n                                                                    width: \"fit-content\"\n                                                                },\n                                                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"py-2 px-2.5 rounded-[8px] bg-[#F6F6F6] text-left group hover:bg-[#D6EDFF] transition-all duration-200 inline-block touch-manipulation\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-[14px] font-[600] text-black mb-0.5 leading-tight whitespace-nowrap\",\n                                                                        children: card.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                        lineNumber: 1071,\n                                                                        columnNumber: 31\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-[16px] text-gray-500 leading-tight whitespace-nowrap\",\n                                                                        children: card.subtitle\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                        lineNumber: 1074,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                ]\n                                                            }, slideIndex * 2 + cardIndex, true, {\n                                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                lineNumber: 1063,\n                                                                columnNumber: 29\n                                                            }, undefined))\n                                                    }, slideIndex, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1055,\n                                                        columnNumber: 25\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1012,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1011,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handlePrevSlide,\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"hidden md:block absolute left-28 top-1/2 transform -translate-y-1/2 -translate-x-6 w-8 h-8 bg-white border border-gray-200 rounded-full items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronLeft, {\n                                                className: \"w-3 h-3 text-gray-600  ms-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1089,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1085,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleNextSlide,\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"hidden md:block absolute right-28 top-1/2 transform -translate-y-1/2 translate-x-6 w-8 h-8 bg-white border border-gray-200 rounded-full items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronRight, {\n                                                className: \"w-3 h-3 text-gray-600 ms-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1095,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1091,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 995,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"200ms\" : \"0ms\",\n                                        position: \"relative\",\n                                        zIndex: 5,\n                                        paddingBottom: \"env(safe-area-inset-bottom)\"\n                                    },\n                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"px-4 bg-white transition-all duration-500 \".concat(showInitialUI ? \"opacity-100 translate-y-0\" : \"translate-y-4\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"relative\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"relative w-full max-w-[890px] mx-auto\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        ref: messages.length === 0 ? inputRef : null,\n                                                        value: message,\n                                                        onChange: handleInputChange,\n                                                        onKeyDown: handleKeyPress,\n                                                        placeholder: \"Ask anything\",\n                                                        rows: 1,\n                                                        style: {\n                                                            boxSizing: \"border-box\",\n                                                            maxHeight: \"250px\",\n                                                            fontSize: \"16px\",\n                                                            zIndex: 1001,\n                                                            overscrollBehaviorY: \"contain\",\n                                                            position: \"relative\"\n                                                        },\n                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"w-full min-h-[104px] pt-3 px-4 pb-10 pr-12 text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base resize-none overflow-y-auto reset-height\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1112,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        disabled: message.trim().length === 0,\n                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation \".concat(message.trim().length === 0 ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                                            className: \"w-4 h-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 1137,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1129,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1111,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1110,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-center py-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"This chat is powered by\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-black\",\n                                                        children: \"Driply.me\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1144,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1142,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1141,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1100,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 991,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 885,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"4bbd75c2920167f7\",\n                children: '.loading-dot{-webkit-animation:loading-dots 1.4s infinite ease-in-out both;-moz-animation:loading-dots 1.4s infinite ease-in-out both;-o-animation:loading-dots 1.4s infinite ease-in-out both;animation:loading-dots 1.4s infinite ease-in-out both}.loading-dot:nth-child(1){-webkit-animation-delay:-.32s;-moz-animation-delay:-.32s;-o-animation-delay:-.32s;animation-delay:-.32s}.loading-dot:nth-child(2){-webkit-animation-delay:-.16s;-moz-animation-delay:-.16s;-o-animation-delay:-.16s;animation-delay:-.16s}.loading-dot:nth-child(3){-webkit-animation-delay:0s;-moz-animation-delay:0s;-o-animation-delay:0s;animation-delay:0s}@-webkit-keyframes loading-dots{0%,80%,100%{-webkit-transform:scale(1);transform:scale(1);opacity:1}40%{-webkit-transform:scale(1.5);transform:scale(1.5);opacity:.7}}@-moz-keyframes loading-dots{0%,80%,100%{-moz-transform:scale(1);transform:scale(1);opacity:1}40%{-moz-transform:scale(1.5);transform:scale(1.5);opacity:.7}}@-o-keyframes loading-dots{0%,80%,100%{-o-transform:scale(1);transform:scale(1);opacity:1}40%{-o-transform:scale(1.5);transform:scale(1.5);opacity:.7}}@keyframes loading-dots{0%,80%,100%{-webkit-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1);opacity:1}40%{-webkit-transform:scale(1.5);-moz-transform:scale(1.5);-o-transform:scale(1.5);transform:scale(1.5);opacity:.7}}.hide-scrollbar{-ms-overflow-style:none!important;scrollbar-width:none!important}.hide-scrollbar::-webkit-scrollbar{display:none!important}@media only screen and (max-width:1023px){.lg\\\\\\\\.jsx-4bbd75c2920167f7:hidden .absolute.bottom-0.jsx-4bbd75c2920167f7{position:fixed!important;bottom:env(safe-area-inset-bottom,0);left:0;right:0;width:100%;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;z-index:1000;background-color:white}textarea.jsx-4bbd75c2920167f7{-webkit-user-select:auto!important;-moz-user-select:auto!important;-ms-user-select:auto!important;user-select:auto!important;-webkit-appearance:none;-moz-appearance:none;-ms-appearance:none;appearance:none;-ms-scroll-chaining:none;overscroll-behavior:none;line-height:24px;max-height:250px!important;position:relative!important;-webkit-transform:translatez(0);-moz-transform:translatez(0);-ms-transform:translatez(0);-o-transform:translatez(0);transform:translatez(0);-webkit-backface-visibility:hidden;-moz-backface-visibility:hidden;backface-visibility:hidden;-webkit-perspective:1000;-moz-perspective:1000;perspective:1000;z-index:1001!important;background-color:white}.lg\\\\\\\\.jsx-4bbd75c2920167f7:hidden form.jsx-4bbd75c2920167f7{position:relative!important;z-index:1001;background-color:white}button[type=\"submit\"].jsx-4bbd75c2920167f7{position:absolute!important;right:12px;bottom:12px;z-index:1002!important;-webkit-tap-highlight-color:transparent;-ms-touch-action:-ms-manipulation;-ms-touch-action:manipulation;touch-action:manipulation;-webkit-transform:translatez(0);-moz-transform:translatez(0);-ms-transform:translatez(0);-o-transform:translatez(0);transform:translatez(0)}.lg\\\\\\\\.jsx-4bbd75c2920167f7:hidden .bg-white.jsx-4bbd75c2920167f7{background-color:white!important}}@media only screen and (min-width:1024px){textarea.jsx-4bbd75c2920167f7{max-height:250px!important;overflow-y:auto!important;-ms-scroll-chaining:none;overscroll-behavior:contain}.reset-height.jsx-4bbd75c2920167f7{min-height:104px!important;max-height:250px!important;overflow-y:auto!important}}.fixed-bottom.jsx-4bbd75c2920167f7{position:fixed!important;bottom:0;left:0;right:0;background:white}'\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n        lineNumber: 701,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatInterface, \"5qeXdVmAPQdIcP6dr6Ok0Fins1w=\", false, function() {\n    return [\n        _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__.useChatContext\n    ];\n});\n_c = ChatInterface;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatInterface);\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ChatInterface.jsx\n"));

/***/ })

});