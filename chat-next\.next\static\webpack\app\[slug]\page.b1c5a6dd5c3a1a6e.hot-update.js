"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[slug]/page",{

/***/ "(app-pages-browser)/./src/components/ChatInterface.jsx":
/*!******************************************!*\
  !*** ./src/components/ChatInterface.jsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/constants */ \"(app-pages-browser)/./src/utils/constants.js\");\n/* harmony import */ var _utils_config__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/config */ \"(app-pages-browser)/./src/utils/config.js\");\n/* harmony import */ var _barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FaArrowUp,FaChevronLeft,FaChevronRight!=!react-icons/fa6 */ \"(app-pages-browser)/./node_modules/react-icons/fa6/index.mjs\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../contexts/ChatContext */ \"(app-pages-browser)/./src/contexts/ChatContext.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst ChatInterface = (param)=>{\n    let { slug, query } = param;\n    _s();\n    // Context API\n    const { metadata, updateMetadata, setLoadingState, setErrorState, clearError, getCustomerName, getBusinessName, hasMetadata, loading: contextLoading, error: contextError } = (0,_contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__.useChatContext)();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [currentSlide, setCurrentSlide] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [touchStart, setTouchStart] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [touchEnd, setTouchEnd] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isTablet, setIsTablet] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showInitialUI, setShowInitialUI] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isBotTyping, setIsBotTyping] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isBotThinking, setIsBotThinking] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [sseConnection, setSseConnection] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const carouselRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const messagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const messageElementsRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(new Map()); // Store refs to individual messages\n    // Detect iOS for targeted fixes\n    const isIOS = ()=>{\n        if (true) {\n            return /iPhone|iPad|iPod/i.test(navigator.userAgent);\n        }\n        return false;\n    };\n    const username = slug;\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (username && \"object\" !== \"undefined\") {\n                fetchCustomer(username);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        username\n    ]);\n    const fetchCustomer = async (customerName)=>{\n        try {\n            setLoadingState(true);\n            clearError();\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"\".concat(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_SETTINGS, \"?customerName=\").concat(customerName));\n            if (response.data && response.data.customerName) {\n                const metadataFromResponse = response.data.metaData;\n                console.log('Storing metadata in context:', metadataFromResponse);\n                updateMetadata(metadataFromResponse);\n                // Keep existing localStorage for backward compatibility\n                localStorage.setItem(\"customerName_userId\", response.data.customerName);\n                localStorage.setItem(\"BusinessName\", response.data.businessName);\n                window.dispatchEvent(new CustomEvent('businessNameLoaded', {\n                    detail: {\n                        businessName: response.data.businessName\n                    }\n                }));\n                const existingUserId = localStorage.getItem(\"userID\");\n                if (existingUserId) {\n                    await fetchExistingMessages(existingUserId);\n                } else {\n                    await initializeChatSession();\n                }\n            } else {\n                await initializeChatSession();\n            }\n        } catch (error) {\n            console.error('Error fetching customer data:', error);\n            setErrorState('Failed to load customer settings');\n            // Fallback metadata\n            updateMetadata({\n                customerName: customerName,\n                businessName: \"Driply\"\n            });\n            localStorage.setItem(\"BusinessName\", \"Driply\");\n            window.dispatchEvent(new CustomEvent('businessNameLoaded', {\n                detail: {\n                    businessName: \"Driply\"\n                }\n            }));\n            await initializeChatSession();\n        } finally{\n            setLoadingState(false);\n        }\n    };\n    const fetchExistingMessages = async (userId)=>{\n        console.log(\"metadata : \", metadata);\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"\".concat(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_MESSAGES, \"?userId=\").concat(userId));\n            if (response.data && response.data.length > 0) {\n                setMessages(response.data.map((msg)=>({\n                        text: msg.message,\n                        timestamp: new Date(msg.createdAt).getTime(),\n                        type: msg.type,\n                        source: msg.source\n                    })));\n            }\n            connectToSSE(userId);\n        } catch (error) {\n            await initializeChatSession();\n        }\n    };\n    const initializeChatSession = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_INIT, {\n                customerName: username\n            });\n            if (response.data && response.data.userId) {\n                localStorage.setItem(\"userId\", response.data.userId);\n                localStorage.setItem(\"userID\", response.data._id);\n                await fetchExistingMessages(response.data._id);\n            }\n        } catch (error) {\n            if (!localStorage.getItem(\"BusinessName\")) {\n                localStorage.setItem(\"BusinessName\", \"Driply\");\n                window.dispatchEvent(new CustomEvent('businessNameLoaded', {\n                    detail: {\n                        businessName: \"Driply\"\n                    }\n                }));\n            }\n        }\n    };\n    const connectToSSE = (userId)=>{\n        try {\n            if (sseConnection) {\n                sseConnection.close();\n            }\n            const sseUrl = (0,_utils_config__WEBPACK_IMPORTED_MODULE_4__.getSSEUrl)(userId);\n            const eventSource = new EventSource(sseUrl);\n            setSseConnection(eventSource);\n            eventSource.addEventListener(\"message\", (param)=>{\n                let { data } = param;\n                try {\n                    const contents = JSON.parse(data);\n                    handleSSEMessage(contents);\n                } catch (error) {\n                // Handle parsing error silently\n                }\n            });\n            eventSource.addEventListener(\"error\", ()=>{\n            // Handle connection error silently\n            });\n        } catch (error) {\n        // Handle SSE connection error silently\n        }\n    };\n    const handleSSEMessage = (data)=>{\n        const subtype = data.subType || data.subtype || data.type || \"UNKNOWN\";\n        const content = data.content || \"\";\n        const message = data.message || data.text || \"\";\n        if ([\n            \"TYPING\",\n            \"THINKING\",\n            \"BEHAVIOUR_MESSAGE\"\n        ].includes(subtype)) {\n            switch(subtype){\n                case \"TYPING\":\n                    setIsBotTyping(true);\n                    setIsBotThinking(false);\n                    break;\n                case \"THINKING\":\n                    setIsBotThinking(true);\n                    setIsBotTyping(false);\n                    break;\n                case \"BEHAVIOUR_MESSAGE\":\n                    setIsBotTyping(false);\n                    setIsBotThinking(false);\n                    break;\n            }\n            return;\n        }\n        const displayText = message || content;\n        if (!displayText) return;\n        const displayType = [\n            \"TEXT\",\n            \"MESSAGE\",\n            \"DATA_MESSAGE\"\n        ].includes(subtype) ? subtype : \"UNKNOWN\";\n        setMessages((prev)=>[\n                ...prev,\n                {\n                    text: displayText,\n                    timestamp: Date.now(),\n                    type: displayType,\n                    source: \"BOT\"\n                }\n            ]);\n        setIsBotTyping(false);\n        setIsBotThinking(false);\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (message.trim()) {\n            const userMessage = message.trim();\n            setMessages((prev)=>[\n                    ...prev,\n                    {\n                        text: userMessage,\n                        timestamp: Date.now(),\n                        type: \"TEXT\",\n                        source: \"USER\"\n                    }\n                ]);\n            setMessage(\"\");\n            setIsTyping(false);\n            // Simple scroll to bottom like Augment\n            setTimeout(()=>scrollToBottom(), 100);\n            // Show loading dots immediately after sending message\n            setIsBotThinking(true);\n            setIsBotTyping(false);\n            // Reset textarea\n            if (inputRef.current) {\n                inputRef.current.style.height = \"104px\";\n                inputRef.current.scrollTop = 0;\n                inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n                inputRef.current.classList.add(\"reset-height\");\n            }\n            try {\n                const userId = localStorage.getItem(\"userID\");\n                if (userId) {\n                    await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_SEND, {\n                        userId: userId,\n                        message: userMessage,\n                        \"typs\": \"TEXT\",\n                        \"source\": \"USER\",\n                        \"isTest\": query.isTest === '1' ? true : false\n                    });\n                }\n            } catch (error) {\n                // Hide loading dots on error\n                setIsBotThinking(false);\n                setIsBotTyping(false);\n                setMessages((prev)=>[\n                        ...prev,\n                        {\n                            text: \"Sorry, there was an error sending your message. Please try again.\",\n                            timestamp: Date.now(),\n                            type: \"TEXT\",\n                            source: \"BOT\"\n                        }\n                    ]);\n            }\n        }\n    };\n    const handleTextareaResize = (textarea)=>{\n        if (textarea) {\n            textarea.style.height = \"104px\";\n            const scrollHeight = textarea.scrollHeight;\n            if (isIOS()) {\n                if (scrollHeight > 48) {\n                    textarea.style.overflowY = \"auto\";\n                } else {\n                    textarea.style.overflowY = \"hidden\";\n                }\n            } else {\n                const newHeight = Math.min(250, Math.max(104, scrollHeight));\n                textarea.style.height = \"\".concat(newHeight, \"px\");\n                textarea.style.overflowY = scrollHeight > newHeight ? \"auto\" : \"hidden\";\n            }\n        }\n    };\n    const handleInputChange = (e)=>{\n        const value = e.target.value;\n        setMessage(value);\n        handleTextareaResize(e.target);\n        if (messages.length === 0) {\n            setIsTyping(value.length > 0);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length > 0 && inputRef.current && !message) {\n                const timer = setTimeout({\n                    \"ChatInterface.useEffect.timer\": ()=>{\n                        if (inputRef.current) {\n                            inputRef.current.style.height = \"104px\";\n                            inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n                            inputRef.current.classList.add(\"reset-height\");\n                        }\n                    }\n                }[\"ChatInterface.useEffect.timer\"], 50);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                })[\"ChatInterface.useEffect\"];\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages,\n        message\n    ]);\n    // SIMPLE AUGMENT-STYLE SCROLL: Just scroll to bottom smoothly\n    const scrollToBottom = ()=>{\n        const container = messagesContainerRef.current;\n        if (container) {\n            container.scrollTo({\n                top: container.scrollHeight,\n                behavior: 'smooth'\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length > 0) {\n                // Simple scroll to bottom like Augment does\n                setTimeout({\n                    \"ChatInterface.useEffect\": ()=>scrollToBottom()\n                }[\"ChatInterface.useEffect\"], 100);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages,\n        isBotTyping,\n        isBotThinking\n    ]);\n    // Clean up - remove complex scroll functions\n    const renderMessages = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: '24px'\n            },\n            children: [\n                messages.map((msg, index)=>{\n                    const messageId = \"\".concat(msg.timestamp, \"-\").concat(index);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: (el)=>{\n                            if (el) {\n                                messageElementsRef.current.set(messageId, el);\n                            } else {\n                                messageElementsRef.current.delete(messageId);\n                            }\n                        },\n                        className: \"message-item\",\n                        \"data-message-index\": index,\n                        \"data-message-id\": messageId,\n                        style: {\n                            display: 'flex',\n                            justifyContent: msg.source === 'USER' ? 'flex-end' : 'flex-start',\n                            marginBottom: '24px'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: '16px',\n                                fontWeight: '400',\n                                borderRadius: isMobile ? '15px' : '24px',\n                                maxWidth: isMobile ? '90%' : '448px',\n                                wordBreak: 'break-word',\n                                overflowWrap: 'break-word',\n                                hyphens: 'auto',\n                                backgroundColor: msg.source === 'USER' ? '#f3f4f6' : '#ffffff',\n                                padding: isMobile ? '8px 12px' : '12px 16px',\n                                boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)'\n                            },\n                            children: msg.text\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 381,\n                            columnNumber: 15\n                        }, undefined)\n                    }, messageId, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 363,\n                        columnNumber: 13\n                    }, undefined);\n                }),\n                (isBotTyping || isBotThinking) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"message-item\",\n                    \"data-message-type\": \"loading\",\n                    style: {\n                        display: 'flex',\n                        justifyContent: 'flex-start',\n                        marginBottom: '24px'\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundColor: '#ffffff',\n                            padding: isMobile ? '8px 12px' : '12px 16px',\n                            borderRadius: isMobile ? '15px' : '24px',\n                            boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                gap: '4px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"loading-dot\",\n                                    style: {\n                                        width: '8px',\n                                        height: '8px',\n                                        backgroundColor: '#000000',\n                                        borderRadius: '50%'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 419,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"loading-dot\",\n                                    style: {\n                                        width: '8px',\n                                        height: '8px',\n                                        backgroundColor: '#000000',\n                                        borderRadius: '50%'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 420,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"loading-dot\",\n                                    style: {\n                                        width: '8px',\n                                        height: '8px',\n                                        backgroundColor: '#000000',\n                                        borderRadius: '50%'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 421,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 418,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 412,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                    lineNumber: 403,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n            lineNumber: 359,\n            columnNumber: 7\n        }, undefined);\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSubmit(e);\n        }\n    };\n    const handleSuggestionClick = async (cardTitle)=>{\n        setMessages((prev)=>[\n                ...prev,\n                {\n                    text: cardTitle,\n                    timestamp: Date.now(),\n                    type: \"TEXT\",\n                    source: \"USER\"\n                }\n            ]);\n        setMessage(\"\");\n        setIsTyping(false);\n        // Simple scroll to bottom like Augment\n        setTimeout(()=>scrollToBottom(), 100);\n        // Show loading dots immediately after sending message\n        setIsBotThinking(true);\n        setIsBotTyping(false);\n        if (inputRef.current) {\n            inputRef.current.style.height = \"104px\";\n            inputRef.current.scrollTop = 0;\n            inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n            inputRef.current.classList.add(\"reset-height\");\n        }\n        try {\n            const userId = localStorage.getItem(\"userID\");\n            if (userId) {\n                await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_SEND, {\n                    userId: userId,\n                    message: cardTitle,\n                    customerName: username\n                });\n            }\n        } catch (error) {\n            // Hide loading dots on error\n            setIsBotThinking(false);\n            setIsBotTyping(false);\n            setMessages((prev)=>[\n                    ...prev,\n                    {\n                        text: \"Sorry, there was an error sending your message. Please try again.\",\n                        timestamp: Date.now(),\n                        type: \"TEXT\",\n                        source: \"BOT\"\n                    }\n                ]);\n        }\n    };\n    const getMaxSlides = ()=>{\n        if (isMobile) return _utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.length;\n        return Math.ceil(_utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.length / 2);\n    };\n    const nextSlide = ()=>{\n        if (isMobile) {\n            setCurrentSlide((prev)=>prev < _utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.length - 1 ? prev + 1 : prev);\n        } else {\n            const maxSlides = getMaxSlides();\n            setCurrentSlide((prev)=>(prev + 1) % maxSlides);\n        }\n    };\n    const prevSlide = ()=>{\n        if (isMobile) {\n            setCurrentSlide((prev)=>prev > 0 ? prev - 1 : prev);\n        } else {\n            const maxSlides = getMaxSlides();\n            setCurrentSlide((prev)=>(prev - 1 + maxSlides) % maxSlides);\n        }\n    };\n    const handleNextSlide = ()=>{\n        nextSlide();\n    };\n    const handlePrevSlide = ()=>{\n        prevSlide();\n    };\n    const handleTouchStart = (e)=>{\n        setTouchStart(e.targetTouches[0].clientX);\n    };\n    const handleTouchMove = (e)=>{\n        setTouchEnd(e.targetTouches[0].clientX);\n    };\n    const handleTouchEnd = ()=>{\n        if (!touchStart || !touchEnd) return;\n        const swipeDistance = touchStart - touchEnd;\n        const minSwipeDistance = 50;\n        if (swipeDistance > minSwipeDistance) {\n            nextSlide();\n        }\n        if (swipeDistance < -minSwipeDistance) {\n            prevSlide();\n        }\n        setTouchStart(0);\n        setTouchEnd(0);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length === 0) {\n                setShowInitialUI(false);\n                const timer = setTimeout({\n                    \"ChatInterface.useEffect.timer\": ()=>setShowInitialUI(true)\n                }[\"ChatInterface.useEffect.timer\"], 60);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                })[\"ChatInterface.useEffect\"];\n            } else {\n                setShowInitialUI(false);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages.length\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const checkScreenSize = {\n                \"ChatInterface.useEffect.checkScreenSize\": ()=>{\n                    if (true) {\n                        const width = window.innerWidth;\n                        setIsMobile(width < 768);\n                        setIsTablet(width >= 768 && width < 1024);\n                    }\n                }\n            }[\"ChatInterface.useEffect.checkScreenSize\"];\n            checkScreenSize();\n            const handleResize = {\n                \"ChatInterface.useEffect.handleResize\": ()=>{\n                    setCurrentSlide(0);\n                    checkScreenSize();\n                }\n            }[\"ChatInterface.useEffect.handleResize\"];\n            if (true) {\n                window.addEventListener(\"resize\", handleResize);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>window.removeEventListener(\"resize\", handleResize)\n                })[\"ChatInterface.useEffect\"];\n            }\n        }\n    }[\"ChatInterface.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (inputRef.current && messages.length === 0) {\n                const shouldAutoFocus = window.innerWidth >= 768;\n                if (shouldAutoFocus) {\n                    const timer = setTimeout({\n                        \"ChatInterface.useEffect.timer\": ()=>{\n                            var _inputRef_current;\n                            (_inputRef_current = inputRef.current) === null || _inputRef_current === void 0 ? void 0 : _inputRef_current.focus();\n                        }\n                    }[\"ChatInterface.useEffect.timer\"], 100);\n                    return ({\n                        \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                    })[\"ChatInterface.useEffect\"];\n                }\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages.length\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            return ({\n                \"ChatInterface.useEffect\": ()=>{\n                    if (sseConnection) {\n                        sseConnection.close();\n                    }\n                }\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], [\n        sseConnection\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"bg-white flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"hidden lg:flex flex-1 flex-col px-4 \",\n                children: messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"flex flex-1 items-center justify-center min-h-[calc(100vh-64px)] mt-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"flex flex-col items-center w-[768px] justify-center mx-auto \",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    transitionDelay: showInitialUI ? \"40ms\" : \"0ms\"\n                                },\n                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-4xl text-gray-900 mb-6 text-center transition-all duration-700 \".concat(showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                children: \"How can I help you?\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 605,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                style: {\n                                    transitionDelay: showInitialUI ? \"120ms\" : \"0ms\"\n                                },\n                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"relative w-full mb-6 flex flex-col items-center transition-all duration-500 \".concat(showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        ref: messages.length === 0 ? inputRef : null,\n                                        value: message,\n                                        onChange: handleInputChange,\n                                        onKeyDown: handleKeyPress,\n                                        placeholder: \"Ask anything\",\n                                        autoFocus: messages.length === 0,\n                                        rows: 1,\n                                        style: {\n                                            boxSizing: \"border-box\",\n                                            zIndex: 1001,\n                                            position: \"relative\"\n                                        },\n                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"pt-3 px-4 lg:p-4 pb-16 pr-12 w-full min-h-[104px] max-h-[250px] overflow-y-auto hide-scrollbar text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base shadow-md resize-none reset-height\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 624,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: message.trim().length === 0,\n                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"absolute right-3 bottom-3 w-9 h-9 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation \".concat(message.trim().length === 0 ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                            className: \"w-4 h-4 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 647,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 639,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 616,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    transitionDelay: showInitialUI ? \"200ms\" : \"0ms\",\n                                    zIndex: 10\n                                },\n                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"relative w-full max-w-2xl transition-all duration-500 ease-in-out \".concat(isTyping ? \"opacity-0 pointer-events-none\" : showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: carouselRef,\n                                            style: {\n                                                transform: \"translateX(-\".concat(currentSlide * 100, \"%)\")\n                                            },\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"flex transition-transform duration-300 ease-in-out\",\n                                            children: Array.from({\n                                                length: Math.ceil(_utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.length / 2)\n                                            }).map((_, slideIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"w-full flex-shrink-0 flex gap-2 justify-center\",\n                                                    children: _utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.slice(slideIndex * 2, slideIndex * 2 + 2).map((card, cardIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleSuggestionClick(card.title),\n                                                            style: {\n                                                                width: \"fit-content\"\n                                                            },\n                                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"py-2 px-3 rounded-[8px] bg-[#f6f6f6] text-left group transition-all duration-200 touch-manipulation\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-[16px] font-[600] text-black mb-0.5 leading-tight whitespace-nowrap\",\n                                                                    children: card.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                    lineNumber: 685,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-[16px] text-gray-500 leading-tight whitespace-nowrap\",\n                                                                    children: card.subtitle\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                    lineNumber: 688,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, slideIndex * 2 + cardIndex, true, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 679,\n                                                            columnNumber: 27\n                                                        }, undefined))\n                                                }, slideIndex, false, {\n                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                    lineNumber: 671,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 663,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 662,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handlePrevSlide,\n                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"absolute left-28 top-1/2 transform -translate-y-1/2 -translate-x-6 w-8 h-8 bg-white border border-gray-200 rounded-full flex items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronLeft, {\n                                            className: \"w-3 h-3 text-gray-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 701,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 697,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleNextSlide,\n                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"absolute right-28 top-1/2 transform -translate-y-1/2 translate-x-6 w-8 h-8 bg-white border border-gray-200 rounded-full flex items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronRight, {\n                                            className: \"w-3 h-3 text-gray-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 707,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 703,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 650,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 604,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                    lineNumber: 603,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"h-screen flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: messagesContainerRef,\n                            style: {\n                                height: \"calc(100vh - 60px - 180px)\",\n                                marginTop: \"60px\",\n                                overflowY: \"auto\",\n                                backgroundColor: \"#ffffff\",\n                                overscrollBehavior: \"contain\",\n                                scrollBehavior: \"smooth\"\n                            },\n                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"hide-scrollbar\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    paddingBottom: '40px'\n                                },\n                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"w-full lg:w-[768px] mx-auto px-4 py-4\",\n                                children: [\n                                    renderMessages(),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: messagesEndRef,\n                                        className: \"jsx-4bbd75c2920167f7\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 729,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 727,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 715,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                height: \"180px\",\n                                position: \"relative\",\n                                zIndex: 1000,\n                                flexShrink: 0\n                            },\n                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"bg-white border-t border-gray-100\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSubmit,\n                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"relative w-full lg:w-[768px] mx-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                ref: messages.length > 0 ? inputRef : null,\n                                                value: message,\n                                                onChange: handleInputChange,\n                                                onKeyDown: handleKeyPress,\n                                                placeholder: \"Ask anything\",\n                                                rows: 1,\n                                                style: {\n                                                    boxSizing: \"border-box\",\n                                                    zIndex: 1001,\n                                                    position: \"relative\"\n                                                },\n                                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"pt-3 px-4 md:px-5 pb-10 pr-12 w-full min-h-[104px] max-h-[250px] overflow-y-auto text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base shadow-lg resize-none reset-height hide-scrollbar\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 748,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"submit\",\n                                                disabled: message.trim().length === 0,\n                                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation \".concat(message.trim().length === 0 ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                                    className: \"w-4 h-4 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                    lineNumber: 770,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 762,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 744,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-center mt-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-xs text-gray-500\",\n                                            children: [\n                                                \"This chat is powered by\",\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-black\",\n                                                    children: \"Driply.me\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                    lineNumber: 776,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 774,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 773,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 743,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 734,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                    lineNumber: 713,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 601,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"lg:hidden h-screen flex flex-col\",\n                children: [\n                    messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"flex flex-col items-center pt-15 pb-4 px-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"hidden md:flex lg:hidden flex-col items-center\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 790,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 789,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    paddingBottom: \"260px\"\n                                },\n                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"flex flex-col items-center justify-center flex-1 px-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"40ms\" : \"0ms\"\n                                    },\n                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-2xl md:text-4xl text-gray-900 text-center leading-relaxed transition-all duration-700 \".concat(showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                    children: \"How can I help you?\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 796,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 792,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"h-screen flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: messagesContainerRef,\n                                style: {\n                                    height: isMobile ? \"calc(100vh - 60px - 280px)\" // mobile: navbar(60px) + input(280px)\n                                     : \"calc(100vh - 60px - 200px)\",\n                                    marginTop: \"60px\",\n                                    overflowY: \"auto\",\n                                    backgroundColor: \"#ffffff\",\n                                    overscrollBehavior: \"contain\",\n                                    scrollBehavior: \"smooth\"\n                                },\n                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"hide-scrollbar\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        paddingBottom: '40px'\n                                    },\n                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"w-full max-w-[803px] mx-auto px-4 py-4\",\n                                    children: [\n                                        renderMessages(),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: messagesEndRef,\n                                            className: \"jsx-4bbd75c2920167f7\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 827,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 825,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 811,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    height: isMobile ? \"280px\" : \"200px\",\n                                    position: \"relative\",\n                                    zIndex: 1000,\n                                    flexShrink: 0,\n                                    paddingBottom: \"env(safe-area-inset-bottom, 0)\"\n                                },\n                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"bg-white border-t border-gray-100\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        transform: \"translateZ(0)\"\n                                    },\n                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"px-4 py-4 bg-white\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            style: {\n                                                zIndex: 1001\n                                            },\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"relative bg-white\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    transform: \"translateZ(0)\"\n                                                },\n                                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"relative w-full max-w-[890px] mx-auto bg-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        ref: messages.length > 0 ? inputRef : null,\n                                                        value: message,\n                                                        onChange: handleInputChange,\n                                                        onKeyDown: handleKeyPress,\n                                                        placeholder: \"Ask anything\",\n                                                        rows: 1,\n                                                        style: {\n                                                            boxSizing: \"border-box\",\n                                                            maxHeight: \"250px\",\n                                                            zIndex: 1001,\n                                                            overscrollBehavior: \"none\",\n                                                            position: \"relative\"\n                                                        },\n                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"w-full min-h-[104px] pt-3 px-4 pb-10 pr-12 text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base resize-none overflow-y-auto reset-height\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 855,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        disabled: message.trim().length === 0,\n                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation \".concat(message.trim().length === 0 ? \"bg-[#E0E0E0] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                                            className: \"w-4 h-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 879,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 871,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 851,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 846,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-center py-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"This chat is powered by\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-black\",\n                                                        children: \"Driply.me\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 886,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 884,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 883,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 842,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 832,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 809,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"absolute bottom-0 left-0 right-0 bg-white\",\n                        children: messages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"120ms\" : \"0ms\",\n                                        position: \"relative\",\n                                        zIndex: 10,\n                                        backgroundColor: \"white\",\n                                        paddingBottom: \"10px\"\n                                    },\n                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"\".concat(isMobile ? \"px-0\" : \"px-4\", \" pt-2 pb-2 transition-all duration-500 ease-in-out \").concat(isTyping ? \"opacity-0 pointer-events-none\" : showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"overflow-hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                ref: carouselRef,\n                                                style: {\n                                                    transform: \"translateX(-\".concat(currentSlide * (isMobile ? 50 : 100), \"%)\"),\n                                                    paddingLeft: isMobile ? \"1rem\" : \"0\"\n                                                },\n                                                onTouchStart: handleTouchStart,\n                                                onTouchMove: handleTouchMove,\n                                                onTouchEnd: handleTouchEnd,\n                                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"flex transition-transform duration-300 ease-in-out\",\n                                                children: isMobile ? (()=>{\n                                                    const circularCards = [\n                                                        ..._utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS,\n                                                        ..._utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS\n                                                    ];\n                                                    return circularCards.map((card, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"flex-shrink-0 mt-3 mr-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleSuggestionClick(card.title),\n                                                                style: {\n                                                                    width: \"fit-content\"\n                                                                },\n                                                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"py-3 px-2.5 rounded-[8px] bg-[#F6F6F6] text-left hover:bg-[#D6EDFF] transition-all duration-200 inline-block touch-manipulation\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-[16px] font-[600] text-black mb-0.5 leading-tight whitespace-nowrap overflow-hidden text-ellipsis\",\n                                                                        children: card.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                        lineNumber: 946,\n                                                                        columnNumber: 31\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-[16px] text-gray-500 leading-tight whitespace-nowrap overflow-hidden text-ellipsis\",\n                                                                        children: card.subtitle\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                        lineNumber: 949,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                lineNumber: 939,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        }, \"\".concat(index, \"-\").concat(card.title), false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 935,\n                                                            columnNumber: 27\n                                                        }, undefined));\n                                                })() : Array.from({\n                                                    length: Math.ceil(_utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.length / 2)\n                                                }).map((_, slideIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"w-full flex-shrink-0 flex gap-2 justify-center\",\n                                                        children: _utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.slice(slideIndex * 2, slideIndex * 2 + 2).map((card, cardIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleSuggestionClick(card.title),\n                                                                style: {\n                                                                    width: \"fit-content\"\n                                                                },\n                                                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"py-2 px-2.5 rounded-[8px] bg-[#F6F6F6] text-left group hover:bg-[#D6EDFF] transition-all duration-200 inline-block touch-manipulation\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-[14px] font-[600] text-black mb-0.5 leading-tight whitespace-nowrap\",\n                                                                        children: card.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                        lineNumber: 975,\n                                                                        columnNumber: 31\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-[16px] text-gray-500 leading-tight whitespace-nowrap\",\n                                                                        children: card.subtitle\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                        lineNumber: 978,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                ]\n                                                            }, slideIndex * 2 + cardIndex, true, {\n                                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                lineNumber: 967,\n                                                                columnNumber: 29\n                                                            }, undefined))\n                                                    }, slideIndex, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 959,\n                                                        columnNumber: 25\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 916,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 915,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handlePrevSlide,\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"hidden md:block absolute left-28 top-1/2 transform -translate-y-1/2 -translate-x-6 w-8 h-8 bg-white border border-gray-200 rounded-full items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronLeft, {\n                                                className: \"w-3 h-3 text-gray-600  ms-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 993,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 989,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleNextSlide,\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"hidden md:block absolute right-28 top-1/2 transform -translate-y-1/2 translate-x-6 w-8 h-8 bg-white border border-gray-200 rounded-full items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronRight, {\n                                                className: \"w-3 h-3 text-gray-600 ms-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 999,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 995,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 899,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"200ms\" : \"0ms\",\n                                        position: \"relative\",\n                                        zIndex: 5,\n                                        paddingBottom: \"env(safe-area-inset-bottom)\"\n                                    },\n                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"px-4 bg-white transition-all duration-500 \".concat(showInitialUI ? \"opacity-100 translate-y-0\" : \"translate-y-4\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"relative\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"relative w-full max-w-[890px] mx-auto\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        ref: messages.length === 0 ? inputRef : null,\n                                                        value: message,\n                                                        onChange: handleInputChange,\n                                                        onKeyDown: handleKeyPress,\n                                                        placeholder: \"Ask anything\",\n                                                        rows: 1,\n                                                        style: {\n                                                            boxSizing: \"border-box\",\n                                                            maxHeight: \"250px\",\n                                                            fontSize: \"16px\",\n                                                            zIndex: 1001,\n                                                            overscrollBehaviorY: \"contain\",\n                                                            position: \"relative\"\n                                                        },\n                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"w-full min-h-[104px] pt-3 px-4 pb-10 pr-12 text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base resize-none overflow-y-auto reset-height\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1016,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        disabled: message.trim().length === 0,\n                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation \".concat(message.trim().length === 0 ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                                            className: \"w-4 h-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 1041,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1033,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1015,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1014,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-center py-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"This chat is powered by\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-black\",\n                                                        children: \"Driply.me\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1048,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1046,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1045,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1004,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 895,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 786,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"4bbd75c2920167f7\",\n                children: '.loading-dot{-webkit-animation:loading-dots 1.4s infinite ease-in-out both;-moz-animation:loading-dots 1.4s infinite ease-in-out both;-o-animation:loading-dots 1.4s infinite ease-in-out both;animation:loading-dots 1.4s infinite ease-in-out both}.loading-dot:nth-child(1){-webkit-animation-delay:-.32s;-moz-animation-delay:-.32s;-o-animation-delay:-.32s;animation-delay:-.32s}.loading-dot:nth-child(2){-webkit-animation-delay:-.16s;-moz-animation-delay:-.16s;-o-animation-delay:-.16s;animation-delay:-.16s}.loading-dot:nth-child(3){-webkit-animation-delay:0s;-moz-animation-delay:0s;-o-animation-delay:0s;animation-delay:0s}@-webkit-keyframes loading-dots{0%,80%,100%{-webkit-transform:scale(1);transform:scale(1);opacity:1}40%{-webkit-transform:scale(1.5);transform:scale(1.5);opacity:.7}}@-moz-keyframes loading-dots{0%,80%,100%{-moz-transform:scale(1);transform:scale(1);opacity:1}40%{-moz-transform:scale(1.5);transform:scale(1.5);opacity:.7}}@-o-keyframes loading-dots{0%,80%,100%{-o-transform:scale(1);transform:scale(1);opacity:1}40%{-o-transform:scale(1.5);transform:scale(1.5);opacity:.7}}@keyframes loading-dots{0%,80%,100%{-webkit-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1);opacity:1}40%{-webkit-transform:scale(1.5);-moz-transform:scale(1.5);-o-transform:scale(1.5);transform:scale(1.5);opacity:.7}}.hide-scrollbar{-ms-overflow-style:none!important;scrollbar-width:none!important}.hide-scrollbar::-webkit-scrollbar{display:none!important}@media only screen and (max-width:1023px){.lg\\\\\\\\.jsx-4bbd75c2920167f7:hidden .absolute.bottom-0.jsx-4bbd75c2920167f7{position:fixed!important;bottom:env(safe-area-inset-bottom,0);left:0;right:0;width:100%;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;z-index:1000;background-color:white}textarea.jsx-4bbd75c2920167f7{-webkit-user-select:auto!important;-moz-user-select:auto!important;-ms-user-select:auto!important;user-select:auto!important;-webkit-appearance:none;-moz-appearance:none;-ms-appearance:none;appearance:none;-ms-scroll-chaining:none;overscroll-behavior:none;line-height:24px;max-height:250px!important;position:relative!important;-webkit-transform:translatez(0);-moz-transform:translatez(0);-ms-transform:translatez(0);-o-transform:translatez(0);transform:translatez(0);-webkit-backface-visibility:hidden;-moz-backface-visibility:hidden;backface-visibility:hidden;-webkit-perspective:1000;-moz-perspective:1000;perspective:1000;z-index:1001!important;background-color:white}.lg\\\\\\\\.jsx-4bbd75c2920167f7:hidden form.jsx-4bbd75c2920167f7{position:relative!important;z-index:1001;background-color:white}button[type=\"submit\"].jsx-4bbd75c2920167f7{position:absolute!important;right:12px;bottom:12px;z-index:1002!important;-webkit-tap-highlight-color:transparent;-ms-touch-action:-ms-manipulation;-ms-touch-action:manipulation;touch-action:manipulation;-webkit-transform:translatez(0);-moz-transform:translatez(0);-ms-transform:translatez(0);-o-transform:translatez(0);transform:translatez(0)}.lg\\\\\\\\.jsx-4bbd75c2920167f7:hidden .bg-white.jsx-4bbd75c2920167f7{background-color:white!important}}@media only screen and (min-width:1024px){textarea.jsx-4bbd75c2920167f7{max-height:250px!important;overflow-y:auto!important;-ms-scroll-chaining:none;overscroll-behavior:contain}.reset-height.jsx-4bbd75c2920167f7{min-height:104px!important;max-height:250px!important;overflow-y:auto!important}}.fixed-bottom.jsx-4bbd75c2920167f7{position:fixed!important;bottom:0;left:0;right:0;background:white}'\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n        lineNumber: 599,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatInterface, \"5qeXdVmAPQdIcP6dr6Ok0Fins1w=\", false, function() {\n    return [\n        _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__.useChatContext\n    ];\n});\n_c = ChatInterface;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatInterface);\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ChatInterface.jsx\n"));

/***/ })

});