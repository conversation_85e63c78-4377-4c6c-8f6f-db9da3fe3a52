"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[slug]/page",{

/***/ "(app-pages-browser)/./src/contexts/ChatContext.jsx":
/*!**************************************!*\
  !*** ./src/contexts/ChatContext.jsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatProvider: () => (/* binding */ ChatProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useChatContext: () => (/* binding */ useChatContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useChatContext,ChatProvider,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n// Create the context\nconst ChatContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\n// Custom hook to use the chat context\nconst useChatContext = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ChatContext);\n    if (!context) {\n        throw new Error('useChatContext must be used within a ChatProvider');\n    }\n    return context;\n};\n_s(useChatContext, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// Chat Provider component\nconst ChatProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    console.log(\"ChatProvider\", children);\n    const [metadata, setMetadata] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: null,\n        keywords: null,\n        description: null,\n        image: null\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [settingData, setSettingData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        customerName: null,\n        businessName: null,\n        chatDesignSettings: null,\n        details: null,\n        firstSentence: null,\n        isActive: null,\n        metaData: null,\n        suggestedTopics: null\n    });\n    // Function to update metadata\n    const updateMetadata = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[updateMetadata]\": (newMetadata)=>{\n            setMetadata({\n                \"ChatProvider.useCallback[updateMetadata]\": (prevMetadata)=>({\n                        ...prevMetadata,\n                        ...newMetadata\n                    })\n            }[\"ChatProvider.useCallback[updateMetadata]\"]);\n        }\n    }[\"ChatProvider.useCallback[updateMetadata]\"], []);\n    // Function to clear metadata\n    const clearMetadata = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[clearMetadata]\": ()=>{\n            setMetadata({\n                title: null,\n                keywords: null,\n                description: null,\n                image: null\n            });\n        }\n    }[\"ChatProvider.useCallback[clearMetadata]\"], []);\n    // Function to set loading state\n    const setLoadingState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[setLoadingState]\": (isLoading)=>{\n            setLoading(isLoading);\n        }\n    }[\"ChatProvider.useCallback[setLoadingState]\"], []);\n    // Function to set error state\n    const setErrorState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[setErrorState]\": (errorMessage)=>{\n            setError(errorMessage);\n        }\n    }[\"ChatProvider.useCallback[setErrorState]\"], []);\n    // Function to clear error\n    const clearError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[clearError]\": ()=>{\n            setError(null);\n        }\n    }[\"ChatProvider.useCallback[clearError]\"], []);\n    const value = {\n        metadata,\n        loading,\n        error,\n        updateMetadata,\n        clearMetadata,\n        setLoadingState,\n        setErrorState,\n        clearError,\n        // Helper getters for easy access\n        getTitle: ()=>metadata.title,\n        getKeywords: ()=>metadata.keywords,\n        getDescription: ()=>metadata.description,\n        getImage: ()=>metadata.image,\n        hasMetadata: ()=>metadata.title !== null || metadata.keywords !== null || metadata.description !== null\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\contexts\\\\ChatContext.jsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(ChatProvider, \"X8CSXPWlJfoHwYDrbPUSPkNJZzk=\");\n_c = ChatProvider;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatContext);\nvar _c;\n$RefreshReg$(_c, \"ChatProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/ChatContext.jsx\n"));

/***/ })

});