"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[slug]/page",{

/***/ "(app-pages-browser)/./src/components/ChatInterface.jsx":
/*!******************************************!*\
  !*** ./src/components/ChatInterface.jsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/constants */ \"(app-pages-browser)/./src/utils/constants.js\");\n/* harmony import */ var _utils_config__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/config */ \"(app-pages-browser)/./src/utils/config.js\");\n/* harmony import */ var _barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FaArrowUp,FaChevronLeft,FaChevronRight!=!react-icons/fa6 */ \"(app-pages-browser)/./node_modules/react-icons/fa6/index.mjs\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../contexts/ChatContext */ \"(app-pages-browser)/./src/contexts/ChatContext.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst ChatInterface = (param)=>{\n    let { slug, query } = param;\n    _s();\n    // Context API\n    const { metadata, updateMetadata, setLoadingState, setErrorState, clearError, getCustomerName, getBusinessName, hasMetadata, loading: contextLoading, error: contextError } = (0,_contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__.useChatContext)();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [currentSlide, setCurrentSlide] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [touchStart, setTouchStart] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [touchEnd, setTouchEnd] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isTablet, setIsTablet] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showInitialUI, setShowInitialUI] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isBotTyping, setIsBotTyping] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isBotThinking, setIsBotThinking] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [sseConnection, setSseConnection] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isFirstMessage, setIsFirstMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [shouldScrollToTop, setShouldScrollToTop] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const carouselRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const desktopMessagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const mobileMessagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const tabletMessagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // Detect iOS for targeted fixes\n    const isIOS = ()=>{\n        if (true) {\n            return /iPhone|iPad|iPod/i.test(navigator.userAgent);\n        }\n        return false;\n    };\n    const username = slug;\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (username && \"object\" !== \"undefined\") {\n                fetchCustomer(username);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        username\n    ]);\n    const fetchCustomer = async (customerName)=>{\n        try {\n            setLoadingState(true);\n            clearError();\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"\".concat(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_SETTINGS, \"?customerName=\").concat(customerName));\n            if (response.data && response.data.customerName) {\n                const metadataFromResponse = response.data.metaData;\n                console.log(\"Storing metadata in context:\", metadataFromResponse);\n                updateMetadata(metadataFromResponse);\n                // Keep existing localStorage for backward compatibility\n                localStorage.setItem(\"customerName_userId\", response.data.customerName);\n                localStorage.setItem(\"BusinessName\", response.data.businessName);\n                window.dispatchEvent(new CustomEvent(\"businessNameLoaded\", {\n                    detail: {\n                        businessName: response.data.businessName\n                    }\n                }));\n                const existingUserId = localStorage.getItem(\"userID\");\n                if (existingUserId) {\n                    await fetchExistingMessages(existingUserId);\n                } else {\n                    await initializeChatSession();\n                }\n            } else {\n                await initializeChatSession();\n            }\n        } catch (error) {\n            console.error(\"Error fetching customer data:\", error);\n            setErrorState(\"Failed to load customer settings\");\n            // Fallback metadata\n            updateMetadata({\n                customerName: customerName,\n                businessName: \"Driply\"\n            });\n            localStorage.setItem(\"BusinessName\", \"Driply\");\n            window.dispatchEvent(new CustomEvent(\"businessNameLoaded\", {\n                detail: {\n                    businessName: \"Driply\"\n                }\n            }));\n            await initializeChatSession();\n        } finally{\n            setLoadingState(false);\n        }\n    };\n    const fetchExistingMessages = async (userId)=>{\n        console.log(\"metadata : \", metadata);\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"\".concat(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_MESSAGES, \"?userId=\").concat(userId));\n            if (response.data && response.data.length > 0) {\n                setMessages(response.data.map((msg)=>({\n                        text: msg.message,\n                        timestamp: new Date(msg.createdAt).getTime(),\n                        type: msg.type,\n                        source: msg.source\n                    })));\n            }\n            connectToSSE(userId);\n        } catch (error) {\n            await initializeChatSession();\n        }\n    };\n    const initializeChatSession = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_INIT, {\n                customerName: username\n            });\n            if (response.data && response.data.userId) {\n                localStorage.setItem(\"userId\", response.data.userId);\n                localStorage.setItem(\"userID\", response.data._id);\n                await fetchExistingMessages(response.data._id);\n            }\n        } catch (error) {\n            if (!localStorage.getItem(\"BusinessName\")) {\n                localStorage.setItem(\"BusinessName\", \"Driply\");\n                window.dispatchEvent(new CustomEvent(\"businessNameLoaded\", {\n                    detail: {\n                        businessName: \"Driply\"\n                    }\n                }));\n            }\n        }\n    };\n    const connectToSSE = (userId)=>{\n        try {\n            if (sseConnection) {\n                sseConnection.close();\n            }\n            const sseUrl = (0,_utils_config__WEBPACK_IMPORTED_MODULE_4__.getSSEUrl)(userId);\n            const eventSource = new EventSource(sseUrl);\n            setSseConnection(eventSource);\n            eventSource.addEventListener(\"message\", (param)=>{\n                let { data } = param;\n                try {\n                    const contents = JSON.parse(data);\n                    handleSSEMessage(contents);\n                } catch (error) {\n                // Handle parsing error silently\n                }\n            });\n            eventSource.addEventListener(\"error\", ()=>{\n            // Handle connection error silently\n            });\n        } catch (error) {\n        // Handle SSE connection error silently\n        }\n    };\n    const handleSSEMessage = (data)=>{\n        const subtype = data.subType || data.subtype || data.type || \"UNKNOWN\";\n        const content = data.content || \"\";\n        const message = data.message || data.text || \"\";\n        if ([\n            \"TYPING\",\n            \"THINKING\",\n            \"BEHAVIOUR_MESSAGE\"\n        ].includes(subtype)) {\n            switch(subtype){\n                case \"TYPING\":\n                    setIsBotTyping(true);\n                    setIsBotThinking(false);\n                    break;\n                case \"THINKING\":\n                    setIsBotThinking(true);\n                    setIsBotTyping(false);\n                    break;\n                case \"BEHAVIOUR_MESSAGE\":\n                    setIsBotTyping(false);\n                    setIsBotThinking(false);\n                    break;\n            }\n            return;\n        }\n        const displayText = message || content;\n        if (!displayText) return;\n        const displayType = [\n            \"TEXT\",\n            \"MESSAGE\",\n            \"DATA_MESSAGE\"\n        ].includes(subtype) ? subtype : \"UNKNOWN\";\n        setMessages((prev)=>[\n                ...prev,\n                {\n                    text: displayText,\n                    timestamp: Date.now(),\n                    type: displayType,\n                    source: \"BOT\"\n                }\n            ]);\n        setIsBotTyping(false);\n        setIsBotThinking(false);\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (message.trim()) {\n            const userMessage = message.trim();\n            const isFirstMsg = messages.length === 0;\n            setMessages((prev)=>[\n                    ...prev,\n                    {\n                        text: userMessage,\n                        timestamp: Date.now(),\n                        type: \"TEXT\",\n                        source: \"USER\"\n                    }\n                ]);\n            setMessage(\"\");\n            setIsTyping(false);\n            // For first message, set flag to scroll to top\n            if (isFirstMsg) {\n                setShouldScrollToTop(true);\n                setIsFirstMessage(false);\n            }\n            // Show loading dots immediately after sending message\n            setIsBotThinking(true);\n            setIsBotTyping(false);\n            // Reset textarea\n            if (inputRef.current) {\n                inputRef.current.style.height = \"104px\";\n                inputRef.current.scrollTop = 0;\n                inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n                inputRef.current.classList.add(\"reset-height\");\n            }\n            try {\n                const userId = localStorage.getItem(\"userID\");\n                if (userId) {\n                    await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_SEND, {\n                        userId: userId,\n                        message: userMessage,\n                        typs: \"TEXT\",\n                        source: \"USER\",\n                        isTest: query.isTest === \"1\" ? true : false\n                    });\n                }\n            } catch (error) {\n                // Hide loading dots on error\n                setIsBotThinking(false);\n                setIsBotTyping(false);\n                setMessages((prev)=>[\n                        ...prev,\n                        {\n                            text: \"Sorry, there was an error sending your message. Please try again.\",\n                            timestamp: Date.now(),\n                            type: \"TEXT\",\n                            source: \"BOT\"\n                        }\n                    ]);\n            }\n        }\n    };\n    const handleTextareaResize = (textarea)=>{\n        if (textarea) {\n            textarea.style.height = \"104px\";\n            const scrollHeight = textarea.scrollHeight;\n            if (isIOS()) {\n                if (scrollHeight > 48) {\n                    textarea.style.overflowY = \"auto\";\n                } else {\n                    textarea.style.overflowY = \"hidden\";\n                }\n            } else {\n                const newHeight = Math.min(250, Math.max(104, scrollHeight));\n                textarea.style.height = \"\".concat(newHeight, \"px\");\n                textarea.style.overflowY = scrollHeight > newHeight ? \"auto\" : \"hidden\";\n            }\n        }\n    };\n    const handleInputChange = (e)=>{\n        const value = e.target.value;\n        setMessage(value);\n        handleTextareaResize(e.target);\n        if (messages.length === 0) {\n            setIsTyping(value.length > 0);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length > 0 && inputRef.current && !message) {\n                const timer = setTimeout({\n                    \"ChatInterface.useEffect.timer\": ()=>{\n                        if (inputRef.current) {\n                            inputRef.current.style.height = \"104px\";\n                            inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n                            inputRef.current.classList.add(\"reset-height\");\n                        }\n                    }\n                }[\"ChatInterface.useEffect.timer\"], 50);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                })[\"ChatInterface.useEffect\"];\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages,\n        message\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length > 0) {\n                handleChatGPTScroll();\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages,\n        isBotTyping,\n        isBotThinking\n    ]);\n    const handleChatGPTScroll = ()=>{\n        setTimeout(()=>{\n            // Determine screen type and get appropriate container\n            const screenWidth = window.innerWidth;\n            const isDesktopLayout = screenWidth >= 1024;\n            const isTabletLayout = screenWidth >= 768 && screenWidth < 1024;\n            let container;\n            if (isDesktopLayout) {\n                container = desktopMessagesContainerRef.current;\n            } else if (isTabletLayout) {\n                container = tabletMessagesContainerRef.current;\n            } else {\n                container = mobileMessagesContainerRef.current;\n            }\n            if (!container) return;\n            if (shouldScrollToTop) {\n                // For first message, scroll to top to show the message there\n                container.scrollTo({\n                    top: 0,\n                    behavior: \"smooth\"\n                });\n                setShouldScrollToTop(false);\n            } else {\n                // Different scrolling behavior for each screen type\n                const messageElements = container.querySelectorAll(\".message-pair\");\n                if (messageElements.length > 0) {\n                    const lastMessagePair = messageElements[messageElements.length - 1];\n                    const containerHeight = container.clientHeight;\n                    const pairHeight = lastMessagePair.offsetHeight;\n                    const pairTop = lastMessagePair.offsetTop;\n                    if (isTabletLayout) {\n                        // Tablet-specific scrolling: Keep messages visible by scrolling to bottom of container\n                        // This ensures both user message and AI response remain visible\n                        const scrollPosition = Math.max(0, pairTop + pairHeight - containerHeight + 100);\n                        container.scrollTo({\n                            top: scrollPosition,\n                            behavior: \"smooth\"\n                        });\n                    } else {\n                        // Desktop and mobile: Center the message pair on screen\n                        const scrollPosition = pairTop - (containerHeight - pairHeight) / 2;\n                        container.scrollTo({\n                            top: Math.max(0, scrollPosition),\n                            behavior: \"smooth\"\n                        });\n                    }\n                }\n            }\n        }, 150);\n    };\n    const renderMessagePairs = ()=>{\n        const pairs = [];\n        const userMessages = messages.filter((msg)=>msg.source === \"USER\");\n        const botMessages = messages.filter((msg)=>msg.source === \"BOT\");\n        // Create pairs of user and bot messages\n        for(let i = 0; i < userMessages.length; i++){\n            const userMsg = userMessages[i];\n            const botMsg = botMessages[i];\n            pairs.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"message-pair flex flex-col justify-start\",\n                style: {\n                    minHeight: i === userMessages.length - 1 ? isMobile ? \"calc(100vh - 100px)\" // Mobile-specific height for newest message\n                     : \"calc(100vh - 200px)\" // Desktop/tablet height for newest message\n                     : \"\",\n                    paddingTop: i === 0 ? \"1rem\" : \"1rem\",\n                    paddingBottom: \"0rem\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-end mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-[16px] font-[400] rounded-3xl max-w-xs lg:max-w-lg break-words hyphens-auto bg-gray-100 \".concat(isMobile ? \"rounded-[15px] px-3 py-2\" : \"px-4 py-3\"),\n                            style: {\n                                overflowWrap: \"break-word\",\n                                wordBreak: \"break-word\",\n                                maxWidth: isMobile ? \"90%\" : undefined\n                            },\n                            children: userMsg.text\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 435,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 434,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-start\",\n                        children: botMsg ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-[16px] font-[400] rounded-3xl max-w-xs lg:max-w-lg break-words hyphens-auto bg-white \".concat(isMobile ? \"rounded-[15px] px-3 py-2\" : \"px-4 py-3\"),\n                            style: {\n                                overflowWrap: \"break-word\",\n                                wordBreak: \"break-word\",\n                                maxWidth: isMobile ? \"90%\" : undefined\n                            },\n                            children: botMsg.text\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 452,\n                            columnNumber: 15\n                        }, undefined) : // Show loading dots for the last pair if bot is thinking/typing\n                        i === userMessages.length - 1 && (isBotTyping || isBotThinking) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white \".concat(isMobile ? \"px-3 py-2 rounded-[15px]\" : \"px-4 py-3 rounded-3xl\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-black rounded-full loading-dot\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-black rounded-full loading-dot\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 477,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-black rounded-full loading-dot\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 478,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 475,\n                                columnNumber: 19\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 468,\n                            columnNumber: 17\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 450,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, \"pair-\".concat(i), true, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 419,\n                columnNumber: 9\n            }, undefined));\n        }\n        return pairs;\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSubmit(e);\n        }\n    };\n    const handleSuggestionClick = async (cardTitle)=>{\n        const isFirstMsg = messages.length === 0;\n        setMessages((prev)=>[\n                ...prev,\n                {\n                    text: cardTitle,\n                    timestamp: Date.now(),\n                    type: \"TEXT\",\n                    source: \"USER\"\n                }\n            ]);\n        setMessage(\"\");\n        setIsTyping(false);\n        // For first message, set flag to scroll to top\n        if (isFirstMsg) {\n            setShouldScrollToTop(true);\n            setIsFirstMessage(false);\n        }\n        // Show loading dots immediately after sending message\n        setIsBotThinking(true);\n        setIsBotTyping(false);\n        if (inputRef.current) {\n            inputRef.current.style.height = \"104px\";\n            inputRef.current.scrollTop = 0;\n            inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n            inputRef.current.classList.add(\"reset-height\");\n        }\n        try {\n            const userId = localStorage.getItem(\"userID\");\n            if (userId) {\n                await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_SEND, {\n                    userId: userId,\n                    message: cardTitle,\n                    customerName: username\n                });\n            }\n        } catch (error) {\n            // Hide loading dots on error\n            setIsBotThinking(false);\n            setIsBotTyping(false);\n            setMessages((prev)=>[\n                    ...prev,\n                    {\n                        text: \"Sorry, there was an error sending your message. Please try again.\",\n                        timestamp: Date.now(),\n                        type: \"TEXT\",\n                        source: \"BOT\"\n                    }\n                ]);\n        }\n    };\n    const getMaxSlides = ()=>{\n        if (isMobile) return _utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.length;\n        return Math.ceil(_utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.length / 2);\n    };\n    const nextSlide = ()=>{\n        if (isMobile) {\n            setCurrentSlide((prev)=>prev < _utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.length - 1 ? prev + 1 : prev);\n        } else {\n            const maxSlides = getMaxSlides();\n            setCurrentSlide((prev)=>(prev + 1) % maxSlides);\n        }\n    };\n    const prevSlide = ()=>{\n        if (isMobile) {\n            setCurrentSlide((prev)=>prev > 0 ? prev - 1 : prev);\n        } else {\n            const maxSlides = getMaxSlides();\n            setCurrentSlide((prev)=>(prev - 1 + maxSlides) % maxSlides);\n        }\n    };\n    const handleNextSlide = ()=>{\n        nextSlide();\n    };\n    const handlePrevSlide = ()=>{\n        prevSlide();\n    };\n    const handleTouchStart = (e)=>{\n        setTouchStart(e.targetTouches[0].clientX);\n    };\n    const handleTouchMove = (e)=>{\n        setTouchEnd(e.targetTouches[0].clientX);\n    };\n    const handleTouchEnd = ()=>{\n        if (!touchStart || !touchEnd) return;\n        const swipeDistance = touchStart - touchEnd;\n        const minSwipeDistance = 50;\n        if (swipeDistance > minSwipeDistance) {\n            nextSlide();\n        }\n        if (swipeDistance < -minSwipeDistance) {\n            prevSlide();\n        }\n        setTouchStart(0);\n        setTouchEnd(0);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length === 0) {\n                setShowInitialUI(false);\n                const timer = setTimeout({\n                    \"ChatInterface.useEffect.timer\": ()=>setShowInitialUI(true)\n                }[\"ChatInterface.useEffect.timer\"], 60);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                })[\"ChatInterface.useEffect\"];\n            } else {\n                setShowInitialUI(false);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages.length\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const checkScreenSize = {\n                \"ChatInterface.useEffect.checkScreenSize\": ()=>{\n                    if (true) {\n                        const width = window.innerWidth;\n                        setIsMobile(width < 768);\n                        setIsTablet(width >= 768 && width < 1024);\n                    }\n                }\n            }[\"ChatInterface.useEffect.checkScreenSize\"];\n            checkScreenSize();\n            const handleResize = {\n                \"ChatInterface.useEffect.handleResize\": ()=>{\n                    setCurrentSlide(0);\n                    checkScreenSize();\n                }\n            }[\"ChatInterface.useEffect.handleResize\"];\n            if (true) {\n                window.addEventListener(\"resize\", handleResize);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>window.removeEventListener(\"resize\", handleResize)\n                })[\"ChatInterface.useEffect\"];\n            }\n        }\n    }[\"ChatInterface.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (inputRef.current && messages.length === 0) {\n                const shouldAutoFocus = window.innerWidth >= 768;\n                if (shouldAutoFocus) {\n                    const timer = setTimeout({\n                        \"ChatInterface.useEffect.timer\": ()=>{\n                            var _inputRef_current;\n                            (_inputRef_current = inputRef.current) === null || _inputRef_current === void 0 ? void 0 : _inputRef_current.focus();\n                        }\n                    }[\"ChatInterface.useEffect.timer\"], 100);\n                    return ({\n                        \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                    })[\"ChatInterface.useEffect\"];\n                }\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages.length\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            return ({\n                \"ChatInterface.useEffect\": ()=>{\n                    if (sseConnection) {\n                        sseConnection.close();\n                    }\n                }\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], [\n        sseConnection\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"bg-white flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"hidden lg:flex flex-1 flex-col px-4 \",\n                children: messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex flex-1 items-center justify-center min-h-[calc(100vh-64px)] mt-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex flex-col items-center w-[768px] justify-center mx-auto \",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    transitionDelay: showInitialUI ? \"40ms\" : \"0ms\"\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-4xl text-gray-900 mb-6 text-center transition-all duration-700 \".concat(showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                children: \"How can I help you?\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 667,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                style: {\n                                    transitionDelay: showInitialUI ? \"120ms\" : \"0ms\"\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"relative w-full mb-6 flex flex-col items-center transition-all duration-500 \".concat(showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        ref: messages.length === 0 ? inputRef : null,\n                                        value: message,\n                                        onChange: handleInputChange,\n                                        onKeyDown: handleKeyPress,\n                                        placeholder: \"Ask anything\",\n                                        autoFocus: messages.length === 0,\n                                        rows: 1,\n                                        style: {\n                                            boxSizing: \"border-box\",\n                                            zIndex: 1001,\n                                            position: \"relative\"\n                                        },\n                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"pt-3 px-4 lg:p-4 pb-16 pr-12 w-full min-h-[104px] max-h-[250px] overflow-y-auto hide-scrollbar text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base shadow-md resize-none reset-height\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 687,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: message.trim().length === 0,\n                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"absolute right-3 bottom-3 w-9 h-9 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation \".concat(message.trim().length === 0 ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                            className: \"w-4 h-4 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 711,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 702,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 678,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    transitionDelay: showInitialUI ? \"200ms\" : \"0ms\",\n                                    zIndex: 10\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"relative w-full max-w-2xl transition-all duration-500 ease-in-out \".concat(isTyping ? \"opacity-0 pointer-events-none\" : showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: carouselRef,\n                                            style: {\n                                                transform: \"translateX(-\".concat(currentSlide * 100, \"%)\")\n                                            },\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex transition-transform duration-300 ease-in-out\",\n                                            children: Array.from({\n                                                length: Math.ceil(_utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.length / 2)\n                                            }).map((_, slideIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"w-full flex-shrink-0 flex gap-2 justify-center\",\n                                                    children: _utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.slice(slideIndex * 2, slideIndex * 2 + 2).map((card, cardIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleSuggestionClick(card.title),\n                                                            style: {\n                                                                width: \"fit-content\"\n                                                            },\n                                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"py-2 px-3 rounded-[8px] bg-[#f6f6f6] text-left group transition-all duration-200 touch-manipulation\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-[16px] font-[600] text-black mb-0.5 leading-tight whitespace-nowrap\",\n                                                                    children: card.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                    lineNumber: 750,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-[16px] text-gray-500 leading-tight whitespace-nowrap\",\n                                                                    children: card.subtitle\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                    lineNumber: 753,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, slideIndex * 2 + cardIndex, true, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 744,\n                                                            columnNumber: 27\n                                                        }, undefined))\n                                                }, slideIndex, false, {\n                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                    lineNumber: 736,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 728,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 727,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handlePrevSlide,\n                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"absolute left-28 top-1/2 transform -translate-y-1/2 -translate-x-6 w-8 h-8 bg-white border border-gray-200 rounded-full flex items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronLeft, {\n                                            className: \"w-3 h-3 text-gray-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 766,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 762,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleNextSlide,\n                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"absolute right-28 top-1/2 transform -translate-y-1/2 translate-x-6 w-8 h-8 bg-white border border-gray-200 rounded-full flex items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronRight, {\n                                            className: \"w-3 h-3 text-gray-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 772,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 768,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 714,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 666,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                    lineNumber: 665,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: desktopMessagesContainerRef,\n                            style: {\n                                overscrollBehavior: \"contain\"\n                            },\n                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex-1 overflow-y-auto max-h-[calc(100vh-200px)] mt-14\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"w-full lg:w-[768px] mx-auto px-4 h-fit \",\n                                children: [\n                                    renderMessagePairs(),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: messagesEndRef,\n                                        className: \"jsx-69ac67aa0147cd65\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 786,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 784,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 779,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                zIndex: 1000,\n                                marginTop: \"20px\"\n                            },\n                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"fixed bottom-0 left-0 right-0 p-4 mt-5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSubmit,\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"relative w-full lg:w-[768px] mx-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            ref: messages.length > 0 ? inputRef : null,\n                                            value: message,\n                                            onChange: handleInputChange,\n                                            onKeyDown: handleKeyPress,\n                                            placeholder: \"Ask anything\",\n                                            rows: 1,\n                                            style: {\n                                                boxSizing: \"border-box\",\n                                                zIndex: 1001,\n                                                position: \"relative\"\n                                            },\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"pt-3 px-4 md:px-5 pb-10 pr-12 w-full min-h-[104px] max-h-[250px] overflow-y-auto text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base shadow-lg resize-none reset-height hide-scrollbar\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 798,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: message.trim().length === 0,\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation \".concat(message.trim().length === 0 ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                                className: \"w-4 h-4 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 821,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 812,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 794,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-center mt-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-xs text-gray-500\",\n                                        children: [\n                                            \"This chat is powered by\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-black\",\n                                                children: \"Driply.me\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 827,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 825,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 824,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 790,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 663,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"lg:hidden overflow-hidden fixed inset-0 flex flex-col  mt-10 \",\n                children: [\n                    messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex flex-col items-center pt-15 pb-4 px-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"hidden md:flex lg:hidden flex-col items-center\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 840,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 839,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    paddingBottom: \"260px\"\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex flex-col items-center justify-center flex-1 px-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"40ms\" : \"0ms\"\n                                    },\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-2xl md:text-4xl text-gray-900 text-center leading-relaxed transition-all duration-700 \".concat(showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                    children: \"How can I help you?\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 846,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 842,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: mobileMessagesContainerRef,\n                                style: {\n                                    overscrollBehavior: \"contain\"\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"md:hidden flex-1 overflow-y-auto max-h-[calc(100vh-190px)] mt-16  hide-scrollbar\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"w-full max-w-[803px] mx-auto px-4  pt-12\",\n                                    children: [\n                                        renderMessagePairs(),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: messagesEndRef,\n                                            className: \"jsx-69ac67aa0147cd65\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 868,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 866,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 861,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: tabletMessagesContainerRef,\n                                style: {\n                                    overscrollBehavior: \"contain\"\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"hidden md:block lg:hidden flex-1 overflow-y-auto max-h-[calc(100vh-100px)]  pb-20 hide-scrollbar\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"w-full max-w-[803px] mx-auto px-4 pb-6 pt-12\",\n                                    children: [\n                                        renderMessagePairs(),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: messagesEndRef,\n                                            className: \"jsx-69ac67aa0147cd65\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 880,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 878,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 873,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    minHeight: \"160px\",\n                                    zIndex: 1000,\n                                    paddingBottom: \"env(safe-area-inset-bottom, 0)\",\n                                    transform: \"translateZ(0)\",\n                                    backfaceVisibility: \"hidden\"\n                                },\n                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"fixed bottom-0 left-0 right-0 bg-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        transform: \"translateZ(0)\"\n                                    },\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"px-4 py-4 bg-white\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            style: {\n                                                zIndex: 1001\n                                            },\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"relative bg-white\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    transform: \"translateZ(0)\"\n                                                },\n                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"relative w-full max-w-[890px] mx-auto bg-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        ref: messages.length > 0 ? inputRef : null,\n                                                        value: message,\n                                                        onChange: handleInputChange,\n                                                        onKeyDown: handleKeyPress,\n                                                        placeholder: \"Ask anything\",\n                                                        rows: 1,\n                                                        style: {\n                                                            boxSizing: \"border-box\",\n                                                            maxHeight: \"250px\",\n                                                            zIndex: 1001,\n                                                            overscrollBehavior: \"none\",\n                                                            position: \"relative\"\n                                                        },\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"w-full min-h-[104px] pt-3 px-4 pb-10 pr-12 text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base resize-none overflow-y-auto reset-height\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 907,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        disabled: message.trim().length === 0,\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation \".concat(message.trim().length === 0 ? \"bg-[#E0E0E0] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                                            className: \"w-4 h-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 932,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 923,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 903,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 898,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-center py-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"This chat is powered by\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-black\",\n                                                        children: \"Driply.me\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 939,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 937,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 936,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 894,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 884,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"absolute bottom-0 left-0 right-0 bg-white\",\n                        children: messages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"120ms\" : \"0ms\",\n                                        position: \"relative\",\n                                        zIndex: 10,\n                                        backgroundColor: \"white\",\n                                        paddingBottom: \"10px\"\n                                    },\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"\".concat(isMobile ? \"px-0\" : \"px-4\", \" pt-2 pb-2 transition-all duration-500 ease-in-out \").concat(isTyping ? \"opacity-0 pointer-events-none\" : showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"overflow-hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                ref: carouselRef,\n                                                style: {\n                                                    transform: \"translateX(-\".concat(currentSlide * (isMobile ? 50 : 100), \"%)\"),\n                                                    paddingLeft: isMobile ? \"1rem\" : \"0\"\n                                                },\n                                                onTouchStart: handleTouchStart,\n                                                onTouchMove: handleTouchMove,\n                                                onTouchEnd: handleTouchEnd,\n                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex transition-transform duration-300 ease-in-out\",\n                                                children: isMobile ? (()=>{\n                                                    const circularCards = [\n                                                        ..._utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS,\n                                                        ..._utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS\n                                                    ];\n                                                    return circularCards.map((card, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"flex-shrink-0 mt-3 mr-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleSuggestionClick(card.title),\n                                                                style: {\n                                                                    width: \"fit-content\"\n                                                                },\n                                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"py-3 px-2.5 rounded-[8px] bg-[#F6F6F6] text-left hover:bg-[#D6EDFF] transition-all duration-200 inline-block touch-manipulation\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-[16px] font-[600] text-black mb-0.5 leading-tight whitespace-nowrap overflow-hidden text-ellipsis\",\n                                                                        children: card.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                        lineNumber: 1002,\n                                                                        columnNumber: 33\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-[16px] text-gray-500 leading-tight whitespace-nowrap overflow-hidden text-ellipsis\",\n                                                                        children: card.subtitle\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                        lineNumber: 1005,\n                                                                        columnNumber: 33\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                lineNumber: 995,\n                                                                columnNumber: 31\n                                                            }, undefined)\n                                                        }, \"\".concat(index, \"-\").concat(card.title), false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 991,\n                                                            columnNumber: 29\n                                                        }, undefined));\n                                                })() : Array.from({\n                                                    length: Math.ceil(_utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.length / 2)\n                                                }).map((_, slideIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"w-full flex-shrink-0 flex gap-2 justify-center\",\n                                                        children: _utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.slice(slideIndex * 2, slideIndex * 2 + 2).map((card, cardIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleSuggestionClick(card.title),\n                                                                style: {\n                                                                    width: \"fit-content\"\n                                                                },\n                                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"py-2 px-2.5 rounded-[8px] bg-[#F6F6F6] text-left group hover:bg-[#D6EDFF] transition-all duration-200 inline-block touch-manipulation\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-[14px] font-[600] text-black mb-0.5 leading-tight whitespace-nowrap\",\n                                                                        children: card.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                        lineNumber: 1031,\n                                                                        columnNumber: 33\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-[16px] text-gray-500 leading-tight whitespace-nowrap\",\n                                                                        children: card.subtitle\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                        lineNumber: 1034,\n                                                                        columnNumber: 33\n                                                                    }, undefined)\n                                                                ]\n                                                            }, slideIndex * 2 + cardIndex, true, {\n                                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                lineNumber: 1023,\n                                                                columnNumber: 31\n                                                            }, undefined))\n                                                    }, slideIndex, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1015,\n                                                        columnNumber: 27\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 971,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 970,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handlePrevSlide,\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"hidden md:block absolute left-28 top-1/2 transform -translate-y-1/2 -translate-x-6 w-8 h-8 bg-white border border-gray-200 rounded-full items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronLeft, {\n                                                className: \"w-3 h-3 text-gray-600  ms-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1049,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1045,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleNextSlide,\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"hidden md:block absolute right-28 top-1/2 transform -translate-y-1/2 translate-x-6 w-8 h-8 bg-white border border-gray-200 rounded-full items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronRight, {\n                                                className: \"w-3 h-3 text-gray-600 ms-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1055,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1051,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 952,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"200ms\" : \"0ms\",\n                                        position: \"relative\",\n                                        zIndex: 5,\n                                        paddingBottom: \"env(safe-area-inset-bottom)\"\n                                    },\n                                    className: \"jsx-69ac67aa0147cd65\" + \" \" + \"px-4 bg-white transition-all duration-500 \".concat(showInitialUI ? \"opacity-100 translate-y-0\" : \"translate-y-4\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"relative\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"relative w-full max-w-[890px] mx-auto\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        ref: messages.length === 0 ? inputRef : null,\n                                                        value: message,\n                                                        onChange: handleInputChange,\n                                                        onKeyDown: handleKeyPress,\n                                                        placeholder: \"Ask anything\",\n                                                        rows: 1,\n                                                        style: {\n                                                            boxSizing: \"border-box\",\n                                                            maxHeight: \"250px\",\n                                                            fontSize: \"16px\",\n                                                            zIndex: 1001,\n                                                            overscrollBehaviorY: \"contain\",\n                                                            position: \"relative\"\n                                                        },\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"w-full min-h-[104px] pt-3 px-4 pb-10 pr-12 text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base resize-none overflow-y-auto reset-height\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1073,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        disabled: message.trim().length === 0,\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation \".concat(message.trim().length === 0 ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                                            className: \"w-4 h-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 1099,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1090,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1072,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1071,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-center py-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"This chat is powered by\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"jsx-69ac67aa0147cd65\" + \" \" + \"text-black\",\n                                                        children: \"Driply.me\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1106,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1104,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1103,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1060,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 948,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 836,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"69ac67aa0147cd65\",\n                children: '.loading-dot{-webkit-animation:loading-dots 1.4s infinite ease-in-out both;-moz-animation:loading-dots 1.4s infinite ease-in-out both;-o-animation:loading-dots 1.4s infinite ease-in-out both;animation:loading-dots 1.4s infinite ease-in-out both}.loading-dot:nth-child(1){-webkit-animation-delay:-.32s;-moz-animation-delay:-.32s;-o-animation-delay:-.32s;animation-delay:-.32s}.loading-dot:nth-child(2){-webkit-animation-delay:-.16s;-moz-animation-delay:-.16s;-o-animation-delay:-.16s;animation-delay:-.16s}.loading-dot:nth-child(3){-webkit-animation-delay:0s;-moz-animation-delay:0s;-o-animation-delay:0s;animation-delay:0s}@-webkit-keyframes loading-dots{0%,80%,100%{-webkit-transform:scale(1);transform:scale(1);opacity:1}40%{-webkit-transform:scale(1.5);transform:scale(1.5);opacity:.7}}@-moz-keyframes loading-dots{0%,80%,100%{-moz-transform:scale(1);transform:scale(1);opacity:1}40%{-moz-transform:scale(1.5);transform:scale(1.5);opacity:.7}}@-o-keyframes loading-dots{0%,80%,100%{-o-transform:scale(1);transform:scale(1);opacity:1}40%{-o-transform:scale(1.5);transform:scale(1.5);opacity:.7}}@keyframes loading-dots{0%,80%,100%{-webkit-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1);opacity:1}40%{-webkit-transform:scale(1.5);-moz-transform:scale(1.5);-o-transform:scale(1.5);transform:scale(1.5);opacity:.7}}.hide-scrollbar{-ms-overflow-style:none!important;scrollbar-width:none!important}.hide-scrollbar::-webkit-scrollbar{display:none!important}@media only screen and (max-width:1023px){.lg\\\\\\\\.jsx-69ac67aa0147cd65:hidden .absolute.bottom-0.jsx-69ac67aa0147cd65{position:fixed!important;bottom:env(safe-area-inset-bottom,0);left:0;right:0;width:100%;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;z-index:1000;background-color:white}textarea.jsx-69ac67aa0147cd65{-webkit-user-select:auto!important;-moz-user-select:auto!important;-ms-user-select:auto!important;user-select:auto!important;-webkit-appearance:none;-moz-appearance:none;-ms-appearance:none;appearance:none;-ms-scroll-chaining:none;overscroll-behavior:none;line-height:24px;max-height:250px!important;position:relative!important;-webkit-transform:translatez(0);-moz-transform:translatez(0);-ms-transform:translatez(0);-o-transform:translatez(0);transform:translatez(0);-webkit-backface-visibility:hidden;-moz-backface-visibility:hidden;backface-visibility:hidden;-webkit-perspective:1000;-moz-perspective:1000;perspective:1000;z-index:1001!important;background-color:white}.lg\\\\\\\\.jsx-69ac67aa0147cd65:hidden form.jsx-69ac67aa0147cd65{position:relative!important;z-index:1001;background-color:white}button[type=\"submit\"].jsx-69ac67aa0147cd65{position:absolute!important;right:12px;bottom:12px;z-index:1002!important;-webkit-tap-highlight-color:transparent;-ms-touch-action:-ms-manipulation;-ms-touch-action:manipulation;touch-action:manipulation;-webkit-transform:translatez(0);-moz-transform:translatez(0);-ms-transform:translatez(0);-o-transform:translatez(0);transform:translatez(0)}.lg\\\\\\\\.jsx-69ac67aa0147cd65:hidden .bg-white.jsx-69ac67aa0147cd65{background-color:white!important}}@media only screen and (min-width:1024px){textarea.jsx-69ac67aa0147cd65{max-height:250px!important;overflow-y:auto!important;-ms-scroll-chaining:none;overscroll-behavior:contain}.reset-height.jsx-69ac67aa0147cd65{min-height:104px!important;max-height:250px!important;overflow-y:auto!important}}.fixed-bottom.jsx-69ac67aa0147cd65{position:fixed!important;bottom:0;left:0;right:0;background:white}'\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n        lineNumber: 661,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatInterface, \"xcEg1M/Cq9RD7LPLEA1DTBeajS0=\", false, function() {\n    return [\n        _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__.useChatContext\n    ];\n});\n_c = ChatInterface;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatInterface);\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ChatInterface.jsx\n"));

/***/ })

});