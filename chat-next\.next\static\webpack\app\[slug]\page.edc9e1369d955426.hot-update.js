"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[slug]/page",{

/***/ "(app-pages-browser)/./src/components/ChatInterface.jsx":
/*!******************************************!*\
  !*** ./src/components/ChatInterface.jsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/constants */ \"(app-pages-browser)/./src/utils/constants.js\");\n/* harmony import */ var _utils_config__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/config */ \"(app-pages-browser)/./src/utils/config.js\");\n/* harmony import */ var _barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FaArrowUp,FaChevronLeft,FaChevronRight!=!react-icons/fa6 */ \"(app-pages-browser)/./node_modules/react-icons/fa6/index.mjs\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../contexts/ChatContext */ \"(app-pages-browser)/./src/contexts/ChatContext.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst ChatInterface = (param)=>{\n    let { slug, query } = param;\n    _s();\n    // Context API\n    const { metadata, updateMetadata, setLoadingState, setErrorState, clearError, getCustomerName, getBusinessName, hasMetadata, loading: contextLoading, error: contextError } = (0,_contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__.useChatContext)();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [currentSlide, setCurrentSlide] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [touchStart, setTouchStart] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [touchEnd, setTouchEnd] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isTablet, setIsTablet] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showInitialUI, setShowInitialUI] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isBotTyping, setIsBotTyping] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isBotThinking, setIsBotThinking] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [sseConnection, setSseConnection] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isFirstMessage, setIsFirstMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [shouldScrollToTop, setShouldScrollToTop] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const carouselRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const desktopMessagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const mobileMessagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const tabletMessagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // Detect iOS for targeted fixes\n    const isIOS = ()=>{\n        if (true) {\n            return /iPhone|iPad|iPod/i.test(navigator.userAgent);\n        }\n        return false;\n    };\n    const username = slug;\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (username && \"object\" !== \"undefined\") {\n                fetchCustomer(username);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        username\n    ]);\n    const fetchCustomer = async (customerName)=>{\n        try {\n            setLoadingState(true);\n            clearError();\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"\".concat(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_SETTINGS, \"?customerName=\").concat(customerName));\n            if (response.data && response.data.customerName) {\n                const metadataFromResponse = response.data.metaData;\n                console.log('Storing metadata in context:', metadataFromResponse);\n                updateMetadata(metadataFromResponse);\n                // Keep existing localStorage for backward compatibility\n                localStorage.setItem(\"customerName_userId\", response.data.customerName);\n                localStorage.setItem(\"BusinessName\", response.data.businessName);\n                window.dispatchEvent(new CustomEvent('businessNameLoaded', {\n                    detail: {\n                        businessName: response.data.businessName\n                    }\n                }));\n                const existingUserId = localStorage.getItem(\"userID\");\n                if (existingUserId) {\n                    await fetchExistingMessages(existingUserId);\n                } else {\n                    await initializeChatSession();\n                }\n            } else {\n                await initializeChatSession();\n            }\n        } catch (error) {\n            console.error('Error fetching customer data:', error);\n            setErrorState('Failed to load customer settings');\n            // Fallback metadata\n            updateMetadata({\n                customerName: customerName,\n                businessName: \"Driply\"\n            });\n            localStorage.setItem(\"BusinessName\", \"Driply\");\n            window.dispatchEvent(new CustomEvent('businessNameLoaded', {\n                detail: {\n                    businessName: \"Driply\"\n                }\n            }));\n            await initializeChatSession();\n        } finally{\n            setLoadingState(false);\n        }\n    };\n    const fetchExistingMessages = async (userId)=>{\n        console.log(\"metadata : \", metadata);\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"\".concat(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_MESSAGES, \"?userId=\").concat(userId));\n            if (response.data && response.data.length > 0) {\n                setMessages(response.data.map((msg)=>({\n                        text: msg.message,\n                        timestamp: new Date(msg.createdAt).getTime(),\n                        type: msg.type,\n                        source: msg.source\n                    })));\n            }\n            connectToSSE(userId);\n        } catch (error) {\n            await initializeChatSession();\n        }\n    };\n    const initializeChatSession = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_INIT, {\n                customerName: username\n            });\n            if (response.data && response.data.userId) {\n                localStorage.setItem(\"userId\", response.data.userId);\n                localStorage.setItem(\"userID\", response.data._id);\n                await fetchExistingMessages(response.data._id);\n            }\n        } catch (error) {\n            if (!localStorage.getItem(\"BusinessName\")) {\n                localStorage.setItem(\"BusinessName\", \"Driply\");\n                window.dispatchEvent(new CustomEvent('businessNameLoaded', {\n                    detail: {\n                        businessName: \"Driply\"\n                    }\n                }));\n            }\n        }\n    };\n    const connectToSSE = (userId)=>{\n        try {\n            if (sseConnection) {\n                sseConnection.close();\n            }\n            const sseUrl = (0,_utils_config__WEBPACK_IMPORTED_MODULE_4__.getSSEUrl)(userId);\n            const eventSource = new EventSource(sseUrl);\n            setSseConnection(eventSource);\n            eventSource.addEventListener(\"message\", (param)=>{\n                let { data } = param;\n                try {\n                    const contents = JSON.parse(data);\n                    handleSSEMessage(contents);\n                } catch (error) {\n                // Handle parsing error silently\n                }\n            });\n            eventSource.addEventListener(\"error\", ()=>{\n            // Handle connection error silently\n            });\n        } catch (error) {\n        // Handle SSE connection error silently\n        }\n    };\n    const handleSSEMessage = (data)=>{\n        const subtype = data.subType || data.subtype || data.type || \"UNKNOWN\";\n        const content = data.content || \"\";\n        const message = data.message || data.text || \"\";\n        if ([\n            \"TYPING\",\n            \"THINKING\",\n            \"BEHAVIOUR_MESSAGE\"\n        ].includes(subtype)) {\n            switch(subtype){\n                case \"TYPING\":\n                    setIsBotTyping(true);\n                    setIsBotThinking(false);\n                    break;\n                case \"THINKING\":\n                    setIsBotThinking(true);\n                    setIsBotTyping(false);\n                    break;\n                case \"BEHAVIOUR_MESSAGE\":\n                    setIsBotTyping(false);\n                    setIsBotThinking(false);\n                    break;\n            }\n            return;\n        }\n        const displayText = message || content;\n        if (!displayText) return;\n        const displayType = [\n            \"TEXT\",\n            \"MESSAGE\",\n            \"DATA_MESSAGE\"\n        ].includes(subtype) ? subtype : \"UNKNOWN\";\n        setMessages((prev)=>[\n                ...prev,\n                {\n                    text: displayText,\n                    timestamp: Date.now(),\n                    type: displayType,\n                    source: \"BOT\"\n                }\n            ]);\n        setIsBotTyping(false);\n        setIsBotThinking(false);\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (message.trim()) {\n            const userMessage = message.trim();\n            const isFirstMsg = messages.length === 0;\n            setMessages((prev)=>[\n                    ...prev,\n                    {\n                        text: userMessage,\n                        timestamp: Date.now(),\n                        type: \"TEXT\",\n                        source: \"USER\"\n                    }\n                ]);\n            setMessage(\"\");\n            setIsTyping(false);\n            // For first message, set flag to scroll to top\n            if (isFirstMsg) {\n                setShouldScrollToTop(true);\n                setIsFirstMessage(false);\n            }\n            // Show loading dots immediately after sending message\n            setIsBotThinking(true);\n            setIsBotTyping(false);\n            // Reset textarea\n            if (inputRef.current) {\n                inputRef.current.style.height = \"104px\";\n                inputRef.current.scrollTop = 0;\n                inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n                inputRef.current.classList.add(\"reset-height\");\n            }\n            try {\n                const userId = localStorage.getItem(\"userID\");\n                if (userId) {\n                    await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_SEND, {\n                        userId: userId,\n                        message: userMessage,\n                        \"typs\": \"TEXT\",\n                        \"source\": \"USER\",\n                        \"isTest\": query.isTest === '1' ? true : false\n                    });\n                }\n            } catch (error) {\n                // Hide loading dots on error\n                setIsBotThinking(false);\n                setIsBotTyping(false);\n                setMessages((prev)=>[\n                        ...prev,\n                        {\n                            text: \"Sorry, there was an error sending your message. Please try again.\",\n                            timestamp: Date.now(),\n                            type: \"TEXT\",\n                            source: \"BOT\"\n                        }\n                    ]);\n            }\n        }\n    };\n    const handleTextareaResize = (textarea)=>{\n        if (textarea) {\n            textarea.style.height = \"104px\";\n            const scrollHeight = textarea.scrollHeight;\n            if (isIOS()) {\n                if (scrollHeight > 48) {\n                    textarea.style.overflowY = \"auto\";\n                } else {\n                    textarea.style.overflowY = \"hidden\";\n                }\n            } else {\n                const newHeight = Math.min(250, Math.max(104, scrollHeight));\n                textarea.style.height = \"\".concat(newHeight, \"px\");\n                textarea.style.overflowY = scrollHeight > newHeight ? \"auto\" : \"hidden\";\n            }\n        }\n    };\n    const handleInputChange = (e)=>{\n        const value = e.target.value;\n        setMessage(value);\n        handleTextareaResize(e.target);\n        if (messages.length === 0) {\n            setIsTyping(value.length > 0);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length > 0 && inputRef.current && !message) {\n                const timer = setTimeout({\n                    \"ChatInterface.useEffect.timer\": ()=>{\n                        if (inputRef.current) {\n                            inputRef.current.style.height = \"104px\";\n                            inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n                            inputRef.current.classList.add(\"reset-height\");\n                        }\n                    }\n                }[\"ChatInterface.useEffect.timer\"], 50);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                })[\"ChatInterface.useEffect\"];\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages,\n        message\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length > 0) {\n                handleChatGPTScroll();\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages,\n        isBotTyping,\n        isBotThinking\n    ]);\n    const handleChatGPTScroll = ()=>{\n        setTimeout(()=>{\n            // Determine screen type and get appropriate container\n            const screenWidth = window.innerWidth;\n            const isDesktopLayout = screenWidth >= 1024;\n            const isTabletLayout = screenWidth >= 768 && screenWidth < 1024;\n            let container;\n            if (isDesktopLayout) {\n                container = desktopMessagesContainerRef.current;\n            } else if (isTabletLayout) {\n                container = tabletMessagesContainerRef.current;\n            } else {\n                container = mobileMessagesContainerRef.current;\n            }\n            if (!container) return;\n            if (shouldScrollToTop) {\n                // For first message, scroll to top to show the message there\n                container.scrollTo({\n                    top: 0,\n                    behavior: 'smooth'\n                });\n                setShouldScrollToTop(false);\n            } else {\n                // Different scrolling behavior for each screen type\n                const messageElements = container.querySelectorAll('.message-pair');\n                if (messageElements.length > 0) {\n                    const lastMessagePair = messageElements[messageElements.length - 1];\n                    const containerHeight = container.clientHeight;\n                    const pairHeight = lastMessagePair.offsetHeight;\n                    const pairTop = lastMessagePair.offsetTop;\n                    if (isTabletLayout) {\n                        // Tablet-specific scrolling: Keep messages visible by scrolling to bottom of container\n                        // This ensures both user message and AI response remain visible\n                        const scrollPosition = Math.max(0, pairTop + pairHeight - containerHeight + 100);\n                        container.scrollTo({\n                            top: scrollPosition,\n                            behavior: 'smooth'\n                        });\n                    } else {\n                        // Desktop and mobile: Center the message pair on screen\n                        const scrollPosition = pairTop - (containerHeight - pairHeight) / 2;\n                        container.scrollTo({\n                            top: Math.max(0, scrollPosition),\n                            behavior: 'smooth'\n                        });\n                    }\n                }\n            }\n        }, 150);\n    };\n    const renderMessagePairs = ()=>{\n        const pairs = [];\n        const userMessages = messages.filter((msg)=>msg.source === \"USER\");\n        const botMessages = messages.filter((msg)=>msg.source === \"BOT\");\n        // Create pairs of user and bot messages\n        for(let i = 0; i < userMessages.length; i++){\n            const userMsg = userMessages[i];\n            const botMsg = botMessages[i]; // May be undefined if bot hasn't responded yet\n            pairs.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"message-pair h-fit flex flex-col justify-start py-12 border border-amber-700\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-end mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-[16px] font-[400] rounded-3xl max-w-xs lg:max-w-lg break-words hyphens-auto bg-gray-100 \".concat(isMobile ? 'rounded-[15px] px-3 py-2' : 'px-4 py-3'),\n                            style: {\n                                overflowWrap: \"break-word\",\n                                wordBreak: \"break-word\",\n                                maxWidth: isMobile ? \"90%\" : undefined\n                            },\n                            children: userMsg.text\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 419,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 418,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-start\",\n                        children: botMsg ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-[16px] font-[400] rounded-3xl max-w-xs lg:max-w-lg break-words hyphens-auto bg-white \".concat(isMobile ? 'rounded-[15px] px-3 py-2' : 'px-4 py-3'),\n                            style: {\n                                overflowWrap: \"break-word\",\n                                wordBreak: \"break-word\",\n                                maxWidth: isMobile ? \"90%\" : undefined\n                            },\n                            children: botMsg.text\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 434,\n                            columnNumber: 15\n                        }, undefined) : // Show loading dots for the last pair if bot is thinking/typing\n                        i === userMessages.length - 1 && (isBotTyping || isBotThinking) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white \".concat(isMobile ? 'px-3 py-2 rounded-[15px]' : 'px-4 py-3 rounded-3xl'),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-black rounded-full loading-dot\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-black rounded-full loading-dot\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-black rounded-full loading-dot\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 448,\n                                columnNumber: 19\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 447,\n                            columnNumber: 17\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 432,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, \"pair-\".concat(i), true, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 408,\n                columnNumber: 9\n            }, undefined));\n        }\n        return pairs;\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSubmit(e);\n        }\n    };\n    const handleSuggestionClick = async (cardTitle)=>{\n        const isFirstMsg = messages.length === 0;\n        setMessages((prev)=>[\n                ...prev,\n                {\n                    text: cardTitle,\n                    timestamp: Date.now(),\n                    type: \"TEXT\",\n                    source: \"USER\"\n                }\n            ]);\n        setMessage(\"\");\n        setIsTyping(false);\n        // For first message, set flag to scroll to top\n        if (isFirstMsg) {\n            setShouldScrollToTop(true);\n            setIsFirstMessage(false);\n        }\n        // Show loading dots immediately after sending message\n        setIsBotThinking(true);\n        setIsBotTyping(false);\n        if (inputRef.current) {\n            inputRef.current.style.height = \"104px\";\n            inputRef.current.scrollTop = 0;\n            inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n            inputRef.current.classList.add(\"reset-height\");\n        }\n        try {\n            const userId = localStorage.getItem(\"userID\");\n            if (userId) {\n                await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_SEND, {\n                    userId: userId,\n                    message: cardTitle,\n                    customerName: username\n                });\n            }\n        } catch (error) {\n            // Hide loading dots on error\n            setIsBotThinking(false);\n            setIsBotTyping(false);\n            setMessages((prev)=>[\n                    ...prev,\n                    {\n                        text: \"Sorry, there was an error sending your message. Please try again.\",\n                        timestamp: Date.now(),\n                        type: \"TEXT\",\n                        source: \"BOT\"\n                    }\n                ]);\n        }\n    };\n    const getMaxSlides = ()=>{\n        if (isMobile) return _utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.length;\n        return Math.ceil(_utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.length / 2);\n    };\n    const nextSlide = ()=>{\n        if (isMobile) {\n            setCurrentSlide((prev)=>prev < _utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.length - 1 ? prev + 1 : prev);\n        } else {\n            const maxSlides = getMaxSlides();\n            setCurrentSlide((prev)=>(prev + 1) % maxSlides);\n        }\n    };\n    const prevSlide = ()=>{\n        if (isMobile) {\n            setCurrentSlide((prev)=>prev > 0 ? prev - 1 : prev);\n        } else {\n            const maxSlides = getMaxSlides();\n            setCurrentSlide((prev)=>(prev - 1 + maxSlides) % maxSlides);\n        }\n    };\n    const handleNextSlide = ()=>{\n        nextSlide();\n    };\n    const handlePrevSlide = ()=>{\n        prevSlide();\n    };\n    const handleTouchStart = (e)=>{\n        setTouchStart(e.targetTouches[0].clientX);\n    };\n    const handleTouchMove = (e)=>{\n        setTouchEnd(e.targetTouches[0].clientX);\n    };\n    const handleTouchEnd = ()=>{\n        if (!touchStart || !touchEnd) return;\n        const swipeDistance = touchStart - touchEnd;\n        const minSwipeDistance = 50;\n        if (swipeDistance > minSwipeDistance) {\n            nextSlide();\n        }\n        if (swipeDistance < -minSwipeDistance) {\n            prevSlide();\n        }\n        setTouchStart(0);\n        setTouchEnd(0);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length === 0) {\n                setShowInitialUI(false);\n                const timer = setTimeout({\n                    \"ChatInterface.useEffect.timer\": ()=>setShowInitialUI(true)\n                }[\"ChatInterface.useEffect.timer\"], 60);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                })[\"ChatInterface.useEffect\"];\n            } else {\n                setShowInitialUI(false);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages.length\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const checkScreenSize = {\n                \"ChatInterface.useEffect.checkScreenSize\": ()=>{\n                    if (true) {\n                        const width = window.innerWidth;\n                        setIsMobile(width < 768);\n                        setIsTablet(width >= 768 && width < 1024);\n                    }\n                }\n            }[\"ChatInterface.useEffect.checkScreenSize\"];\n            checkScreenSize();\n            const handleResize = {\n                \"ChatInterface.useEffect.handleResize\": ()=>{\n                    setCurrentSlide(0);\n                    checkScreenSize();\n                }\n            }[\"ChatInterface.useEffect.handleResize\"];\n            if (true) {\n                window.addEventListener(\"resize\", handleResize);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>window.removeEventListener(\"resize\", handleResize)\n                })[\"ChatInterface.useEffect\"];\n            }\n        }\n    }[\"ChatInterface.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (inputRef.current && messages.length === 0) {\n                const shouldAutoFocus = window.innerWidth >= 768;\n                if (shouldAutoFocus) {\n                    const timer = setTimeout({\n                        \"ChatInterface.useEffect.timer\": ()=>{\n                            var _inputRef_current;\n                            (_inputRef_current = inputRef.current) === null || _inputRef_current === void 0 ? void 0 : _inputRef_current.focus();\n                        }\n                    }[\"ChatInterface.useEffect.timer\"], 100);\n                    return ({\n                        \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                    })[\"ChatInterface.useEffect\"];\n                }\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages.length\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            return ({\n                \"ChatInterface.useEffect\": ()=>{\n                    if (sseConnection) {\n                        sseConnection.close();\n                    }\n                }\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], [\n        sseConnection\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"bg-white flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"hidden lg:flex flex-1 flex-col px-4 \",\n                children: messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"flex flex-1 items-center justify-center min-h-[calc(100vh-64px)] mt-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"flex flex-col items-center w-[768px] justify-center mx-auto \",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    transitionDelay: showInitialUI ? \"40ms\" : \"0ms\"\n                                },\n                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-4xl text-gray-900 mb-6 text-center transition-all duration-700 \".concat(showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                children: \"How can I help you?\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 644,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                style: {\n                                    transitionDelay: showInitialUI ? \"120ms\" : \"0ms\"\n                                },\n                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"relative w-full mb-6 flex flex-col items-center transition-all duration-500 \".concat(showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        ref: messages.length === 0 ? inputRef : null,\n                                        value: message,\n                                        onChange: handleInputChange,\n                                        onKeyDown: handleKeyPress,\n                                        placeholder: \"Ask anything\",\n                                        autoFocus: messages.length === 0,\n                                        rows: 1,\n                                        style: {\n                                            boxSizing: \"border-box\",\n                                            zIndex: 1001,\n                                            position: \"relative\"\n                                        },\n                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"pt-3 px-4 lg:p-4 pb-16 pr-12 w-full min-h-[104px] max-h-[250px] overflow-y-auto hide-scrollbar text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base shadow-md resize-none reset-height\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 663,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: message.trim().length === 0,\n                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"absolute right-3 bottom-3 w-9 h-9 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation \".concat(message.trim().length === 0 ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                            className: \"w-4 h-4 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 686,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 678,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 655,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    transitionDelay: showInitialUI ? \"200ms\" : \"0ms\",\n                                    zIndex: 10\n                                },\n                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"relative w-full max-w-2xl transition-all duration-500 ease-in-out \".concat(isTyping ? \"opacity-0 pointer-events-none\" : showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: carouselRef,\n                                            style: {\n                                                transform: \"translateX(-\".concat(currentSlide * 100, \"%)\")\n                                            },\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"flex transition-transform duration-300 ease-in-out\",\n                                            children: Array.from({\n                                                length: Math.ceil(_utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.length / 2)\n                                            }).map((_, slideIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"w-full flex-shrink-0 flex gap-2 justify-center\",\n                                                    children: _utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.slice(slideIndex * 2, slideIndex * 2 + 2).map((card, cardIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleSuggestionClick(card.title),\n                                                            style: {\n                                                                width: \"fit-content\"\n                                                            },\n                                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"py-2 px-3 rounded-[8px] bg-[#f6f6f6] text-left group transition-all duration-200 touch-manipulation\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-[16px] font-[600] text-black mb-0.5 leading-tight whitespace-nowrap\",\n                                                                    children: card.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                    lineNumber: 724,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-[16px] text-gray-500 leading-tight whitespace-nowrap\",\n                                                                    children: card.subtitle\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                    lineNumber: 727,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, slideIndex * 2 + cardIndex, true, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 718,\n                                                            columnNumber: 27\n                                                        }, undefined))\n                                                }, slideIndex, false, {\n                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                    lineNumber: 710,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 702,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 701,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handlePrevSlide,\n                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"absolute left-28 top-1/2 transform -translate-y-1/2 -translate-x-6 w-8 h-8 bg-white border border-gray-200 rounded-full flex items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronLeft, {\n                                            className: \"w-3 h-3 text-gray-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 740,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 736,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleNextSlide,\n                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"absolute right-28 top-1/2 transform -translate-y-1/2 translate-x-6 w-8 h-8 bg-white border border-gray-200 rounded-full flex items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronRight, {\n                                            className: \"w-3 h-3 text-gray-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 746,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 742,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 689,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 643,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                    lineNumber: 642,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: desktopMessagesContainerRef,\n                            style: {\n                                overscrollBehavior: \"contain\"\n                            },\n                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"flex-1 overflow-y-auto  outline-2 outline-green-400 hide-scrollbar  mt-14\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"w-full lg:w-[768px] mx-auto px-4 h-fit border-2 border-red-600\",\n                                children: [\n                                    renderMessagePairs(),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: messagesEndRef,\n                                        className: \"jsx-4bbd75c2920167f7\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 760,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 758,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 753,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                zIndex: 1000,\n                                marginTop: \"20px\"\n                            },\n                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"fixed bottom-0 left-0 right-0 p-4 mt-5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSubmit,\n                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"relative w-full lg:w-[768px] mx-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            ref: messages.length > 0 ? inputRef : null,\n                                            value: message,\n                                            onChange: handleInputChange,\n                                            onKeyDown: handleKeyPress,\n                                            placeholder: \"Ask anything\",\n                                            rows: 1,\n                                            style: {\n                                                boxSizing: \"border-box\",\n                                                zIndex: 1001,\n                                                position: \"relative\"\n                                            },\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"pt-3 px-4 md:px-5 pb-10 pr-12 w-full min-h-[104px] max-h-[250px] overflow-y-auto text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base shadow-lg resize-none reset-height hide-scrollbar\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 772,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: message.trim().length === 0,\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation \".concat(message.trim().length === 0 ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                                className: \"w-4 h-4 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 794,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 786,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 768,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-center mt-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-xs text-gray-500\",\n                                        children: [\n                                            \"This chat is powered by\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-black\",\n                                                children: \"Driply.me\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 800,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 798,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 797,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 764,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 640,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"lg:hidden overflow-hidden fixed inset-0 flex flex-col  mt-10 \",\n                children: [\n                    messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"flex flex-col items-center pt-15 pb-4 px-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"hidden md:flex lg:hidden flex-col items-center\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 813,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 812,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    paddingBottom: \"260px\"\n                                },\n                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"flex flex-col items-center justify-center flex-1 px-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"40ms\" : \"0ms\"\n                                    },\n                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-2xl md:text-4xl text-gray-900 text-center leading-relaxed transition-all duration-700 \".concat(showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                    children: \"How can I help you?\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 819,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 815,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: mobileMessagesContainerRef,\n                                style: {\n                                    overscrollBehavior: \"contain\"\n                                },\n                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"md:hidden flex-1 overflow-y-auto max-h-[calc(100vh-340px)] mt-7 pb-20 hide-scrollbar\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"w-full max-w-[803px] mx-auto px-4 pb-6 pt-12\",\n                                    children: [\n                                        renderMessagePairs(),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: messagesEndRef,\n                                            className: \"jsx-4bbd75c2920167f7\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 841,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 839,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 834,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: tabletMessagesContainerRef,\n                                style: {\n                                    overscrollBehavior: \"contain\"\n                                },\n                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"hidden md:block lg:hidden flex-1 overflow-y-auto max-h-[calc(100vh-100px)]  pb-20 hide-scrollbar\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"w-full max-w-[803px] mx-auto px-4 pb-6 pt-12\",\n                                    children: [\n                                        renderMessagePairs(),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: messagesEndRef,\n                                            className: \"jsx-4bbd75c2920167f7\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 853,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 851,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 846,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    minHeight: \"160px\",\n                                    zIndex: 1000,\n                                    paddingBottom: \"env(safe-area-inset-bottom, 0)\",\n                                    transform: \"translateZ(0)\",\n                                    backfaceVisibility: \"hidden\"\n                                },\n                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"fixed bottom-0 left-0 right-0 bg-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        transform: \"translateZ(0)\"\n                                    },\n                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"px-4 py-4 bg-white\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            style: {\n                                                zIndex: 1001\n                                            },\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"relative bg-white\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    transform: \"translateZ(0)\"\n                                                },\n                                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"relative w-full max-w-[890px] mx-auto bg-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        ref: messages.length > 0 ? inputRef : null,\n                                                        value: message,\n                                                        onChange: handleInputChange,\n                                                        onKeyDown: handleKeyPress,\n                                                        placeholder: \"Ask anything\",\n                                                        rows: 1,\n                                                        style: {\n                                                            boxSizing: \"border-box\",\n                                                            maxHeight: \"250px\",\n                                                            zIndex: 1001,\n                                                            overscrollBehavior: \"none\",\n                                                            position: \"relative\"\n                                                        },\n                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"w-full min-h-[104px] pt-3 px-4 pb-10 pr-12 text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base resize-none overflow-y-auto reset-height\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 881,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        disabled: message.trim().length === 0,\n                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation \".concat(message.trim().length === 0 ? \"bg-[#E0E0E0] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                                            className: \"w-4 h-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 905,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 897,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 877,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 872,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-center py-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"This chat is powered by\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-black\",\n                                                        children: \"Driply.me\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 912,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 910,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 909,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 868,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 858,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"absolute bottom-0 left-0 right-0 bg-white\",\n                        children: messages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"120ms\" : \"0ms\",\n                                        position: \"relative\",\n                                        zIndex: 10,\n                                        backgroundColor: \"white\",\n                                        paddingBottom: \"10px\"\n                                    },\n                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"\".concat(isMobile ? \"px-0\" : \"px-4\", \" pt-2 pb-2 transition-all duration-500 ease-in-out \").concat(isTyping ? \"opacity-0 pointer-events-none\" : showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"overflow-hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                ref: carouselRef,\n                                                style: {\n                                                    transform: \"translateX(-\".concat(currentSlide * (isMobile ? 50 : 100), \"%)\"),\n                                                    paddingLeft: isMobile ? \"1rem\" : \"0\"\n                                                },\n                                                onTouchStart: handleTouchStart,\n                                                onTouchMove: handleTouchMove,\n                                                onTouchEnd: handleTouchEnd,\n                                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"flex transition-transform duration-300 ease-in-out\",\n                                                children: isMobile ? (()=>{\n                                                    const circularCards = [\n                                                        ..._utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS,\n                                                        ..._utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS\n                                                    ];\n                                                    return circularCards.map((card, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"flex-shrink-0 mt-3 mr-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleSuggestionClick(card.title),\n                                                                style: {\n                                                                    width: \"fit-content\"\n                                                                },\n                                                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"py-3 px-2.5 rounded-[8px] bg-[#F6F6F6] text-left hover:bg-[#D6EDFF] transition-all duration-200 inline-block touch-manipulation\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-[16px] font-[600] text-black mb-0.5 leading-tight whitespace-nowrap overflow-hidden text-ellipsis\",\n                                                                        children: card.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                        lineNumber: 972,\n                                                                        columnNumber: 31\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-[16px] text-gray-500 leading-tight whitespace-nowrap overflow-hidden text-ellipsis\",\n                                                                        children: card.subtitle\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                        lineNumber: 975,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                lineNumber: 965,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        }, \"\".concat(index, \"-\").concat(card.title), false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 961,\n                                                            columnNumber: 27\n                                                        }, undefined));\n                                                })() : Array.from({\n                                                    length: Math.ceil(_utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.length / 2)\n                                                }).map((_, slideIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"w-full flex-shrink-0 flex gap-2 justify-center\",\n                                                        children: _utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.slice(slideIndex * 2, slideIndex * 2 + 2).map((card, cardIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleSuggestionClick(card.title),\n                                                                style: {\n                                                                    width: \"fit-content\"\n                                                                },\n                                                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"py-2 px-2.5 rounded-[8px] bg-[#F6F6F6] text-left group hover:bg-[#D6EDFF] transition-all duration-200 inline-block touch-manipulation\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-[14px] font-[600] text-black mb-0.5 leading-tight whitespace-nowrap\",\n                                                                        children: card.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                        lineNumber: 1001,\n                                                                        columnNumber: 31\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-[16px] text-gray-500 leading-tight whitespace-nowrap\",\n                                                                        children: card.subtitle\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                        lineNumber: 1004,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                ]\n                                                            }, slideIndex * 2 + cardIndex, true, {\n                                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                lineNumber: 993,\n                                                                columnNumber: 29\n                                                            }, undefined))\n                                                    }, slideIndex, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 985,\n                                                        columnNumber: 25\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 942,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 941,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handlePrevSlide,\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"hidden md:block absolute left-28 top-1/2 transform -translate-y-1/2 -translate-x-6 w-8 h-8 bg-white border border-gray-200 rounded-full items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronLeft, {\n                                                className: \"w-3 h-3 text-gray-600  ms-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1019,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1015,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleNextSlide,\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"hidden md:block absolute right-28 top-1/2 transform -translate-y-1/2 translate-x-6 w-8 h-8 bg-white border border-gray-200 rounded-full items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronRight, {\n                                                className: \"w-3 h-3 text-gray-600 ms-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1025,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1021,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 925,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"200ms\" : \"0ms\",\n                                        position: \"relative\",\n                                        zIndex: 5,\n                                        paddingBottom: \"env(safe-area-inset-bottom)\"\n                                    },\n                                    className: \"jsx-4bbd75c2920167f7\" + \" \" + \"px-4 bg-white transition-all duration-500 \".concat(showInitialUI ? \"opacity-100 translate-y-0\" : \"translate-y-4\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"relative\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"relative w-full max-w-[890px] mx-auto\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        ref: messages.length === 0 ? inputRef : null,\n                                                        value: message,\n                                                        onChange: handleInputChange,\n                                                        onKeyDown: handleKeyPress,\n                                                        placeholder: \"Ask anything\",\n                                                        rows: 1,\n                                                        style: {\n                                                            boxSizing: \"border-box\",\n                                                            maxHeight: \"250px\",\n                                                            fontSize: \"16px\",\n                                                            zIndex: 1001,\n                                                            overscrollBehaviorY: \"contain\",\n                                                            position: \"relative\"\n                                                        },\n                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"w-full min-h-[104px] pt-3 px-4 pb-10 pr-12 text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base resize-none overflow-y-auto reset-height\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1042,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        disabled: message.trim().length === 0,\n                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation \".concat(message.trim().length === 0 ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                                            className: \"w-4 h-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 1067,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1059,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1041,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1040,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-center py-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"This chat is powered by\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"jsx-4bbd75c2920167f7\" + \" \" + \"text-black\",\n                                                        children: \"Driply.me\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1074,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1072,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1071,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1030,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 921,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 809,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"4bbd75c2920167f7\",\n                children: '.loading-dot{-webkit-animation:loading-dots 1.4s infinite ease-in-out both;-moz-animation:loading-dots 1.4s infinite ease-in-out both;-o-animation:loading-dots 1.4s infinite ease-in-out both;animation:loading-dots 1.4s infinite ease-in-out both}.loading-dot:nth-child(1){-webkit-animation-delay:-.32s;-moz-animation-delay:-.32s;-o-animation-delay:-.32s;animation-delay:-.32s}.loading-dot:nth-child(2){-webkit-animation-delay:-.16s;-moz-animation-delay:-.16s;-o-animation-delay:-.16s;animation-delay:-.16s}.loading-dot:nth-child(3){-webkit-animation-delay:0s;-moz-animation-delay:0s;-o-animation-delay:0s;animation-delay:0s}@-webkit-keyframes loading-dots{0%,80%,100%{-webkit-transform:scale(1);transform:scale(1);opacity:1}40%{-webkit-transform:scale(1.5);transform:scale(1.5);opacity:.7}}@-moz-keyframes loading-dots{0%,80%,100%{-moz-transform:scale(1);transform:scale(1);opacity:1}40%{-moz-transform:scale(1.5);transform:scale(1.5);opacity:.7}}@-o-keyframes loading-dots{0%,80%,100%{-o-transform:scale(1);transform:scale(1);opacity:1}40%{-o-transform:scale(1.5);transform:scale(1.5);opacity:.7}}@keyframes loading-dots{0%,80%,100%{-webkit-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1);opacity:1}40%{-webkit-transform:scale(1.5);-moz-transform:scale(1.5);-o-transform:scale(1.5);transform:scale(1.5);opacity:.7}}.hide-scrollbar{-ms-overflow-style:none!important;scrollbar-width:none!important}.hide-scrollbar::-webkit-scrollbar{display:none!important}@media only screen and (max-width:1023px){.lg\\\\\\\\.jsx-4bbd75c2920167f7:hidden .absolute.bottom-0.jsx-4bbd75c2920167f7{position:fixed!important;bottom:env(safe-area-inset-bottom,0);left:0;right:0;width:100%;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;z-index:1000;background-color:white}textarea.jsx-4bbd75c2920167f7{-webkit-user-select:auto!important;-moz-user-select:auto!important;-ms-user-select:auto!important;user-select:auto!important;-webkit-appearance:none;-moz-appearance:none;-ms-appearance:none;appearance:none;-ms-scroll-chaining:none;overscroll-behavior:none;line-height:24px;max-height:250px!important;position:relative!important;-webkit-transform:translatez(0);-moz-transform:translatez(0);-ms-transform:translatez(0);-o-transform:translatez(0);transform:translatez(0);-webkit-backface-visibility:hidden;-moz-backface-visibility:hidden;backface-visibility:hidden;-webkit-perspective:1000;-moz-perspective:1000;perspective:1000;z-index:1001!important;background-color:white}.lg\\\\\\\\.jsx-4bbd75c2920167f7:hidden form.jsx-4bbd75c2920167f7{position:relative!important;z-index:1001;background-color:white}button[type=\"submit\"].jsx-4bbd75c2920167f7{position:absolute!important;right:12px;bottom:12px;z-index:1002!important;-webkit-tap-highlight-color:transparent;-ms-touch-action:-ms-manipulation;-ms-touch-action:manipulation;touch-action:manipulation;-webkit-transform:translatez(0);-moz-transform:translatez(0);-ms-transform:translatez(0);-o-transform:translatez(0);transform:translatez(0)}.lg\\\\\\\\.jsx-4bbd75c2920167f7:hidden .bg-white.jsx-4bbd75c2920167f7{background-color:white!important}}@media only screen and (min-width:1024px){textarea.jsx-4bbd75c2920167f7{max-height:250px!important;overflow-y:auto!important;-ms-scroll-chaining:none;overscroll-behavior:contain}.reset-height.jsx-4bbd75c2920167f7{min-height:104px!important;max-height:250px!important;overflow-y:auto!important}}.fixed-bottom.jsx-4bbd75c2920167f7{position:fixed!important;bottom:0;left:0;right:0;background:white}'\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n        lineNumber: 638,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatInterface, \"yVXLZfDq5r5nLNNt4zR6KesqzMA=\", false, function() {\n    return [\n        _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__.useChatContext\n    ];\n});\n_c = ChatInterface;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatInterface);\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ChatInterface.jsx\n"));

/***/ })

});