"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[slug]/page",{

/***/ "(app-pages-browser)/./src/components/ChatInterface.jsx":
/*!******************************************!*\
  !*** ./src/components/ChatInterface.jsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/constants */ \"(app-pages-browser)/./src/utils/constants.js\");\n/* harmony import */ var _utils_config__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/config */ \"(app-pages-browser)/./src/utils/config.js\");\n/* harmony import */ var _barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FaArrowUp,FaChevronLeft,FaChevronRight!=!react-icons/fa6 */ \"(app-pages-browser)/./node_modules/react-icons/fa6/index.mjs\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../contexts/ChatContext */ \"(app-pages-browser)/./src/contexts/ChatContext.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst ChatInterface = (param)=>{\n    let { slug, query } = param;\n    _s();\n    // Context API\n    const { metadata, updateMetadata, setLoadingState, setErrorState, clearError, getCustomerName, getBusinessName, hasMetadata, loading: contextLoading, error: contextError } = (0,_contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__.useChatContext)();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [currentSlide, setCurrentSlide] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [touchStart, setTouchStart] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [touchEnd, setTouchEnd] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isTablet, setIsTablet] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showInitialUI, setShowInitialUI] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isBotTyping, setIsBotTyping] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isBotThinking, setIsBotThinking] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [sseConnection, setSseConnection] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isFirstMessage, setIsFirstMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [shouldScrollToTop, setShouldScrollToTop] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isAtBottom, setIsAtBottom] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [lastMessageCount, setLastMessageCount] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const carouselRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const desktopMessagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const mobileMessagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const tabletMessagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // Detect iOS for targeted fixes\n    const isIOS = ()=>{\n        if (true) {\n            return /iPhone|iPad|iPod/i.test(navigator.userAgent);\n        }\n        return false;\n    };\n    const username = slug;\n    // Utility function to get current container\n    const getCurrentContainer = ()=>{\n        const screenWidth = window.innerWidth;\n        const isDesktopLayout = screenWidth >= 1024;\n        const isTabletLayout = screenWidth >= 768 && screenWidth < 1024;\n        if (isDesktopLayout) {\n            return desktopMessagesContainerRef.current;\n        } else if (isTabletLayout) {\n            return tabletMessagesContainerRef.current;\n        } else {\n            return mobileMessagesContainerRef.current;\n        }\n    };\n    // Check if user is scrolled to bottom\n    const isScrolledToBottom = ()=>{\n        const container = getCurrentContainer();\n        if (!container) return true;\n        const threshold = 100; // pixels from bottom\n        return container.scrollTop + container.clientHeight >= container.scrollHeight - threshold;\n    };\n    // Dynamic margin calculation for message pairs\n    const getDynamicBottomMargin = (pairIndex, totalPairs)=>{\n        const isLastPair = pairIndex === totalPairs - 1;\n        const isFirstPair = pairIndex === 0;\n        // Rule 1: Only the current last pair gets positioning margin when at bottom\n        if (isLastPair && !isFirstPair && isAtBottom) {\n            return isMobile ? 'calc(70vh - 160px)' : 'calc(75vh - 120px)';\n        }\n        // Rule 2: When scrolled up or not last pair, no positioning margin\n        return '0';\n    };\n    // Get minimum height for message pairs\n    const getMinHeight = (isLastPair, isFirstPair)=>{\n        if (isLastPair && isFirstPair) {\n            // For the very first message, ensure it appears at optimal reading position\n            return isMobile ? 'calc(30vh - 60px)' : 'calc(25vh - 20px)';\n        }\n        return 'auto';\n    };\n    // Get top margin for message pairs\n    const getTopMargin = (isFirstPair)=>{\n        return isFirstPair ? isMobile ? '60px' : '80px' : '0';\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (username && \"object\" !== \"undefined\") {\n                fetchCustomer(username);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        username\n    ]);\n    const fetchCustomer = async (customerName)=>{\n        try {\n            setLoadingState(true);\n            clearError();\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"\".concat(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_SETTINGS, \"?customerName=\").concat(customerName));\n            if (response.data && response.data.customerName) {\n                const metadataFromResponse = response.data.metaData;\n                console.log('Storing metadata in context:', metadataFromResponse);\n                updateMetadata(metadataFromResponse);\n                // Keep existing localStorage for backward compatibility\n                localStorage.setItem(\"customerName_userId\", response.data.customerName);\n                localStorage.setItem(\"BusinessName\", response.data.businessName);\n                window.dispatchEvent(new CustomEvent('businessNameLoaded', {\n                    detail: {\n                        businessName: response.data.businessName\n                    }\n                }));\n                const existingUserId = localStorage.getItem(\"userID\");\n                if (existingUserId) {\n                    await fetchExistingMessages(existingUserId);\n                } else {\n                    await initializeChatSession();\n                }\n            } else {\n                await initializeChatSession();\n            }\n        } catch (error) {\n            console.error('Error fetching customer data:', error);\n            setErrorState('Failed to load customer settings');\n            // Fallback metadata\n            updateMetadata({\n                customerName: customerName,\n                businessName: \"Driply\"\n            });\n            localStorage.setItem(\"BusinessName\", \"Driply\");\n            window.dispatchEvent(new CustomEvent('businessNameLoaded', {\n                detail: {\n                    businessName: \"Driply\"\n                }\n            }));\n            await initializeChatSession();\n        } finally{\n            setLoadingState(false);\n        }\n    };\n    const fetchExistingMessages = async (userId)=>{\n        console.log(\"metadata : \", metadata);\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"\".concat(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_MESSAGES, \"?userId=\").concat(userId));\n            if (response.data && response.data.length > 0) {\n                setMessages(response.data.map((msg)=>({\n                        text: msg.message,\n                        timestamp: new Date(msg.createdAt).getTime(),\n                        type: msg.type,\n                        source: msg.source\n                    })));\n            }\n            connectToSSE(userId);\n        } catch (error) {\n            await initializeChatSession();\n        }\n    };\n    const initializeChatSession = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_INIT, {\n                customerName: username\n            });\n            if (response.data && response.data.userId) {\n                localStorage.setItem(\"userId\", response.data.userId);\n                localStorage.setItem(\"userID\", response.data._id);\n                await fetchExistingMessages(response.data._id);\n            }\n        } catch (error) {\n            if (!localStorage.getItem(\"BusinessName\")) {\n                localStorage.setItem(\"BusinessName\", \"Driply\");\n                window.dispatchEvent(new CustomEvent('businessNameLoaded', {\n                    detail: {\n                        businessName: \"Driply\"\n                    }\n                }));\n            }\n        }\n    };\n    const connectToSSE = (userId)=>{\n        try {\n            if (sseConnection) {\n                sseConnection.close();\n            }\n            const sseUrl = (0,_utils_config__WEBPACK_IMPORTED_MODULE_4__.getSSEUrl)(userId);\n            const eventSource = new EventSource(sseUrl);\n            setSseConnection(eventSource);\n            eventSource.addEventListener(\"message\", (param)=>{\n                let { data } = param;\n                try {\n                    const contents = JSON.parse(data);\n                    handleSSEMessage(contents);\n                } catch (error) {\n                // Handle parsing error silently\n                }\n            });\n            eventSource.addEventListener(\"error\", ()=>{\n            // Handle connection error silently\n            });\n        } catch (error) {\n        // Handle SSE connection error silently\n        }\n    };\n    const handleSSEMessage = (data)=>{\n        const subtype = data.subType || data.subtype || data.type || \"UNKNOWN\";\n        const content = data.content || \"\";\n        const message = data.message || data.text || \"\";\n        if ([\n            \"TYPING\",\n            \"THINKING\",\n            \"BEHAVIOUR_MESSAGE\"\n        ].includes(subtype)) {\n            switch(subtype){\n                case \"TYPING\":\n                    setIsBotTyping(true);\n                    setIsBotThinking(false);\n                    break;\n                case \"THINKING\":\n                    setIsBotThinking(true);\n                    setIsBotTyping(false);\n                    break;\n                case \"BEHAVIOUR_MESSAGE\":\n                    setIsBotTyping(false);\n                    setIsBotThinking(false);\n                    break;\n            }\n            return;\n        }\n        const displayText = message || content;\n        if (!displayText) return;\n        const displayType = [\n            \"TEXT\",\n            \"MESSAGE\",\n            \"DATA_MESSAGE\"\n        ].includes(subtype) ? subtype : \"UNKNOWN\";\n        setMessages((prev)=>[\n                ...prev,\n                {\n                    text: displayText,\n                    timestamp: Date.now(),\n                    type: displayType,\n                    source: \"BOT\"\n                }\n            ]);\n        setIsBotTyping(false);\n        setIsBotThinking(false);\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (message.trim()) {\n            const userMessage = message.trim();\n            const isFirstMsg = messages.length === 0;\n            setMessages((prev)=>[\n                    ...prev,\n                    {\n                        text: userMessage,\n                        timestamp: Date.now(),\n                        type: \"TEXT\",\n                        source: \"USER\"\n                    }\n                ]);\n            setMessage(\"\");\n            setIsTyping(false);\n            // For first message, set flag to scroll to top\n            if (isFirstMsg) {\n                setShouldScrollToTop(true);\n                setIsFirstMessage(false);\n            }\n            // Show loading dots immediately after sending message\n            setIsBotThinking(true);\n            setIsBotTyping(false);\n            // Reset textarea\n            if (inputRef.current) {\n                inputRef.current.style.height = \"104px\";\n                inputRef.current.scrollTop = 0;\n                inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n                inputRef.current.classList.add(\"reset-height\");\n            }\n            try {\n                const userId = localStorage.getItem(\"userID\");\n                if (userId) {\n                    await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_SEND, {\n                        userId: userId,\n                        message: userMessage,\n                        \"typs\": \"TEXT\",\n                        \"source\": \"USER\",\n                        \"isTest\": query.isTest === '1' ? true : false\n                    });\n                }\n            } catch (error) {\n                // Hide loading dots on error\n                setIsBotThinking(false);\n                setIsBotTyping(false);\n                setMessages((prev)=>[\n                        ...prev,\n                        {\n                            text: \"Sorry, there was an error sending your message. Please try again.\",\n                            timestamp: Date.now(),\n                            type: \"TEXT\",\n                            source: \"BOT\"\n                        }\n                    ]);\n            }\n        }\n    };\n    const handleTextareaResize = (textarea)=>{\n        if (textarea) {\n            textarea.style.height = \"104px\";\n            const scrollHeight = textarea.scrollHeight;\n            if (isIOS()) {\n                if (scrollHeight > 48) {\n                    textarea.style.overflowY = \"auto\";\n                } else {\n                    textarea.style.overflowY = \"hidden\";\n                }\n            } else {\n                const newHeight = Math.min(250, Math.max(104, scrollHeight));\n                textarea.style.height = \"\".concat(newHeight, \"px\");\n                textarea.style.overflowY = scrollHeight > newHeight ? \"auto\" : \"hidden\";\n            }\n        }\n    };\n    const handleInputChange = (e)=>{\n        const value = e.target.value;\n        setMessage(value);\n        handleTextareaResize(e.target);\n        if (messages.length === 0) {\n            setIsTyping(value.length > 0);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length > 0 && inputRef.current && !message) {\n                const timer = setTimeout({\n                    \"ChatInterface.useEffect.timer\": ()=>{\n                        if (inputRef.current) {\n                            inputRef.current.style.height = \"104px\";\n                            inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n                            inputRef.current.classList.add(\"reset-height\");\n                        }\n                    }\n                }[\"ChatInterface.useEffect.timer\"], 50);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                })[\"ChatInterface.useEffect\"];\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages,\n        message\n    ]);\n    // Handle scroll events to detect bottom position\n    const handleScroll = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"ChatInterface.useCallback[handleScroll]\": ()=>{\n            const atBottom = isScrolledToBottom();\n            if (atBottom !== isAtBottom) {\n                setIsAtBottom(atBottom);\n            }\n        }\n    }[\"ChatInterface.useCallback[handleScroll]\"], [\n        isAtBottom\n    ]);\n    // Track message count changes\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length !== lastMessageCount) {\n                setIsAtBottom(true); // New message = scroll to bottom\n                setLastMessageCount(messages.length);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages.length,\n        lastMessageCount\n    ]);\n    // Add scroll listeners to containers\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const containers = [\n                desktopMessagesContainerRef.current,\n                mobileMessagesContainerRef.current,\n                tabletMessagesContainerRef.current\n            ].filter(Boolean);\n            containers.forEach({\n                \"ChatInterface.useEffect\": (container)=>{\n                    container.addEventListener('scroll', handleScroll, {\n                        passive: true\n                    });\n                }\n            }[\"ChatInterface.useEffect\"]);\n            return ({\n                \"ChatInterface.useEffect\": ()=>{\n                    containers.forEach({\n                        \"ChatInterface.useEffect\": (container)=>{\n                            container.removeEventListener('scroll', handleScroll);\n                        }\n                    }[\"ChatInterface.useEffect\"]);\n                }\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], [\n        handleScroll\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length > 0) {\n                handleChatGPTScroll();\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages,\n        isBotTyping,\n        isBotThinking\n    ]);\n    const handleChatGPTScroll = ()=>{\n        setTimeout(()=>{\n            // Determine screen type and get appropriate container\n            const screenWidth = window.innerWidth;\n            const isDesktopLayout = screenWidth >= 1024;\n            const isTabletLayout = screenWidth >= 768 && screenWidth < 1024;\n            let container;\n            if (isDesktopLayout) {\n                container = desktopMessagesContainerRef.current;\n            } else if (isTabletLayout) {\n                container = tabletMessagesContainerRef.current;\n            } else {\n                container = mobileMessagesContainerRef.current;\n            }\n            if (!container) return;\n            const messageElements = container.querySelectorAll('.message-pair');\n            if (messageElements.length === 0) return;\n            // Get container dimensions and account for navbar offset\n            const containerHeight = container.clientHeight;\n            const containerRect = container.getBoundingClientRect();\n            const navbarHeight = 60; // h-15 = 60px\n            // Calculate the actual visible area accounting for navbar\n            const effectiveViewportTop = Math.max(0, navbarHeight - containerRect.top);\n            const lastMessagePair = messageElements[messageElements.length - 1];\n            if (shouldScrollToTop) {\n                // For first message, position at optimal reading position accounting for navbar\n                const targetPosition = containerHeight * (isMobile ? 0.3 : 0.25) + effectiveViewportTop;\n                const userMessageElement = lastMessagePair.querySelector('.flex.justify-end');\n                if (userMessageElement) {\n                    const userMessageTop = lastMessagePair.offsetTop + userMessageElement.offsetTop;\n                    const scrollPosition = Math.max(0, userMessageTop - targetPosition);\n                    container.scrollTo({\n                        top: scrollPosition,\n                        behavior: 'smooth'\n                    });\n                }\n                setShouldScrollToTop(false);\n            } else {\n                // For subsequent messages: Position at optimal reading position accounting for navbar\n                const targetPosition = containerHeight * (isMobile ? 0.25 : 0.2) + effectiveViewportTop;\n                const userMessageElement = lastMessagePair.querySelector('.flex.justify-end');\n                if (userMessageElement) {\n                    const userMessageTop = lastMessagePair.offsetTop + userMessageElement.offsetTop;\n                    const scrollPosition = Math.max(0, userMessageTop - targetPosition);\n                    requestAnimationFrame(()=>{\n                        container.scrollTo({\n                            top: scrollPosition,\n                            behavior: 'smooth'\n                        });\n                    });\n                }\n            }\n        }, 100); // Reduced timeout for more responsive feel\n    };\n    const renderMessagePairs = ()=>{\n        const pairs = [];\n        const userMessages = messages.filter((msg)=>msg.source === \"USER\");\n        const botMessages = messages.filter((msg)=>msg.source === \"BOT\");\n        // Create pairs of user and bot messages\n        for(let i = 0; i < userMessages.length; i++){\n            const userMsg = userMessages[i];\n            const botMsg = botMessages[i];\n            const isLastPair = i === userMessages.length - 1;\n            const isFirstPair = i === 0;\n            pairs.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"message-pair flex flex-col justify-start\",\n                style: {\n                    // Dynamic height based on content, with special handling for positioning\n                    minHeight: getMinHeight(isLastPair, isFirstPair),\n                    // Add top margin to account for navbar\n                    marginTop: getTopMargin(isFirstPair),\n                    paddingTop: isFirstPair ? '1rem' : '2rem',\n                    paddingBottom: isLastPair ? '2rem' : '1rem',\n                    // DYNAMIC MARGIN SYSTEM - only applies when at bottom and is last pair\n                    marginBottom: getDynamicBottomMargin(i, userMessages.length),\n                    // Smooth transition for margin changes\n                    transition: 'margin-bottom 0.3s ease-in-out'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-end mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-[16px] font-[400] rounded-3xl max-w-xs lg:max-w-lg break-words hyphens-auto bg-gray-100 \".concat(isMobile ? 'rounded-[15px] px-3 py-2' : 'px-4 py-3'),\n                            style: {\n                                overflowWrap: \"break-word\",\n                                wordBreak: \"break-word\",\n                                maxWidth: isMobile ? \"90%\" : undefined\n                            },\n                            children: userMsg.text\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 531,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 530,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-start\",\n                        children: botMsg ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-[16px] font-[400] rounded-3xl max-w-xs lg:max-w-lg break-words hyphens-auto bg-white \".concat(isMobile ? 'rounded-[15px] px-3 py-2' : 'px-4 py-3'),\n                            style: {\n                                overflowWrap: \"break-word\",\n                                wordBreak: \"break-word\",\n                                maxWidth: isMobile ? \"90%\" : undefined\n                            },\n                            children: botMsg.text\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 546,\n                            columnNumber: 15\n                        }, undefined) : // Show loading dots for the last pair if bot is thinking/typing\n                        i === userMessages.length - 1 && (isBotTyping || isBotThinking) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white \".concat(isMobile ? 'px-3 py-2 rounded-[15px]' : 'px-4 py-3 rounded-3xl'),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-black rounded-full loading-dot\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 561,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-black rounded-full loading-dot\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 562,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-black rounded-full loading-dot\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 563,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 560,\n                                columnNumber: 19\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 559,\n                            columnNumber: 17\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 544,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, \"pair-\".concat(i), true, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 513,\n                columnNumber: 9\n            }, undefined));\n        }\n        return pairs;\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSubmit(e);\n        }\n    };\n    const handleSuggestionClick = async (cardTitle)=>{\n        const isFirstMsg = messages.length === 0;\n        setMessages((prev)=>[\n                ...prev,\n                {\n                    text: cardTitle,\n                    timestamp: Date.now(),\n                    type: \"TEXT\",\n                    source: \"USER\"\n                }\n            ]);\n        setMessage(\"\");\n        setIsTyping(false);\n        // For first message, set flag to scroll to top\n        if (isFirstMsg) {\n            setShouldScrollToTop(true);\n            setIsFirstMessage(false);\n        }\n        // Show loading dots immediately after sending message\n        setIsBotThinking(true);\n        setIsBotTyping(false);\n        if (inputRef.current) {\n            inputRef.current.style.height = \"104px\";\n            inputRef.current.scrollTop = 0;\n            inputRef.current.style.overflowY = isIOS() ? \"hidden\" : \"auto\";\n            inputRef.current.classList.add(\"reset-height\");\n        }\n        try {\n            const userId = localStorage.getItem(\"userID\");\n            if (userId) {\n                await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(_utils_config__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.CHAT_SEND, {\n                    userId: userId,\n                    message: cardTitle,\n                    customerName: username\n                });\n            }\n        } catch (error) {\n            // Hide loading dots on error\n            setIsBotThinking(false);\n            setIsBotTyping(false);\n            setMessages((prev)=>[\n                    ...prev,\n                    {\n                        text: \"Sorry, there was an error sending your message. Please try again.\",\n                        timestamp: Date.now(),\n                        type: \"TEXT\",\n                        source: \"BOT\"\n                    }\n                ]);\n        }\n    };\n    const getMaxSlides = ()=>{\n        if (isMobile) return _utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.length;\n        return Math.ceil(_utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.length / 2);\n    };\n    const nextSlide = ()=>{\n        if (isMobile) {\n            setCurrentSlide((prev)=>prev < _utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.length - 1 ? prev + 1 : prev);\n        } else {\n            const maxSlides = getMaxSlides();\n            setCurrentSlide((prev)=>(prev + 1) % maxSlides);\n        }\n    };\n    const prevSlide = ()=>{\n        if (isMobile) {\n            setCurrentSlide((prev)=>prev > 0 ? prev - 1 : prev);\n        } else {\n            const maxSlides = getMaxSlides();\n            setCurrentSlide((prev)=>(prev - 1 + maxSlides) % maxSlides);\n        }\n    };\n    const handleNextSlide = ()=>{\n        nextSlide();\n    };\n    const handlePrevSlide = ()=>{\n        prevSlide();\n    };\n    const handleTouchStart = (e)=>{\n        setTouchStart(e.targetTouches[0].clientX);\n    };\n    const handleTouchMove = (e)=>{\n        setTouchEnd(e.targetTouches[0].clientX);\n    };\n    const handleTouchEnd = ()=>{\n        if (!touchStart || !touchEnd) return;\n        const swipeDistance = touchStart - touchEnd;\n        const minSwipeDistance = 50;\n        if (swipeDistance > minSwipeDistance) {\n            nextSlide();\n        }\n        if (swipeDistance < -minSwipeDistance) {\n            prevSlide();\n        }\n        setTouchStart(0);\n        setTouchEnd(0);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length === 0) {\n                setShowInitialUI(false);\n                const timer = setTimeout({\n                    \"ChatInterface.useEffect.timer\": ()=>setShowInitialUI(true)\n                }[\"ChatInterface.useEffect.timer\"], 60);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                })[\"ChatInterface.useEffect\"];\n            } else {\n                setShowInitialUI(false);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages.length\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const checkScreenSize = {\n                \"ChatInterface.useEffect.checkScreenSize\": ()=>{\n                    if (true) {\n                        const width = window.innerWidth;\n                        setIsMobile(width < 768);\n                        setIsTablet(width >= 768 && width < 1024);\n                    }\n                }\n            }[\"ChatInterface.useEffect.checkScreenSize\"];\n            checkScreenSize();\n            const handleResize = {\n                \"ChatInterface.useEffect.handleResize\": ()=>{\n                    setCurrentSlide(0);\n                    checkScreenSize();\n                }\n            }[\"ChatInterface.useEffect.handleResize\"];\n            if (true) {\n                window.addEventListener(\"resize\", handleResize);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>window.removeEventListener(\"resize\", handleResize)\n                })[\"ChatInterface.useEffect\"];\n            }\n        }\n    }[\"ChatInterface.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (inputRef.current && messages.length === 0) {\n                const shouldAutoFocus = window.innerWidth >= 768;\n                if (shouldAutoFocus) {\n                    const timer = setTimeout({\n                        \"ChatInterface.useEffect.timer\": ()=>{\n                            var _inputRef_current;\n                            (_inputRef_current = inputRef.current) === null || _inputRef_current === void 0 ? void 0 : _inputRef_current.focus();\n                        }\n                    }[\"ChatInterface.useEffect.timer\"], 100);\n                    return ({\n                        \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n                    })[\"ChatInterface.useEffect\"];\n                }\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages.length\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            return ({\n                \"ChatInterface.useEffect\": ()=>{\n                    if (sseConnection) {\n                        sseConnection.close();\n                    }\n                }\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], [\n        sseConnection\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-e43538de6c4adb41\" + \" \" + \"bg-white flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-e43538de6c4adb41\" + \" \" + \"hidden lg:flex flex-1 flex-col px-4 \",\n                children: messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-e43538de6c4adb41\" + \" \" + \"flex flex-1 items-center justify-center min-h-[calc(100vh-64px)] mt-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-e43538de6c4adb41\" + \" \" + \"flex flex-col items-center w-[768px] justify-center mx-auto \",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    transitionDelay: showInitialUI ? \"40ms\" : \"0ms\"\n                                },\n                                className: \"jsx-e43538de6c4adb41\" + \" \" + \"text-4xl text-gray-900 mb-6 text-center transition-all duration-700 \".concat(showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                children: \"How can I help you?\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 756,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                style: {\n                                    transitionDelay: showInitialUI ? \"120ms\" : \"0ms\"\n                                },\n                                className: \"jsx-e43538de6c4adb41\" + \" \" + \"relative w-full mb-6 flex flex-col items-center transition-all duration-500 \".concat(showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        ref: messages.length === 0 ? inputRef : null,\n                                        value: message,\n                                        onChange: handleInputChange,\n                                        onKeyDown: handleKeyPress,\n                                        placeholder: \"Ask anything\",\n                                        autoFocus: messages.length === 0,\n                                        rows: 1,\n                                        style: {\n                                            boxSizing: \"border-box\",\n                                            zIndex: 1001,\n                                            position: \"relative\"\n                                        },\n                                        className: \"jsx-e43538de6c4adb41\" + \" \" + \"pt-3 px-4 lg:p-4 pb-16 pr-12 w-full min-h-[104px] max-h-[250px] overflow-y-auto hide-scrollbar text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base shadow-md resize-none reset-height\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 775,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: message.trim().length === 0,\n                                        className: \"jsx-e43538de6c4adb41\" + \" \" + \"absolute right-3 bottom-3 w-9 h-9 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation \".concat(message.trim().length === 0 ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                            className: \"w-4 h-4 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 798,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 790,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 767,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    transitionDelay: showInitialUI ? \"200ms\" : \"0ms\",\n                                    zIndex: 10\n                                },\n                                className: \"jsx-e43538de6c4adb41\" + \" \" + \"relative w-full max-w-2xl transition-all duration-500 ease-in-out \".concat(isTyping ? \"opacity-0 pointer-events-none\" : showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-e43538de6c4adb41\" + \" \" + \"overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: carouselRef,\n                                            style: {\n                                                transform: \"translateX(-\".concat(currentSlide * 100, \"%)\")\n                                            },\n                                            className: \"jsx-e43538de6c4adb41\" + \" \" + \"flex transition-transform duration-300 ease-in-out\",\n                                            children: Array.from({\n                                                length: Math.ceil(_utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.length / 2)\n                                            }).map((_, slideIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-e43538de6c4adb41\" + \" \" + \"w-full flex-shrink-0 flex gap-2 justify-center\",\n                                                    children: _utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.slice(slideIndex * 2, slideIndex * 2 + 2).map((card, cardIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleSuggestionClick(card.title),\n                                                            style: {\n                                                                width: \"fit-content\"\n                                                            },\n                                                            className: \"jsx-e43538de6c4adb41\" + \" \" + \"py-2 px-3 rounded-[8px] bg-[#f6f6f6] text-left group transition-all duration-200 touch-manipulation\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-e43538de6c4adb41\" + \" \" + \"text-[16px] font-[600] text-black mb-0.5 leading-tight whitespace-nowrap\",\n                                                                    children: card.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                    lineNumber: 836,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-e43538de6c4adb41\" + \" \" + \"text-[16px] text-gray-500 leading-tight whitespace-nowrap\",\n                                                                    children: card.subtitle\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                    lineNumber: 839,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, slideIndex * 2 + cardIndex, true, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 830,\n                                                            columnNumber: 27\n                                                        }, undefined))\n                                                }, slideIndex, false, {\n                                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                    lineNumber: 822,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 814,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 813,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handlePrevSlide,\n                                        className: \"jsx-e43538de6c4adb41\" + \" \" + \"absolute left-28 top-1/2 transform -translate-y-1/2 -translate-x-6 w-8 h-8 bg-white border border-gray-200 rounded-full flex items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronLeft, {\n                                            className: \"w-3 h-3 text-gray-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 852,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 848,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleNextSlide,\n                                        className: \"jsx-e43538de6c4adb41\" + \" \" + \"absolute right-28 top-1/2 transform -translate-y-1/2 translate-x-6 w-8 h-8 bg-white border border-gray-200 rounded-full flex items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronRight, {\n                                            className: \"w-3 h-3 text-gray-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 858,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 854,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 801,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 755,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                    lineNumber: 754,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: desktopMessagesContainerRef,\n                            style: {\n                                overscrollBehavior: \"contain\",\n                                maxHeight: \"calc(100vh - 200px)\",\n                                minHeight: \"calc(100vh - 200px)\",\n                                marginTop: \"60px\" // Exact navbar height (h-15 = 60px)\n                            },\n                            className: \"jsx-e43538de6c4adb41\" + \" \" + \"flex-1 overflow-y-auto hide-scrollbar \".concat(!isAtBottom ? 'scrolled-up' : ''),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-e43538de6c4adb41\" + \" \" + \"w-full lg:w-[768px] mx-auto px-4\",\n                                children: [\n                                    renderMessagePairs(),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: messagesEndRef,\n                                        className: \"jsx-e43538de6c4adb41\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 877,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 875,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 865,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                zIndex: 1000,\n                                marginTop: \"20px\"\n                            },\n                            className: \"jsx-e43538de6c4adb41\" + \" \" + \"fixed bottom-0 left-0 right-0 p-4 mt-5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSubmit,\n                                    className: \"jsx-e43538de6c4adb41\" + \" \" + \"relative w-full lg:w-[768px] mx-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            ref: messages.length > 0 ? inputRef : null,\n                                            value: message,\n                                            onChange: handleInputChange,\n                                            onKeyDown: handleKeyPress,\n                                            placeholder: \"Ask anything\",\n                                            rows: 1,\n                                            style: {\n                                                boxSizing: \"border-box\",\n                                                zIndex: 1001,\n                                                position: \"relative\"\n                                            },\n                                            className: \"jsx-e43538de6c4adb41\" + \" \" + \"pt-3 px-4 md:px-5 pb-10 pr-12 w-full min-h-[104px] max-h-[250px] overflow-y-auto text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base shadow-lg resize-none reset-height hide-scrollbar\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 889,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: message.trim().length === 0,\n                                            className: \"jsx-e43538de6c4adb41\" + \" \" + \"absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation \".concat(message.trim().length === 0 ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                                className: \"w-4 h-4 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 911,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 903,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 885,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-e43538de6c4adb41\" + \" \" + \"text-center mt-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-e43538de6c4adb41\" + \" \" + \"text-xs text-gray-500\",\n                                        children: [\n                                            \"This chat is powered by\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                className: \"jsx-e43538de6c4adb41\" + \" \" + \"text-black\",\n                                                children: \"Driply.me\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 917,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                        lineNumber: 915,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 914,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                            lineNumber: 881,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 752,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-e43538de6c4adb41\" + \" \" + \"lg:hidden overflow-hidden fixed inset-0 flex flex-col  mt-10 \",\n                children: [\n                    messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-e43538de6c4adb41\" + \" \" + \"flex flex-col items-center pt-15 pb-4 px-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-e43538de6c4adb41\" + \" \" + \"hidden md:flex lg:hidden flex-col items-center\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 930,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 929,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    paddingBottom: \"260px\"\n                                },\n                                className: \"jsx-e43538de6c4adb41\" + \" \" + \"flex flex-col items-center justify-center flex-1 px-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"40ms\" : \"0ms\"\n                                    },\n                                    className: \"jsx-e43538de6c4adb41\" + \" \" + \"text-2xl md:text-4xl text-gray-900 text-center leading-relaxed transition-all duration-700 \".concat(showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                    children: \"How can I help you?\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 936,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 932,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: mobileMessagesContainerRef,\n                                style: {\n                                    overscrollBehavior: \"contain\",\n                                    maxHeight: \"calc(100vh - 340px)\",\n                                    minHeight: \"calc(100vh - 340px)\",\n                                    marginTop: \"60px\" // Exact navbar height (h-15 = 60px)\n                                },\n                                className: \"jsx-e43538de6c4adb41\" + \" \" + \"md:hidden flex-1 overflow-y-auto hide-scrollbar \".concat(!isAtBottom ? 'scrolled-up' : ''),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-e43538de6c4adb41\" + \" \" + \"w-full max-w-[803px] mx-auto px-4 pt-4\",\n                                    children: [\n                                        renderMessagePairs(),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: messagesEndRef,\n                                            className: \"jsx-e43538de6c4adb41\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 963,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 961,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 951,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: tabletMessagesContainerRef,\n                                style: {\n                                    overscrollBehavior: \"contain\",\n                                    maxHeight: \"calc(100vh - 200px)\",\n                                    minHeight: \"calc(100vh - 200px)\",\n                                    marginTop: \"60px\" // Exact navbar height (h-15 = 60px)\n                                },\n                                className: \"jsx-e43538de6c4adb41\" + \" \" + \"hidden md:block lg:hidden flex-1 overflow-y-auto hide-scrollbar \".concat(!isAtBottom ? 'scrolled-up' : ''),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-e43538de6c4adb41\" + \" \" + \"w-full max-w-[803px] mx-auto px-4 pt-4\",\n                                    children: [\n                                        renderMessagePairs(),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: messagesEndRef,\n                                            className: \"jsx-e43538de6c4adb41\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 980,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 978,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 968,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    minHeight: \"160px\",\n                                    zIndex: 1000,\n                                    paddingBottom: \"env(safe-area-inset-bottom, 0)\",\n                                    transform: \"translateZ(0)\",\n                                    backfaceVisibility: \"hidden\"\n                                },\n                                className: \"jsx-e43538de6c4adb41\" + \" \" + \"fixed bottom-0 left-0 right-0 bg-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        transform: \"translateZ(0)\"\n                                    },\n                                    className: \"jsx-e43538de6c4adb41\" + \" \" + \"px-4 py-4 bg-white\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            style: {\n                                                zIndex: 1001\n                                            },\n                                            className: \"jsx-e43538de6c4adb41\" + \" \" + \"relative bg-white\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    transform: \"translateZ(0)\"\n                                                },\n                                                className: \"jsx-e43538de6c4adb41\" + \" \" + \"relative w-full max-w-[890px] mx-auto bg-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        ref: messages.length > 0 ? inputRef : null,\n                                                        value: message,\n                                                        onChange: handleInputChange,\n                                                        onKeyDown: handleKeyPress,\n                                                        placeholder: \"Ask anything\",\n                                                        rows: 1,\n                                                        style: {\n                                                            boxSizing: \"border-box\",\n                                                            maxHeight: \"250px\",\n                                                            zIndex: 1001,\n                                                            overscrollBehavior: \"none\",\n                                                            position: \"relative\"\n                                                        },\n                                                        className: \"jsx-e43538de6c4adb41\" + \" \" + \"w-full min-h-[104px] pt-3 px-4 pb-10 pr-12 text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base resize-none overflow-y-auto reset-height\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1008,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        disabled: message.trim().length === 0,\n                                                        className: \"jsx-e43538de6c4adb41\" + \" \" + \"absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation \".concat(message.trim().length === 0 ? \"bg-[#E0E0E0] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                                            className: \"w-4 h-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 1032,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1024,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1004,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 999,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-e43538de6c4adb41\" + \" \" + \"text-center py-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-e43538de6c4adb41\" + \" \" + \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"This chat is powered by\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"jsx-e43538de6c4adb41\" + \" \" + \"text-black\",\n                                                        children: \"Driply.me\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1039,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1037,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1036,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 995,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                lineNumber: 985,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-e43538de6c4adb41\" + \" \" + \"absolute bottom-0 left-0 right-0 bg-white\",\n                        children: messages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"120ms\" : \"0ms\",\n                                        position: \"relative\",\n                                        zIndex: 10,\n                                        backgroundColor: \"white\",\n                                        paddingBottom: \"10px\"\n                                    },\n                                    className: \"jsx-e43538de6c4adb41\" + \" \" + \"\".concat(isMobile ? \"px-0\" : \"px-4\", \" pt-2 pb-2 transition-all duration-500 ease-in-out \").concat(isTyping ? \"opacity-0 pointer-events-none\" : showInitialUI ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-e43538de6c4adb41\" + \" \" + \"overflow-hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                ref: carouselRef,\n                                                style: {\n                                                    transform: \"translateX(-\".concat(currentSlide * (isMobile ? 50 : 100), \"%)\"),\n                                                    paddingLeft: isMobile ? \"1rem\" : \"0\"\n                                                },\n                                                onTouchStart: handleTouchStart,\n                                                onTouchMove: handleTouchMove,\n                                                onTouchEnd: handleTouchEnd,\n                                                className: \"jsx-e43538de6c4adb41\" + \" \" + \"flex transition-transform duration-300 ease-in-out\",\n                                                children: isMobile ? (()=>{\n                                                    const circularCards = [\n                                                        ..._utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS,\n                                                        ..._utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS\n                                                    ];\n                                                    return circularCards.map((card, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-e43538de6c4adb41\" + \" \" + \"flex-shrink-0 mt-3 mr-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleSuggestionClick(card.title),\n                                                                style: {\n                                                                    width: \"fit-content\"\n                                                                },\n                                                                className: \"jsx-e43538de6c4adb41\" + \" \" + \"py-3 px-2.5 rounded-[8px] bg-[#F6F6F6] text-left hover:bg-[#D6EDFF] transition-all duration-200 inline-block touch-manipulation\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-e43538de6c4adb41\" + \" \" + \"text-[16px] font-[600] text-black mb-0.5 leading-tight whitespace-nowrap overflow-hidden text-ellipsis\",\n                                                                        children: card.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                        lineNumber: 1099,\n                                                                        columnNumber: 31\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-e43538de6c4adb41\" + \" \" + \"text-[16px] text-gray-500 leading-tight whitespace-nowrap overflow-hidden text-ellipsis\",\n                                                                        children: card.subtitle\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                        lineNumber: 1102,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                lineNumber: 1092,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        }, \"\".concat(index, \"-\").concat(card.title), false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 1088,\n                                                            columnNumber: 27\n                                                        }, undefined));\n                                                })() : Array.from({\n                                                    length: Math.ceil(_utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.length / 2)\n                                                }).map((_, slideIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-e43538de6c4adb41\" + \" \" + \"w-full flex-shrink-0 flex gap-2 justify-center\",\n                                                        children: _utils_constants__WEBPACK_IMPORTED_MODULE_3__.SUGGESTION_CARDS.slice(slideIndex * 2, slideIndex * 2 + 2).map((card, cardIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleSuggestionClick(card.title),\n                                                                style: {\n                                                                    width: \"fit-content\"\n                                                                },\n                                                                className: \"jsx-e43538de6c4adb41\" + \" \" + \"py-2 px-2.5 rounded-[8px] bg-[#F6F6F6] text-left group hover:bg-[#D6EDFF] transition-all duration-200 inline-block touch-manipulation\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-e43538de6c4adb41\" + \" \" + \"text-[14px] font-[600] text-black mb-0.5 leading-tight whitespace-nowrap\",\n                                                                        children: card.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                        lineNumber: 1128,\n                                                                        columnNumber: 31\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-e43538de6c4adb41\" + \" \" + \"text-[16px] text-gray-500 leading-tight whitespace-nowrap\",\n                                                                        children: card.subtitle\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                        lineNumber: 1131,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                ]\n                                                            }, slideIndex * 2 + cardIndex, true, {\n                                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                                lineNumber: 1120,\n                                                                columnNumber: 29\n                                                            }, undefined))\n                                                    }, slideIndex, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1112,\n                                                        columnNumber: 25\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1069,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1068,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handlePrevSlide,\n                                            className: \"jsx-e43538de6c4adb41\" + \" \" + \"hidden md:block absolute left-28 top-1/2 transform -translate-y-1/2 -translate-x-6 w-8 h-8 bg-white border border-gray-200 rounded-full items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronLeft, {\n                                                className: \"w-3 h-3 text-gray-600  ms-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1146,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1142,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleNextSlide,\n                                            className: \"jsx-e43538de6c4adb41\" + \" \" + \"hidden md:block absolute right-28 top-1/2 transform -translate-y-1/2 translate-x-6 w-8 h-8 bg-white border border-gray-200 rounded-full items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaChevronRight, {\n                                                className: \"w-3 h-3 text-gray-600 ms-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1152,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1148,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1052,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        transitionDelay: showInitialUI ? \"200ms\" : \"0ms\",\n                                        position: \"relative\",\n                                        zIndex: 5,\n                                        paddingBottom: \"env(safe-area-inset-bottom)\"\n                                    },\n                                    className: \"jsx-e43538de6c4adb41\" + \" \" + \"px-4 bg-white transition-all duration-500 \".concat(showInitialUI ? \"opacity-100 translate-y-0\" : \"translate-y-4\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            className: \"jsx-e43538de6c4adb41\" + \" \" + \"relative\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-e43538de6c4adb41\" + \" \" + \"relative w-full max-w-[890px] mx-auto\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        ref: messages.length === 0 ? inputRef : null,\n                                                        value: message,\n                                                        onChange: handleInputChange,\n                                                        onKeyDown: handleKeyPress,\n                                                        placeholder: \"Ask anything\",\n                                                        rows: 1,\n                                                        style: {\n                                                            boxSizing: \"border-box\",\n                                                            maxHeight: \"250px\",\n                                                            fontSize: \"16px\",\n                                                            zIndex: 1001,\n                                                            overscrollBehaviorY: \"contain\",\n                                                            position: \"relative\"\n                                                        },\n                                                        className: \"jsx-e43538de6c4adb41\" + \" \" + \"w-full min-h-[104px] pt-3 px-4 pb-10 pr-12 text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base resize-none overflow-y-auto reset-height\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1169,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        disabled: message.trim().length === 0,\n                                                        className: \"jsx-e43538de6c4adb41\" + \" \" + \"absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation \".concat(message.trim().length === 0 ? \"bg-[#D4D6CE] text-white cursor-not-allowed\" : \"bg-black text-white hover:bg-gray-800\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaChevronLeft_FaChevronRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_7__.FaArrowUp, {\n                                                            className: \"w-4 h-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                            lineNumber: 1194,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1186,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1168,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1167,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-e43538de6c4adb41\" + \" \" + \"text-center py-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-e43538de6c4adb41\" + \" \" + \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"This chat is powered by\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"jsx-e43538de6c4adb41\" + \" \" + \"text-black\",\n                                                        children: \"Driply.me\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                        lineNumber: 1201,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                                lineNumber: 1199,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                            lineNumber: 1198,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                                    lineNumber: 1157,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                        lineNumber: 1048,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n                lineNumber: 926,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"e43538de6c4adb41\",\n                children: '.loading-dot{-webkit-animation:loading-dots 1.4s infinite ease-in-out both;-moz-animation:loading-dots 1.4s infinite ease-in-out both;-o-animation:loading-dots 1.4s infinite ease-in-out both;animation:loading-dots 1.4s infinite ease-in-out both}.loading-dot:nth-child(1){-webkit-animation-delay:-.32s;-moz-animation-delay:-.32s;-o-animation-delay:-.32s;animation-delay:-.32s}.loading-dot:nth-child(2){-webkit-animation-delay:-.16s;-moz-animation-delay:-.16s;-o-animation-delay:-.16s;animation-delay:-.16s}.loading-dot:nth-child(3){-webkit-animation-delay:0s;-moz-animation-delay:0s;-o-animation-delay:0s;animation-delay:0s}@-webkit-keyframes loading-dots{0%,80%,100%{-webkit-transform:scale(1);transform:scale(1);opacity:1}40%{-webkit-transform:scale(1.5);transform:scale(1.5);opacity:.7}}@-moz-keyframes loading-dots{0%,80%,100%{-moz-transform:scale(1);transform:scale(1);opacity:1}40%{-moz-transform:scale(1.5);transform:scale(1.5);opacity:.7}}@-o-keyframes loading-dots{0%,80%,100%{-o-transform:scale(1);transform:scale(1);opacity:1}40%{-o-transform:scale(1.5);transform:scale(1.5);opacity:.7}}@keyframes loading-dots{0%,80%,100%{-webkit-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1);opacity:1}40%{-webkit-transform:scale(1.5);-moz-transform:scale(1.5);-o-transform:scale(1.5);transform:scale(1.5);opacity:.7}}.hide-scrollbar{-ms-overflow-style:none!important;scrollbar-width:none!important}.hide-scrollbar::-webkit-scrollbar{display:none!important}.message-pair{-webkit-transition:opacity.3s ease-in-out,-webkit-transform.3s ease-in-out;-moz-transition:opacity.3s ease-in-out,-moz-transform.3s ease-in-out;-o-transition:opacity.3s ease-in-out,-o-transform.3s ease-in-out;transition:opacity.3s ease-in-out,-webkit-transform.3s ease-in-out;transition:opacity.3s ease-in-out,-moz-transform.3s ease-in-out;transition:opacity.3s ease-in-out,-o-transform.3s ease-in-out;transition:opacity.3s ease-in-out,transform.3s ease-in-out}[ref*=\"MessagesContainerRef\"]{scroll-behavior:smooth}.message-pair:last-child{opacity:1;-webkit-transform:translatey(0);-moz-transform:translatey(0);-ms-transform:translatey(0);-o-transform:translatey(0);transform:translatey(0)}.message-pair:not(:last-child){opacity:.85}.message-pair:nth-last-child(n+3){opacity:.7}.message-pair:nth-last-child(n+5){opacity:.5}.scrolled-up .message-pair{opacity:.9!important}.scrolled-up .message-pair:last-child{opacity:1!important}@media only screen and (max-width:1023px){.lg\\\\\\\\.jsx-e43538de6c4adb41:hidden .absolute.bottom-0.jsx-e43538de6c4adb41{position:fixed!important;bottom:env(safe-area-inset-bottom,0);left:0;right:0;width:100%;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;z-index:1000;background-color:white}textarea.jsx-e43538de6c4adb41{-webkit-user-select:auto!important;-moz-user-select:auto!important;-ms-user-select:auto!important;user-select:auto!important;-webkit-appearance:none;-moz-appearance:none;-ms-appearance:none;appearance:none;-ms-scroll-chaining:none;overscroll-behavior:none;line-height:24px;max-height:250px!important;position:relative!important;-webkit-transform:translatez(0);-moz-transform:translatez(0);-ms-transform:translatez(0);-o-transform:translatez(0);transform:translatez(0);-webkit-backface-visibility:hidden;-moz-backface-visibility:hidden;backface-visibility:hidden;-webkit-perspective:1000;-moz-perspective:1000;perspective:1000;z-index:1001!important;background-color:white}.lg\\\\\\\\.jsx-e43538de6c4adb41:hidden form.jsx-e43538de6c4adb41{position:relative!important;z-index:1001;background-color:white}button[type=\"submit\"].jsx-e43538de6c4adb41{position:absolute!important;right:12px;bottom:12px;z-index:1002!important;-webkit-tap-highlight-color:transparent;-ms-touch-action:-ms-manipulation;-ms-touch-action:manipulation;touch-action:manipulation;-webkit-transform:translatez(0);-moz-transform:translatez(0);-ms-transform:translatez(0);-o-transform:translatez(0);transform:translatez(0)}.lg\\\\\\\\.jsx-e43538de6c4adb41:hidden .bg-white.jsx-e43538de6c4adb41{background-color:white!important}}@media only screen and (min-width:1024px){textarea.jsx-e43538de6c4adb41{max-height:250px!important;overflow-y:auto!important;-ms-scroll-chaining:none;overscroll-behavior:contain}.reset-height.jsx-e43538de6c4adb41{min-height:104px!important;max-height:250px!important;overflow-y:auto!important}}.fixed-bottom.jsx-e43538de6c4adb41{position:fixed!important;bottom:0;left:0;right:0;background:white}'\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\components\\\\ChatInterface.jsx\",\n        lineNumber: 750,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatInterface, \"rVXT4NmLTWQ5AbHzbpGibd4ccew=\", false, function() {\n    return [\n        _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_5__.useChatContext\n    ];\n});\n_c = ChatInterface;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatInterface);\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ChatInterface.jsx\n"));

/***/ })

});