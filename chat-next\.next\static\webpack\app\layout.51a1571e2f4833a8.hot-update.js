"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"13e202d47efc\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJEOlxcTGFwdG9wIGRhdGFcXERSSVBMWS1DSEFUXFxjaGF0LW5leHRcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjEzZTIwMmQ0N2VmY1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/ChatContext.jsx":
/*!**************************************!*\
  !*** ./src/contexts/ChatContext.jsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatProvider: () => (/* binding */ ChatProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useChatContext: () => (/* binding */ useChatContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useChatContext,ChatProvider,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n// Create the context\nconst ChatContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\n// Custom hook to use the chat context\nconst useChatContext = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ChatContext);\n    if (!context) {\n        throw new Error('useChatContext must be used within a ChatProvider');\n    }\n    return context;\n};\n_s(useChatContext, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// Chat Provider component\nconst ChatProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    console.log(\"ChatProvider\", children);\n    const [metadata, setMetadata] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: null,\n        keywords: null,\n        description: null,\n        image: null\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [settingData, setSettingData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        customerName: null,\n        businessName: null,\n        chatDesignSettings: null,\n        details: null,\n        firstSentence: null,\n        isActive: Boolean,\n        metaData: null,\n        suggestedTopics: null\n    });\n    // Function to update metadata\n    const updateMetadata = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[updateMetadata]\": (newMetadata)=>{\n            setMetadata({\n                \"ChatProvider.useCallback[updateMetadata]\": (prevMetadata)=>({\n                        ...prevMetadata,\n                        ...newMetadata\n                    })\n            }[\"ChatProvider.useCallback[updateMetadata]\"]);\n        }\n    }[\"ChatProvider.useCallback[updateMetadata]\"], []);\n    // Function to clear metadata\n    const clearMetadata = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[clearMetadata]\": ()=>{\n            setMetadata({\n                title: null,\n                keywords: null,\n                description: null,\n                image: null\n            });\n        }\n    }[\"ChatProvider.useCallback[clearMetadata]\"], []);\n    // Function to set loading state\n    const setLoadingState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[setLoadingState]\": (isLoading)=>{\n            setLoading(isLoading);\n        }\n    }[\"ChatProvider.useCallback[setLoadingState]\"], []);\n    // Function to set error state\n    const setErrorState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[setErrorState]\": (errorMessage)=>{\n            setError(errorMessage);\n        }\n    }[\"ChatProvider.useCallback[setErrorState]\"], []);\n    // Function to clear error\n    const clearError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[clearError]\": ()=>{\n            setError(null);\n        }\n    }[\"ChatProvider.useCallback[clearError]\"], []);\n    const value = {\n        metadata,\n        loading,\n        error,\n        updateMetadata,\n        clearMetadata,\n        setLoadingState,\n        setErrorState,\n        clearError,\n        // Helper getters for easy access\n        getTitle: ()=>metadata.title,\n        getKeywords: ()=>metadata.keywords,\n        getDescription: ()=>metadata.description,\n        getImage: ()=>metadata.image,\n        hasMetadata: ()=>metadata.title !== null || metadata.keywords !== null || metadata.description !== null\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\contexts\\\\ChatContext.jsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(ChatProvider, \"SXsQh1lvMIvwqR6DZYz5EZWaif8=\");\n_c = ChatProvider;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatContext);\nvar _c;\n$RefreshReg$(_c, \"ChatProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/ChatContext.jsx\n"));

/***/ })

});