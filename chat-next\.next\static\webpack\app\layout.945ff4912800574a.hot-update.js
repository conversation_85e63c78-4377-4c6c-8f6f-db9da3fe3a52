"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"bc907cf079e9\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJEOlxcTGFwdG9wIGRhdGFcXERSSVBMWS1DSEFUXFxjaGF0LW5leHRcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImJjOTA3Y2YwNzllOVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/ChatContext.jsx":
/*!**************************************!*\
  !*** ./src/contexts/ChatContext.jsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatProvider: () => (/* binding */ ChatProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useChatContext: () => (/* binding */ useChatContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useChatContext,ChatProvider,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n// Create the context\nconst ChatContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\n// Custom hook to use the chat context\nconst useChatContext = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ChatContext);\n    if (!context) {\n        throw new Error('useChatContext must be used within a ChatProvider');\n    }\n    return context;\n};\n_s(useChatContext, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// Chat Provider component\nconst ChatProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    console.log(\"ChatProvider\", children);\n    const [metadata, setMetadata] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: null,\n        keywords: null,\n        description: null,\n        image: null\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [settingData, setSettingData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        customerName: null,\n        businessName: null,\n        chatDesignSettings: null,\n        details: null,\n        firstSentence: null,\n        isActive: null,\n        metaData: null,\n        suggestedTopics: null\n    });\n    // Function that update setting response data\n    const updateSettingData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[updateSettingData]\": (newSettingData)=>{\n            setSettingData({\n                \"ChatProvider.useCallback[updateSettingData]\": (prevSettingData)=>({\n                        ...prevSettingData,\n                        ...newSettingData\n                    })\n            }[\"ChatProvider.useCallback[updateSettingData]\"]);\n        }\n    }[\"ChatProvider.useCallback[updateSettingData]\"], []);\n    // Function to update metadata\n    const updateMetadata = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[updateMetadata]\": (newMetadata)=>{\n            setMetadata({\n                \"ChatProvider.useCallback[updateMetadata]\": (prevMetadata)=>({\n                        ...prevMetadata,\n                        ...newMetadata\n                    })\n            }[\"ChatProvider.useCallback[updateMetadata]\"]);\n        }\n    }[\"ChatProvider.useCallback[updateMetadata]\"], []);\n    // Function to clear metadata\n    const clearMetadata = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[clearMetadata]\": ()=>{\n            setMetadata({\n                title: null,\n                keywords: null,\n                description: null,\n                image: null\n            });\n        }\n    }[\"ChatProvider.useCallback[clearMetadata]\"], []);\n    // Function to set loading state\n    const setLoadingState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[setLoadingState]\": (isLoading)=>{\n            setLoading(isLoading);\n        }\n    }[\"ChatProvider.useCallback[setLoadingState]\"], []);\n    // Function to set error state\n    const setErrorState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[setErrorState]\": (errorMessage)=>{\n            setError(errorMessage);\n        }\n    }[\"ChatProvider.useCallback[setErrorState]\"], []);\n    // Function to clear error\n    const clearError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[clearError]\": ()=>{\n            setError(null);\n        }\n    }[\"ChatProvider.useCallback[clearError]\"], []);\n    const value = {\n        metadata,\n        loading,\n        error,\n        updateMetadata,\n        clearMetadata,\n        setLoadingState,\n        setErrorState,\n        clearError,\n        settingData,\n        updateSettingData,\n        // Helper getters for easy access\n        getTitle: ()=>metadata.title,\n        getKeywords: ()=>metadata.keywords,\n        getDescription: ()=>metadata.description,\n        getImage: ()=>metadata.image,\n        hasMetadata: ()=>metadata.title !== null || metadata.keywords !== null || metadata.description !== null\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Laptop data\\\\DRIPLY-CHAT\\\\chat-next\\\\src\\\\contexts\\\\ChatContext.jsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(ChatProvider, \"nLy0tUJbA7PeciJZxlikySFYUIY=\");\n_c = ChatProvider;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatContext);\nvar _c;\n$RefreshReg$(_c, \"ChatProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/ChatContext.jsx\n"));

/***/ })

});