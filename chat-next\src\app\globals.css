@import "tailwindcss";
@import url('https://api.fontshare.com/v2/css?f[]=general-sans@400,500,600,700&display=swap');

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --font-wix-madefor: var(--font-wix-madefor-text);
}



body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-wix-madefor-text), Arial, Helvetica, sans-serif;
}

.hide-scrollbar {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 10+ */
}
.hide-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome/Safari/Webkit */
}

/* Chat layout - proper separation of message area and input area */
.chat-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.chat-messages-area {
  flex: 1;
  overflow-y: auto;
  margin-top: 60px; /* navbar height */
}

.chat-input-area {
  flex-shrink: 0;
  background: white;
  border-top: 1px solid #e5e7eb;
}

.chat-input-desktop {
  height: 180px;
}

.chat-input-tablet {
  height: 200px;
}

.chat-input-mobile {
  height: 280px;
}
