@import "tailwindcss";
@import url('https://api.fontshare.com/v2/css?f[]=general-sans@400,500,600,700&display=swap');

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --font-wix-madefor: var(--font-wix-madefor-text);
}



body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-wix-madefor-text), Arial, Helvetica, sans-serif;
}

.hide-scrollbar {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 10+ */
}
.hide-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome/Safari/Webkit */
}

/* Fixed height chat layout - intelligent scrolling system */
.chat-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.chat-messages-container {
  overflow-y: auto;
  background: white;
  /* Fixed heights calculated to exclude navbar and input */
}

.chat-messages-desktop {
  height: calc(100vh - 60px - 180px); /* navbar + input */
}

.chat-messages-tablet {
  height: calc(100vh - 60px - 200px); /* navbar + input */
}

.chat-messages-mobile {
  height: calc(100vh - 60px - 280px); /* navbar + input */
}

.chat-input-fixed {
  position: relative;
  flex-shrink: 0;
  background: white;
  border-top: 1px solid #e5e7eb;
  z-index: 1000;
}

.chat-input-desktop {
  height: 180px;
}

.chat-input-tablet {
  height: 200px;
}

.chat-input-mobile {
  height: 280px;
}

/* Message item styling for height calculations */
.message-item {
  /* Ensure consistent spacing for height calculations */
  margin-bottom: 1.5rem; /* 24px - matches space-y-6 */
}

.message-item:last-child {
  margin-bottom: 0;
}
