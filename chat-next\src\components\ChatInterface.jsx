"use client";
import React, { useState, useEffect, useRef } from "react";
import {
  getSSEUrl,
  API_ENDPOINTS,
  EXTERNAL_API_ENDPOINTS,
} from "../utils/config";
import axios from "axios";
import { useChatContext } from "../contexts/ChatContext";

// Import custom hooks
import { useCarousel } from "../hooks/useCarousel";
import { useDeviceDetection } from "../hooks/useDeviceDetection";
import { useMessagePairing } from "../hooks/useMessagePairing";

// Import components
import MessagePairs from "./MessagePairs";
import CarouselSection from "./CarouselSection";
import InputSection from "./InputSection";
import { GlobalLoadingIndicator } from "./LoadingIndicators";

// Import styles
import "../styles/ChatInterface.css";

const ChatInterface = ({ slug, query }) => {
  // Context API
  const {
    metadata,
    updateMetadata,
    setLoadingState,
    setErrorState,
    clearError,
    settingData,
    updateSettingData,
    getCustomerName,
    getBusinessName,
    hasMetadata,
    loading: contextLoading,
    error: contextError,
  } = useChatContext();

  // Custom hooks
  const carousel = useCarousel(settingData);
  const { isMobile, isTablet } = useDeviceDetection(carousel.resetSlide);
  const messagePairing = useMessagePairing();

  // Local state
  const [message, setMessage] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [showInitialUI, setShowInitialUI] = useState(false);
  const [isBotTyping, setIsBotTyping] = useState(false);
  const [isBotThinking, setIsBotThinking] = useState(false);
  const [sseConnection, setSseConnection] = useState(null);
  const [isFirstMessage, setIsFirstMessage] = useState(true);
  const [shouldScrollToTop, setShouldScrollToTop] = useState(false);

  // Refs
  const messagesEndRef = useRef(null);
  const carouselRef = useRef(null);
  const inputRef = useRef(null);
  const desktopMessagesContainerRef = useRef(null);
  const mobileMessagesContainerRef = useRef(null);
  const tabletMessagesContainerRef = useRef(null);

  // Debug: Log state updates when they actually happen
  useEffect(() => {
    if (
      metadata &&
      Object.keys(metadata).some((key) => metadata[key] !== null)
    ) {
      console.log("✅ METADATA STATE UPDATED:", metadata);
    }
  }, [metadata]);

  useEffect(() => {
    if (
      settingData &&
      Object.keys(settingData).some((key) => settingData[key] !== null)
    ) {
      console.log("✅ SETTING DATA STATE UPDATED:", settingData);
    }
  }, [settingData]);

  // Cleanup pending queries that are too old (timeout mechanism)
  useEffect(() => {
    const cleanup = setInterval(() => {
      const now = Date.now();
      const TIMEOUT_MS = 60000; // 60 seconds timeout

      setPendingQueries((prev) => {
        const updated = new Map(prev);
        let hasChanges = false;

        for (const [queryId, queryData] of prev.entries()) {
          if (now - queryData.sentAt > TIMEOUT_MS) {
            updated.delete(queryId);
            hasChanges = true;
          }
        }

        return hasChanges ? updated : prev;
      });
    }, 10000); // Check every 10 seconds

    return () => clearInterval(cleanup);
  }, []);
  // Ensure carousel starts from first card on initial load
  useEffect(() => {
    if (settingData?.suggestedTopics?.length > 0) {
      carousel.resetSlide(); // Always start from first card
    }
  }, [settingData?.suggestedTopics?.length, carousel]);



  // Detect iOS for targeted fixes
  const isIOS = () => {
    if (typeof window !== "undefined") {
      return /iPhone|iPad|iPod/i.test(navigator.userAgent);
    }
    return false;
  };

  const username = slug;

  useEffect(() => {
    if (username && typeof window !== "undefined") {
      fetchCustomer(username);
    }
  }, [username]);

  const fetchCustomer = async (customerName) => {
    try {
      setLoadingState(true);

      clearError();

      const response = await axios.get(
        `${API_ENDPOINTS.CHAT_SETTINGS}?customerName=${customerName}`
      );

      // Extract data from axios response
      const responseData = response.data;
      const metadataFromResponse = responseData.metaData;

      updateSettingData(responseData);
      updateMetadata(metadataFromResponse);

      if (responseData && responseData.customerName) {
        //  localStorage for backward compatibility
        localStorage.setItem("customerName_userId", responseData.customerName);
        localStorage.setItem("BusinessName", responseData.businessName);

        window.dispatchEvent(
          new CustomEvent("businessNameLoaded", {
            detail: { businessName: responseData.businessName },
          })
        );

        const existingUserId = localStorage.getItem("userID");

        if (existingUserId) {
          await fetchExistingMessages(existingUserId);
        } else {
          await initializeChatSession();
        }
      } else {
        await initializeChatSession();
      }
    } catch (error) {
      console.error("Error fetching customer data:", error);
      setErrorState("Failed to load customer settings");

      // Fallback metadata
      updateMetadata({
        customerName: customerName,
        businessName: "Driply",
      });

      localStorage.setItem("BusinessName", "Driply");
      window.dispatchEvent(
        new CustomEvent("businessNameLoaded", {
          detail: { businessName: "Driply" },
        })
      );
      await initializeChatSession();
    } finally {
      setLoadingState(false);
    }
  };

  const fetchExistingMessages = async (userId) => {
    // console.log("fetchExistingMessages");

    try {
      const response = await axios.get(
        `${API_ENDPOINTS.CHAT_MESSAGES}?userId=${userId}`
      );

      if (response.data && response.data.length > 0) {
        // Load existing messages into the message pairing system
        response.data.forEach((msg) => {
          messagePairing.addMessage({
            text: msg.message,
            timestamp: new Date(msg.createdAt).getTime(),
            type: msg.type,
            source: msg.source,
          });
        });
      }

      connectToSSE(userId);
    } catch (error) {
      await initializeChatSession();
    }
  };

  const initializeChatSession = async () => {
    try {
      const response = await axios.post(API_ENDPOINTS.CHAT_INIT, {
        customerName: username,
      });

      if (response.data && response.data.userId) {
        localStorage.setItem("userId", response.data.userId);
        localStorage.setItem("userID", response.data._id);

        await fetchExistingMessages(response.data._id);
      }
    } catch (error) {
      if (!localStorage.getItem("BusinessName")) {
        localStorage.setItem("BusinessName", "Driply");
        window.dispatchEvent(
          new CustomEvent("businessNameLoaded", {
            detail: { businessName: "Driply" },
          })
        );
      }
    }
  };

  const connectToSSE = (userId) => {
    try {
      if (sseConnection) {
        sseConnection.close();
      }

      const sseUrl = getSSEUrl(userId);
      const eventSource = new EventSource(sseUrl);
      setSseConnection(eventSource);

      eventSource.addEventListener("message", ({ data }) => {
        try {
          const contents = JSON.parse(data);
          handleSSEMessage(contents);
        } catch (error) {
          // Handle parsing error silently
        }
      });

      eventSource.addEventListener("error", () => {
        // Handle connection error silently
      });
    } catch (error) {
      // Handle SSE connection error silently
    }
  };

  const handleSSEMessage = (data) => {
    const subtype = data.subType || data.subtype || data.type || "UNKNOWN";
    const content = data.content || "";
    const message = data.message || data.text || "";

    if (["TYPING", "THINKING", "BEHAVIOUR_MESSAGE"].includes(subtype)) {
      switch (subtype) {
        case "TYPING":
          setIsBotTyping(true);
          setIsBotThinking(false);
          break;
        case "THINKING":
          setIsBotThinking(true);
          setIsBotTyping(false);
          break;
        case "BEHAVIOUR_MESSAGE":
          setIsBotTyping(false);
          setIsBotThinking(false);
          break;
      }
      return;
    }

    const displayText = message || content;
    if (!displayText) return;

    const displayType = ["TEXT", "MESSAGE", "DATA_MESSAGE"].includes(subtype)
      ? subtype
      : "UNKNOWN";

    // Enhanced message pairing logic
    const responseToId =
      data.queryId || data.responseToId || data.correlationId;
    let queryId = responseToId;

    // If no explicit queryId, find the most recent pending query
    if (!queryId) {
      const pendingEntries = Array.from(messagePairing.pendingQueries.entries()).sort(
        (a, b) => b[1].timestamp - a[1].timestamp
      );

      if (pendingEntries.length > 0) {
        queryId = pendingEntries[0][0];
      }
    }

    // Create bot message with proper pairing information
    const botMessage = messagePairing.addBotMessage(displayText, queryId, false);

    // The messagePairing hook handles pending query cleanup automatically

    setIsBotTyping(false);
    setIsBotThinking(false);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (message.trim()) {
      const userMessage = message.trim();
      const isFirstMsg = messages.length === 0;

      // Generate unique ID for this query
      const queryId = messagePairing.createMessageId();
      const userMessageObj = messagePairing.addUserMessage(userMessage, queryId);

      // The messagePairing hook handles message state automatically

      // Track this query as pending response
      setPendingQueries((prev) =>
        new Map(prev).set(queryId, {
          id: queryId,
          message: userMessage,
          timestamp: userMessageObj.timestamp,
          sentAt: Date.now(),
        })
      );

      setMessage("");
      setIsTyping(false);

      // For first message, set flag to scroll to top
      if (isFirstMsg) {
        setShouldScrollToTop(true);
        setIsFirstMessage(false);
      }

      // Show loading dots immediately after sending message
      setIsBotThinking(true);
      setIsBotTyping(false);

      // Reset textarea
      if (inputRef.current) {
        inputRef.current.style.height = "104px";
        inputRef.current.scrollTop = 0;
        inputRef.current.style.overflowY = isIOS() ? "hidden" : "auto";
        inputRef.current.classList.add("reset-height");
      }

      try {
        const userId = localStorage.getItem("userID");
        if (userId) {
          await axios.post(API_ENDPOINTS.CHAT_SEND, {
            userId: userId,
            message: userMessage,
            queryId: queryId, // Include queryId for response pairing
            typs: "TEXT",
            source: "USER",
            isTest: query.isTest === "1" ? true : false,
          });
        }
      } catch (error) {
        // Hide loading dots on error
        setIsBotThinking(false);
        setIsBotTyping(false);

        const errorMessage = messagePairing.addBotMessage(
          "Sorry, there was an error sending your message. Please try again.",
          queryId,
          true // isError = true
        );

        // The messagePairing hook handles message state automatically

        // Remove from pending queries since we got an error
        setPendingQueries((prev) => {
          const updated = new Map(prev);
          updated.delete(queryId);
          return updated;
        });
      }
    }
  };

  // Input handling is now managed by InputSection component

  useEffect(() => {
    if (messagePairing.messages.length > 0 && inputRef.current && !message) {
      const timer = setTimeout(() => {
        if (inputRef.current) {
          inputRef.current.style.height = "104px";
          inputRef.current.style.overflowY = isIOS() ? "hidden" : "auto";
          inputRef.current.classList.add("reset-height");
        }
      }, 50);
      return () => clearTimeout(timer);
    }
  }, [messagePairing.messages, message]);

  useEffect(() => {
    if (messagePairing.messages.length > 0) {
      handleChatGPTScroll();
    }
  }, [messagePairing.messages, isBotTyping, isBotThinking]);

  const handleChatGPTScroll = () => {
    setTimeout(() => {
      // Determine screen type and get appropriate container
      const screenWidth = window.innerWidth;
      const isDesktopLayout = screenWidth >= 1024;
      const isTabletLayout = screenWidth >= 768 && screenWidth < 1024;

      let container;
      if (isDesktopLayout) {
        container = desktopMessagesContainerRef.current;
      } else if (isTabletLayout) {
        container = tabletMessagesContainerRef.current;
      } else {
        container = mobileMessagesContainerRef.current;
      }

      if (!container) return;

      if (shouldScrollToTop) {
        // For first message, scroll to top to show the message there
        container.scrollTo({ top: 0, behavior: "smooth" });
        setShouldScrollToTop(false);
      } else {
        // Different scrolling behavior for each screen type
        const messageElements = container.querySelectorAll(".message-pair");
        if (messageElements.length > 0) {
          const lastMessagePair = messageElements[messageElements.length - 1];
          const containerHeight = container.clientHeight;
          const pairHeight = lastMessagePair.offsetHeight;
          const pairTop = lastMessagePair.offsetTop;

          if (isTabletLayout) {
            // Tablet-specific scrolling: Keep messages visible by scrolling to bottom of container
            // This ensures both user message and AI response remain visible
            const scrollPosition = Math.max(
              0,
              pairTop + pairHeight - containerHeight + 100
            );
            container.scrollTo({
              top: scrollPosition,
              behavior: "smooth",
            });
          } else {
            // Desktop and mobile: Center the message pair on screen
            const scrollPosition = pairTop - (containerHeight - pairHeight) / 2;
            container.scrollTo({
              top: Math.max(0, scrollPosition),
              behavior: "smooth",
            });
          }
        }
      }
    }, 150);
  };

  const renderMessagePairs = () => {
    // Use the enhanced message pairing logic from custom hook
    const messagePairs = messagePairing.getMessagePairs();

    return (
      <MessagePairs
        messagePairs={messagePairs}
        isBotTyping={isBotTyping}
        isBotThinking={isBotThinking}
        isMobile={isMobile}
      />
    );
  };

  const handleKeyPress = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleSuggestionClick = async (cardTitle) => {
    const isFirstMsg = messagePairing.messages.length === 0;

    // Generate unique ID for this suggestion query
    const queryId = messagePairing.createMessageId();
    const userMessageObj = messagePairing.addUserMessage(cardTitle, queryId);
    // Mark as suggestion
    userMessageObj.isSuggestion = true;

    // The messagePairing hook handles message state and pending queries automatically
    setMessage("");
    setIsTyping(false);

    // For first message, set flag to scroll to top
    if (isFirstMsg) {
      setShouldScrollToTop(true);
      setIsFirstMessage(false);
    }

    // Show loading dots immediately after sending message
    setIsBotThinking(true);
    setIsBotTyping(false);

    if (inputRef.current) {
      inputRef.current.style.height = "104px";
      inputRef.current.scrollTop = 0;
      inputRef.current.style.overflowY = isIOS() ? "hidden" : "auto";
      inputRef.current.classList.add("reset-height");
    }

    try {
      const userId = localStorage.getItem("userID");
      if (userId) {
        await axios.post(API_ENDPOINTS.CHAT_SEND, {
          userId: userId,
          message: cardTitle,
          queryId: queryId, // Include queryId for response pairing
          customerName: username,
          isSuggestion: true,
        });
      }
    } catch (error) {
      // Hide loading dots on error
      setIsBotThinking(false);
      setIsBotTyping(false);

      const errorMessage = messagePairing.addBotMessage(
        "Sorry, there was an error sending your message. Please try again.",
        queryId,
        true // isError = true
      );

      // The messagePairing hook handles message state and pending queries automatically
    }
  };

  const getMaxSlides = () => {
    if (isMobile) return settingData?.suggestedTopics.length;
    return Math.ceil(settingData?.suggestedTopics.length / 2);
  };



  useEffect(() => {
    if (messagePairing.messages.length === 0) {
      setShowInitialUI(false);
      const timer = setTimeout(() => setShowInitialUI(true), 60);
      return () => clearTimeout(timer);
    } else {
      setShowInitialUI(false);
    }
  }, [messagePairing.messages.length]);

  // Screen size detection is now handled by useDeviceDetection hook

  useEffect(() => {
    if (inputRef.current && messagePairing.messages.length === 0) {
      const shouldAutoFocus = window.innerWidth >= 768;
      if (shouldAutoFocus) {
        const timer = setTimeout(() => {
          inputRef.current?.focus();
        }, 100);
        return () => clearTimeout(timer);
      }
    }
  }, [messagePairing.messages.length]);

  useEffect(() => {
    return () => {
      if (sseConnection) {
        sseConnection.close();
      }
    };
  }, [sseConnection]);

  // Check if settingData has been populated with actual data
  const hasSettingData =
    settingData &&
    Object.keys(settingData).some((key) => settingData[key] !== null);

  // Show loading state while waiting for data
  // if (!hasSettingData) {
  //   return (
  //     <div className="bg-white flex flex-col min-h-screen items-center justify-center">
  //       <div className="text-center">
  //         <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
  //         <p className="text-gray-600">Loading chat settings...</p>
  //         {contextError && (
  //           <p className="text-red-500 mt-2">Error: {contextError}</p>
  //         )}
  //       </div>
  //     </div>
  //   );
  // }

  return (
    <div className="bg-white flex flex-col">
      {/* Desktop Layout (≥1024px) */}
      <div className="hidden lg:flex flex-1 flex-col px-4 ">
        {messagePairing.messages.length === 0 ? (
          <div className="flex flex-1 items-center justify-center min-h-[calc(100vh-64px)] mt-20">
            <div className="flex flex-col items-center w-[768px] justify-center mx-auto ">
              <h1
                className={`text-4xl text-gray-900 mb-6 text-center transition-opacity duration-500 ${
                  showInitialUI ? "opacity-100" : "opacity-0"
                }`}
                style={{ transitionDelay: showInitialUI ? "40ms" : "0ms" }}
              >
                How can I help you?
              </h1>

              <div
                className={`relative w-full mb-6 flex flex-col items-center transition-all duration-500 ${
                  showInitialUI
                    ? "opacity-100 "
                    : "opacity-0"
                }`}
                style={{ transitionDelay: showInitialUI ? "80ms" : "0ms" }}
              >
                <InputSection
                  message={message}
                  onMessageChange={setMessage}
                  onSubmit={handleSubmit}
                  isDisabled={false}
                  isMobile={isMobile}
                  placeholder="Ask anything"
                  showPoweredBy={false}
                />
              </div>

              <div
                className={`relative w-full max-w-2xl transition-all duration-200 ease-in-out ${
                  isTyping
                    ? "opacity-100 pointer-events-none" // TC1
                    : showInitialUI
                    ? "opacity-100 "
                    : "opacity-0"
                }`}
                style={{
                  transitionDelay: showInitialUI ? "40ms" : "0ms",
                  zIndex: 10,
                }}
              >
                <CarouselSection
                  settingData={settingData}
                  currentSlide={carousel.currentSlide}
                  onCardClick={handleSuggestionClick}
                  carouselRef={carouselRef}
                  onTouchStart={carousel.handleTouchStart}
                  onTouchMove={carousel.handleTouchMove}
                  onTouchEnd={carousel.handleTouchEnd}
                  onPrevSlide={carousel.prevSlide}
                  onNextSlide={carousel.nextSlide}
                  isAtStart={carousel.isAtStart}
                  isAtEnd={carousel.isAtEnd}
                  isMobile={isMobile}
                />
              </div>
            </div>
          </div>
        ) : (
          <>
            <div
              ref={desktopMessagesContainerRef}
              className="flex-1 overflow-y-auto max-h-[calc(100vh-200px)] mt-14"
              style={{ overscrollBehavior: "contain" }}
            >
              <div className="w-full lg:w-[768px] mx-auto px-4 h-fit ">
                {renderMessagePairs()}
                <div ref={messagesEndRef} />
              </div>
            </div>

            <div
              className="fixed bottom-0 left-0 right-0 p-4 mt-5"
              style={{ zIndex: 1000, marginTop: "20px" }}
            >
              <InputSection
                message={message}
                onMessageChange={setMessage}
                onSubmit={handleSubmit}
                isDisabled={false}
                isMobile={isMobile}
                placeholder="Ask anything"
                showPoweredBy={true}
              />
            </div>
          </>
        )}
      </div>

      {/* Mobile/Tablet Layout (<1024px) */}
      <div className="lg:hidden overflow-hidden fixed inset-0 flex flex-col  mt-10 ">
        {messagePairing.messages.length === 0 ? (
          <>
            <div className="flex flex-col items-center pt-15 pb-4 px-4">
              <div className="hidden md:flex lg:hidden flex-col items-center"></div>
            </div>
            <div
              className="flex flex-col items-center justify-center flex-1 px-4"
              style={{ paddingBottom: "260px" }}
            >
              <h1
                className={`text-2xl md:text-4xl text-gray-900 text-center leading-relaxed transition-all duration-700 ${
                  showInitialUI
                    ? "opacity-100 "
                    : "opacity-0 "
                }`}
                style={{ transitionDelay: showInitialUI ? "40ms" : "0ms" }}
              >
                How can I help you?
              </h1>
            </div>
          </>
        ) : (
          <>
            {/* Mobile Messages Container (< 768px) */}
            <div
              ref={mobileMessagesContainerRef}
              className="md:hidden flex-1 overflow-y-auto max-h-[calc(100vh-190px)] hide-scrollbar"
              style={{ overscrollBehavior: "contain" }}
            >
              <div className="w-full max-w-[803px] mx-auto px-4  pt-12">
                {renderMessagePairs()}
                <div ref={messagesEndRef} />
              </div>
            </div>

            {/* Tablet Messages Container (768px - 1023px) */}
            <div
              ref={tabletMessagesContainerRef}
              className="hidden md:block lg:hidden flex-1 overflow-y-auto max-h-[calc(100vh-100px)] pb-[140px] hide-scrollbar"
              style={{ overscrollBehavior: "contain" }}
            >
              <div className="w-full max-w-[803px] mx-auto px-4 pb-6 pt-12">
                {renderMessagePairs()}
                <div ref={messagesEndRef} />
              </div>
            </div>

            <div
              className="fixed bottom-0 left-0 right-0 bg-white"
              style={{
                minHeight: "160px",
                zIndex: 1000,
                paddingBottom: "env(safe-area-inset-bottom, 0)",
                transform: "translateZ(0)",
                backfaceVisibility: "hidden",
              }}
            >
              <InputSection
                message={message}
                onMessageChange={setMessage}
                onSubmit={handleSubmit}
                isDisabled={false}
                isMobile={isMobile}
                placeholder="Ask anything"
                showPoweredBy={true}
              />
            </div>
          </>
        )}

        {/* Mobile/Tablet Bottom Section */}
        <div className="absolute bottom-0 left-0 right-0 bg-white">
          {messagePairing.messages.length === 0 && (
            <>
              {/* Suggestion Cards Section */}
              <div
                className={`${
                  isMobile ? "px-0" : "px-4"
                } pt-2 pb-2 transition-all duration-500 ease-in-out ${
                  isTyping
                    ? "opacity-0 pointer-events-none"
                    : showInitialUI
                    ? "opacity-100"
                    : "opacity-0"
                }`}
                style={{
                  transitionDelay: showInitialUI ? "120ms" : "0ms",
                  position: "relative",
                  zIndex: 10,
                  backgroundColor: "white",
                  paddingBottom: "10px",
                }}
              >
                <div className="overflow-hidden px-4">
                  {/* Show loading spinner when card data is not available */}
                  {!settingData?.suggestedTopics || settingData.suggestedTopics.length === 0 ? (
                    <div className="flex justify-center items-center py-8">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                    </div>
                  ) : (
                    <div
                      ref={carouselRef}
                      className="flex gap-4 transition-transform duration-300 ease-in-out"
                      style={{
                        transform: `translateX(-${carousel.currentSlide * 100}%)`,
                      }}
                      onTouchStart={carousel.handleTouchStart}
                      onTouchMove={carousel.handleTouchMove}
                      onTouchEnd={carousel.handleTouchEnd}
                    >
                      {settingData.suggestedTopics.map((card, cardIndex) => (
                      <button
                        key={cardIndex}
                        onClick={() => handleSuggestionClick(card.question)}
                        className={`${
                          isMobile ? "py-3 mt-3" : "py-3"
                        } px-4 rounded-[8px] bg-[#F6F6F6] text-left hover:bg-[#D6EDFF] transition-all duration-200 flex-shrink-0 touch-manipulation`}
                        style={{
                          // Calculate width accounting for gap (gap-4 = 16px)
                          width: "calc(100% - 16px)",
                          minHeight: isMobile ? "90px" : "80px", // Minimum height to accommodate wrapped text
                        }}
                      >
                        <div
                          className={`${
                            isMobile ? "text-[16px]" : "text-[16px]"
                          } font-[600] text-black mb-1 leading-snug break-words word-wrap overflow-wrap-anywhere`}
                        >
                          {card.question}
                        </div>
                        <div
                          className={`${
                            isMobile ? "text-[14px]" : "text-[14px]"
                          } text-gray-500 leading-snug break-words word-wrap overflow-wrap-anywhere`}
                        >
                          {card.subtitle}
                        </div>
                        </button>
                      ))}
                    </div>
                  )}
                </div>

                {/* Show navigation buttons only when data is loaded */}
                {settingData?.suggestedTopics && settingData.suggestedTopics.length > 0 && (
                  <>
                    <button
                      onClick={handlePrevSlide}
                      disabled={isAtStart}
                      className={`hidden md:flex absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-10 w-8 h-8 border rounded-full items-center justify-center shadow-md transition-all duration-200 touch-manipulation z-10 ${
                        isAtStart
                          ? "bg-gray-100 border-gray-300 cursor-not-allowed"
                          : "bg-white border-gray-200 hover:bg-gray-50 cursor-pointer"
                      }`}
                    >
                      <FaChevronLeft
                        className={`w-3 h-3 ${
                          isAtStart ? "text-gray-400" : "text-gray-600"
                        }`}
                      />
                    </button>
                    <button
                      onClick={handleNextSlide}
                      disabled={isAtEnd()}
                      className={`hidden md:flex absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-10 w-8 h-8 border rounded-full items-center justify-center shadow-md transition-all duration-200 touch-manipulation z-10 ${
                        isAtEnd()
                          ? "bg-gray-100 border-gray-300 cursor-not-allowed"
                          : "bg-white border-gray-200 hover:bg-gray-50 cursor-pointer"
                      }`}
                    >
                      <FaChevronRight
                        className={`w-3 h-3 ${
                          isAtEnd() ? "text-gray-400" : "text-gray-600"
                        }`}
                      />
                    </button>
                  </>
                )}
              </div>

              {/* Input Section */}
              <div
                className={`px-4 bg-white transition-all duration-500 ${
                  showInitialUI ? "opacity-100 translate-y-0" : "translate-y-4"
                }`}
                style={{
                  transitionDelay: showInitialUI ? "200ms" : "0ms",
                  position: "relative",
                  zIndex: 5,
                  paddingBottom: "env(safe-area-inset-bottom)",
                }}
              >
                <InputSection
                  message={message}
                  onMessageChange={setMessage}
                  onSubmit={handleSubmit}
                  isDisabled={false}
                  isMobile={isMobile}
                  placeholder="Ask anything"
                  showPoweredBy={true}
                />
              </div>
            </>
          )}
        </div>
      </div>

      {/* All styles are now in ChatInterface.module.css */}
    </div>
  );
};

export default ChatInterface;
