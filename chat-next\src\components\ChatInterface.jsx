"use client";
import React, { useState, useEffect, useRef } from "react";
import { SUGGESTION_CARDS } from "../utils/constants";
import {
  getSSEUrl,
  API_ENDPOINTS,
  EXTERNAL_API_ENDPOINTS,
} from "../utils/config";
import { FaArrowUp, FaChevronLeft, FaChevronRight } from "react-icons/fa6";
import axios from "axios";
import { useChatContext } from "../contexts/ChatContext";

const ChatInterface = ({ slug, query }) => {
  // Context API
  const {
    metadata,
    updateMetadata,
    setLoadingState,
    setErrorState,
    clearError,
    settingData,
    updateSettingData,
    getCustomerName,
    getBusinessName,
    hasMetadata,
    loading: contextLoading,
    error: contextError,
  } = useChatContext();

  const [message, setMessage] = useState("");
  const [messages, setMessages] = useState([]);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [touchStart, setTouchStart] = useState(0);
  const [touchEnd, setTouchEnd] = useState(0);
  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const [showInitialUI, setShowInitialUI] = useState(false);
  const [isBotTyping, setIsBotTyping] = useState(false);
  const [isBotThinking, setIsBotThinking] = useState(false);
  const [sseConnection, setSseConnection] = useState(null);
  const [isFirstMessage, setIsFirstMessage] = useState(true);
  const [shouldScrollToTop, setShouldScrollToTop] = useState(false);

  // Enhanced message pairing state
  const [pendingQueries, setPendingQueries] = useState(new Map()); // Track queries waiting for responses
  const [messageSequence, setMessageSequence] = useState(0); // Global sequence counter
  const messagesEndRef = useRef(null);
  const carouselRef = useRef(null);
  const inputRef = useRef(null);
  const desktopMessagesContainerRef = useRef(null);
  const mobileMessagesContainerRef = useRef(null);
  const tabletMessagesContainerRef = useRef(null);

  // Debug: Log state updates when they actually happen
  useEffect(() => {
    if (
      metadata &&
      Object.keys(metadata).some((key) => metadata[key] !== null)
    ) {
      console.log("✅ METADATA STATE UPDATED:", metadata);
    }
  }, [metadata]);

  useEffect(() => {
    if (
      settingData &&
      Object.keys(settingData).some((key) => settingData[key] !== null)
    ) {
      console.log("✅ SETTING DATA STATE UPDATED:", settingData);
    }
  }, [settingData]);

  // Cleanup pending queries that are too old (timeout mechanism)
  useEffect(() => {
    const cleanup = setInterval(() => {
      const now = Date.now();
      const TIMEOUT_MS = 60000; // 60 seconds timeout

      setPendingQueries(prev => {
        const updated = new Map(prev);
        let hasChanges = false;

        for (const [queryId, queryData] of prev.entries()) {
          if (now - queryData.sentAt > TIMEOUT_MS) {

            updated.delete(queryId);
            hasChanges = true;
          }
        }

        return hasChanges ? updated : prev;
      });
    }, 10000); // Check every 10 seconds

    return () => clearInterval(cleanup);
  }, []);

  // Handle infinite scroll reset for smooth circular behavior
  useEffect(() => {
    const totalCards = settingData?.suggestedTopics?.length || 0;
    if (totalCards === 0) return;

    // Reset position when we've scrolled through one complete cycle
    // This creates seamless infinite scrolling
    const resetTimeout = setTimeout(() => {
      if (currentSlide >= totalCards * 2) {
        // Reset to middle section without animation
        const carousel = carouselRef.current;
        if (carousel) {
          carousel.style.transition = 'none';
          setCurrentSlide(totalCards);

          // Re-enable transition after reset
          setTimeout(() => {
            carousel.style.transition = 'transform 300ms ease-in-out';
          }, 50);
        }
      } else if (currentSlide < 0) {
        // Reset from negative to positive section
        const carousel = carouselRef.current;
        if (carousel) {
          carousel.style.transition = 'none';
          setCurrentSlide(totalCards - 1);

          setTimeout(() => {
            carousel.style.transition = 'transform 300ms ease-in-out';
          }, 50);
        }
      }
    }, 350); // Wait for transition to complete

    return () => clearTimeout(resetTimeout);
  }, [currentSlide, settingData?.suggestedTopics?.length]);

  // Initialize carousel to start from middle section for better circular behavior
  useEffect(() => {
    const totalCards = settingData?.suggestedTopics?.length || 0;
    if (totalCards > 0 && currentSlide === 0) {
      // Start from the middle section (second copy of cards)
      setCurrentSlide(totalCards);
    }
  }, [settingData?.suggestedTopics?.length]);

  // Utility functions for message pairing
  const generateMessageId = () => {
    const sequence = messageSequence + 1;
    setMessageSequence(sequence);
    return `msg_${Date.now()}_${sequence}_${Math.random().toString(36).substr(2, 9)}`;
  };

  const createMessagePairs = (messages) => {
    const pairs = [];
    const processedMessages = new Set();

    // Sort messages by timestamp to handle out-of-order arrivals
    const sortedMessages = [...messages].sort((a, b) => a.timestamp - b.timestamp);

    // Group messages by queryId for proper pairing
    const messageGroups = new Map();

    sortedMessages.forEach(msg => {
      if (msg.source === "USER") {
        // User message starts a new conversation pair
        const queryId = msg.queryId || msg.id || `fallback_${msg.timestamp}`;
        if (!messageGroups.has(queryId)) {
          messageGroups.set(queryId, { user: null, bot: null, timestamp: msg.timestamp });
        }
        messageGroups.get(queryId).user = msg;
      } else if (msg.source === "BOT") {
        // Bot message should be paired with corresponding user message
        const queryId = msg.queryId || msg.responseToId;
        if (queryId && messageGroups.has(queryId)) {
          messageGroups.get(queryId).bot = msg;
        } else {
          // Fallback: pair with most recent unpaired user message
          const unpairedGroups = Array.from(messageGroups.entries())
            .filter(([_, group]) => group.user && !group.bot)
            .sort((a, b) => b[1].timestamp - a[1].timestamp);

          if (unpairedGroups.length > 0) {
            const [groupId, group] = unpairedGroups[0];
            group.bot = msg;
            // Update the message with proper queryId for future reference
            msg.queryId = groupId;
          }
        }
      }
    });

    // Convert groups to pairs array, sorted by timestamp
    const sortedGroups = Array.from(messageGroups.entries())
      .sort((a, b) => a[1].timestamp - b[1].timestamp);

    return sortedGroups.map(([queryId, group]) => ({
      id: queryId,
      user: group.user,
      bot: group.bot,
      timestamp: group.timestamp,
      isComplete: !!(group.user && group.bot),
      isPending: !!(group.user && !group.bot)
    }));
  };

  // Detect iOS for targeted fixes
  const isIOS = () => {
    if (typeof window !== "undefined") {
      return /iPhone|iPad|iPod/i.test(navigator.userAgent);
    }
    return false;
  };

  const username = slug;

  useEffect(() => {
    if (username && typeof window !== "undefined") {
      fetchCustomer(username);
    }
  }, [username]);

  const fetchCustomer = async (customerName) => {
    try {
      setLoadingState(true);

      clearError();

      const response = await axios.get(
        `${API_ENDPOINTS.CHAT_SETTINGS}?customerName=${customerName}`
      );

      // Extract data from axios response
      const responseData = response.data;
      const metadataFromResponse = responseData.metaData;

      updateSettingData(responseData);
      updateMetadata(metadataFromResponse);

      if (responseData && responseData.customerName) {
        //  localStorage for backward compatibility
        localStorage.setItem("customerName_userId", responseData.customerName);
        localStorage.setItem("BusinessName", responseData.businessName);

        window.dispatchEvent(
          new CustomEvent("businessNameLoaded", {
            detail: { businessName: responseData.businessName },
          })
        );

        const existingUserId = localStorage.getItem("userID");

        if (existingUserId) {
          await fetchExistingMessages(existingUserId);
        } else {
          await initializeChatSession();
        }
      } else {
        await initializeChatSession();
      }
    } catch (error) {
      console.error("Error fetching customer data:", error);
      setErrorState("Failed to load customer settings");

      // Fallback metadata
      updateMetadata({
        customerName: customerName,
        businessName: "Driply",
      });

      localStorage.setItem("BusinessName", "Driply");
      window.dispatchEvent(
        new CustomEvent("businessNameLoaded", {
          detail: { businessName: "Driply" },
        })
      );
      await initializeChatSession();
    } finally {
      setLoadingState(false);
    }
  };

  const fetchExistingMessages = async (userId) => {
    // console.log("fetchExistingMessages");

    try {
      const response = await axios.get(
        `${API_ENDPOINTS.CHAT_MESSAGES}?userId=${userId}`
      );

      if (response.data && response.data.length > 0) {
        setMessages(
          response.data.map((msg) => ({
            text: msg.message,
            timestamp: new Date(msg.createdAt).getTime(),
            type: msg.type,
            source: msg.source,
          }))
        );
      }

      connectToSSE(userId);
    } catch (error) {
      await initializeChatSession();
    }
  };

  const initializeChatSession = async () => {
    try {
      const response = await axios.post(API_ENDPOINTS.CHAT_INIT, {
        customerName: username,
      });

      if (response.data && response.data.userId) {
        localStorage.setItem("userId", response.data.userId);
        localStorage.setItem("userID", response.data._id);

        await fetchExistingMessages(response.data._id);
      }
    } catch (error) {
      if (!localStorage.getItem("BusinessName")) {
        localStorage.setItem("BusinessName", "Driply");
        window.dispatchEvent(
          new CustomEvent("businessNameLoaded", {
            detail: { businessName: "Driply" },
          })
        );
      }
    }
  };

  const connectToSSE = (userId) => {
    try {
      if (sseConnection) {
        sseConnection.close();
      }

      const sseUrl = getSSEUrl(userId);
      const eventSource = new EventSource(sseUrl);
      setSseConnection(eventSource);

      eventSource.addEventListener("message", ({ data }) => {
        try {
          const contents = JSON.parse(data);
          handleSSEMessage(contents);
        } catch (error) {
          // Handle parsing error silently
        }
      });

      eventSource.addEventListener("error", () => {
        // Handle connection error silently
      });
    } catch (error) {
      // Handle SSE connection error silently
    }
  };

  const handleSSEMessage = (data) => {
    const subtype = data.subType || data.subtype || data.type || "UNKNOWN";
    const content = data.content || "";
    const message = data.message || data.text || "";

    if (["TYPING", "THINKING", "BEHAVIOUR_MESSAGE"].includes(subtype)) {
      switch (subtype) {
        case "TYPING":
          setIsBotTyping(true);
          setIsBotThinking(false);
          break;
        case "THINKING":
          setIsBotThinking(true);
          setIsBotTyping(false);
          break;
        case "BEHAVIOUR_MESSAGE":
          setIsBotTyping(false);
          setIsBotThinking(false);
          break;
      }
      return;
    }

    const displayText = message || content;
    if (!displayText) return;

    const displayType = ["TEXT", "MESSAGE", "DATA_MESSAGE"].includes(subtype)
      ? subtype
      : "UNKNOWN";

    // Enhanced message pairing logic
    const responseToId = data.queryId || data.responseToId || data.correlationId;
    let queryId = responseToId;

    // If no explicit queryId, find the most recent pending query
    if (!queryId) {
      const pendingEntries = Array.from(pendingQueries.entries())
        .sort((a, b) => b[1].timestamp - a[1].timestamp);

      if (pendingEntries.length > 0) {
        queryId = pendingEntries[0][0];
      }
    }

    // Create bot message with proper pairing information
    const botMessage = {
      id: generateMessageId(),
      text: displayText,
      timestamp: Date.now(),
      type: displayType,
      source: "BOT",
      queryId: queryId, // Link to corresponding user query
      responseToId: queryId // Explicit response relationship
    };



    setMessages((prev) => [
      ...prev,
      botMessage
    ]);

    // Remove from pending queries if we found a match
    if (queryId && pendingQueries.has(queryId)) {
      setPendingQueries(prev => {
        const updated = new Map(prev);
        updated.delete(queryId);
        return updated;
      });
    }

    setIsBotTyping(false);
    setIsBotThinking(false);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (message.trim()) {
      const userMessage = message.trim();
      const isFirstMsg = messages.length === 0;

      // Generate unique ID for this query
      const queryId = generateMessageId();
      const userMessageObj = {
        id: queryId,
        queryId: queryId, // Self-reference for user messages
        text: userMessage,
        timestamp: Date.now(),
        type: "TEXT",
        source: "USER",
      };



      setMessages((prev) => [
        ...prev,
        userMessageObj
      ]);

      // Track this query as pending response
      setPendingQueries(prev => new Map(prev).set(queryId, {
        id: queryId,
        message: userMessage,
        timestamp: userMessageObj.timestamp,
        sentAt: Date.now()
      }));

      setMessage("");
      setIsTyping(false);

      // For first message, set flag to scroll to top
      if (isFirstMsg) {
        setShouldScrollToTop(true);
        setIsFirstMessage(false);
      }

      // Show loading dots immediately after sending message
      setIsBotThinking(true);
      setIsBotTyping(false);

      // Reset textarea
      if (inputRef.current) {
        inputRef.current.style.height = "104px";
        inputRef.current.scrollTop = 0;
        inputRef.current.style.overflowY = isIOS() ? "hidden" : "auto";
        inputRef.current.classList.add("reset-height");
      }

      try {
        const userId = localStorage.getItem("userID");
        if (userId) {
          await axios.post(API_ENDPOINTS.CHAT_SEND, {
            userId: userId,
            message: userMessage,
            queryId: queryId, // Include queryId for response pairing
            typs: "TEXT",
            source: "USER",
            isTest: query.isTest === "1" ? true : false,
          });
        }
      } catch (error) {
        // Hide loading dots on error
        setIsBotThinking(false);
        setIsBotTyping(false);

        const errorMessage = {
          id: generateMessageId(),
          text: "Sorry, there was an error sending your message. Please try again.",
          timestamp: Date.now(),
          type: "TEXT",
          source: "BOT",
          queryId: queryId, // Link error message to the failed query
          isError: true
        };

        setMessages((prev) => [
          ...prev,
          errorMessage
        ]);

        // Remove from pending queries since we got an error
        setPendingQueries(prev => {
          const updated = new Map(prev);
          updated.delete(queryId);
          return updated;
        });
      }
    }
  };

  const handleTextareaResize = (textarea) => {
    if (textarea) {
      textarea.style.height = "104px";
      const scrollHeight = textarea.scrollHeight;
      if (isIOS()) {
        if (scrollHeight > 48) {
          textarea.style.overflowY = "auto";
        } else {
          textarea.style.overflowY = "hidden";
        }
      } else {
        const newHeight = Math.min(250, Math.max(104, scrollHeight));
        textarea.style.height = `${newHeight}px`;
        textarea.style.overflowY = scrollHeight > newHeight ? "auto" : "hidden";
      }
    }
  };

  const handleInputChange = (e) => {
    const value = e.target.value;
    setMessage(value);
    handleTextareaResize(e.target);
    if (messages.length === 0) {
      setIsTyping(value.length > 0);
    }
  };

  useEffect(() => {
    if (messages.length > 0 && inputRef.current && !message) {
      const timer = setTimeout(() => {
        if (inputRef.current) {
          inputRef.current.style.height = "104px";
          inputRef.current.style.overflowY = isIOS() ? "hidden" : "auto";
          inputRef.current.classList.add("reset-height");
        }
      }, 50);
      return () => clearTimeout(timer);
    }
  }, [messages, message]);

  useEffect(() => {
    if (messages.length > 0) {
      handleChatGPTScroll();
    }
  }, [messages, isBotTyping, isBotThinking]);

  const handleChatGPTScroll = () => {
    setTimeout(() => {
      // Determine screen type and get appropriate container
      const screenWidth = window.innerWidth;
      const isDesktopLayout = screenWidth >= 1024;
      const isTabletLayout = screenWidth >= 768 && screenWidth < 1024;

      let container;
      if (isDesktopLayout) {
        container = desktopMessagesContainerRef.current;
      } else if (isTabletLayout) {
        container = tabletMessagesContainerRef.current;
      } else {
        container = mobileMessagesContainerRef.current;
      }

      if (!container) return;

      if (shouldScrollToTop) {
        // For first message, scroll to top to show the message there
        container.scrollTo({ top: 0, behavior: "smooth" });
        setShouldScrollToTop(false);
      } else {
        // Different scrolling behavior for each screen type
        const messageElements = container.querySelectorAll(".message-pair");
        if (messageElements.length > 0) {
          const lastMessagePair = messageElements[messageElements.length - 1];
          const containerHeight = container.clientHeight;
          const pairHeight = lastMessagePair.offsetHeight;
          const pairTop = lastMessagePair.offsetTop;

          if (isTabletLayout) {
            // Tablet-specific scrolling: Keep messages visible by scrolling to bottom of container
            // This ensures both user message and AI response remain visible
            const scrollPosition = Math.max(
              0,
              pairTop + pairHeight - containerHeight + 100
            );
            container.scrollTo({
              top: scrollPosition,
              behavior: "smooth",
            });
          } else {
            // Desktop and mobile: Center the message pair on screen
            const scrollPosition = pairTop - (containerHeight - pairHeight) / 2;
            container.scrollTo({
              top: Math.max(0, scrollPosition),
              behavior: "smooth",
            });
          }
        }
      }
    }, 150);
  };

  const renderMessagePairs = () => {
    // Use the enhanced message pairing logic
    const messagePairs = createMessagePairs(messages);



    return messagePairs.map((pair, i) => (
      <div
        key={`pair-${pair.id}`}
        className="message-pair flex flex-col justify-start"
        data-pair-id={pair.id}
        data-is-complete={pair.isComplete}
        data-is-pending={pair.isPending}
        style={{
          minHeight:
            i === messagePairs.length - 1
              ? isMobile
                ? "calc(100vh - 200px)" // Mobile-specific height for newest message
                : "calc(100vh - 200px)" // Desktop/tablet height for newest message
              : "", // No min height for older messages on ALL devices
          paddingTop: i === 0 ? "1rem" : "1rem",
          paddingBottom: "0rem",
        }}
      >
        {/* User Message */}
        {pair.user && (
          <div className="flex justify-end mb-8">
            <div
              className={`text-[16px] font-[400] rounded-3xl max-w-xs lg:max-w-lg break-words hyphens-auto bg-gray-100 ${
                isMobile ? "rounded-[15px] px-3 py-2" : "px-4 py-3"
              }`}
              data-message-id={pair.user.id}
              data-query-id={pair.user.queryId}
              style={{
                overflowWrap: "break-word",
                wordBreak: "break-word",
                maxWidth: isMobile ? "90%" : undefined,
              }}
            >
              {pair.user.text}
              {pair.user.isSuggestion && (
                <span className="text-xs text-gray-500 ml-2">💡</span>
              )}
            </div>
          </div>
        )}

        {/* Bot Message */}
        {pair.bot && (
          <div className="flex justify-start">
            <div
              className={`text-[16px] font-[400] rounded-3xl max-w-xs lg:max-w-lg break-words hyphens-auto bg-white ${
                isMobile ? "rounded-[15px] px-3 py-2" : "px-4 py-3"
              } ${pair.bot.isError ? "border-red-200 bg-red-50" : ""}`}
              data-message-id={pair.bot.id}
              data-query-id={pair.bot.queryId}
              data-response-to={pair.bot.responseToId}
              style={{
                overflowWrap: "break-word",
                wordBreak: "break-word",
                maxWidth: isMobile ? "90%" : undefined,
              }}
            >
              {pair.bot.text}
              {pair.bot.isError && (
                <span className="text-xs text-red-500 ml-2">⚠️</span>
              )}
            </div>
          </div>
        )}



        {/* Global Loading Indicator for Current Typing/Thinking */}
        {i === messagePairs.length - 1 && (isBotTyping || isBotThinking) && !pair.bot && (
          <div className="flex justify-start">
            <div
              className={`bg-white ${
                isMobile
                  ? "px-3 py-2 rounded-[15px]"
                  : "px-4 py-3 rounded-3xl"
              }`}
            >
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-black rounded-full loading-dot"></div>
                <div className="w-2 h-2 bg-black rounded-full loading-dot"></div>
                <div className="w-2 h-2 bg-black rounded-full loading-dot"></div>
              </div>
            </div>
          </div>
        )}
      </div>
    ));
  };



  const handleKeyPress = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleSuggestionClick = async (cardTitle) => {
    const isFirstMsg = messages.length === 0;

    // Generate unique ID for this suggestion query
    const queryId = generateMessageId();
    const userMessageObj = {
      id: queryId,
      queryId: queryId,
      text: cardTitle,
      timestamp: Date.now(),
      type: "TEXT",
      source: "USER",
      isSuggestion: true
    };



    setMessages((prev) => [
      ...prev,
      userMessageObj
    ]);

    // Track this suggestion query as pending response
    setPendingQueries(prev => new Map(prev).set(queryId, {
      id: queryId,
      message: cardTitle,
      timestamp: userMessageObj.timestamp,
      sentAt: Date.now(),
      isSuggestion: true
    }));
    setMessage("");
    setIsTyping(false);

    // For first message, set flag to scroll to top
    if (isFirstMsg) {
      setShouldScrollToTop(true);
      setIsFirstMessage(false);
    }

    // Show loading dots immediately after sending message
    setIsBotThinking(true);
    setIsBotTyping(false);

    if (inputRef.current) {
      inputRef.current.style.height = "104px";
      inputRef.current.scrollTop = 0;
      inputRef.current.style.overflowY = isIOS() ? "hidden" : "auto";
      inputRef.current.classList.add("reset-height");
    }

    try {
      const userId = localStorage.getItem("userID");
      if (userId) {
        await axios.post(API_ENDPOINTS.CHAT_SEND, {
          userId: userId,
          message: cardTitle,
          queryId: queryId, // Include queryId for response pairing
          customerName: username,
          isSuggestion: true
        });
      }
    } catch (error) {
      // Hide loading dots on error
      setIsBotThinking(false);
      setIsBotTyping(false);

      const errorMessage = {
        id: generateMessageId(),
        text: "Sorry, there was an error sending your message. Please try again.",
        timestamp: Date.now(),
        type: "TEXT",
        source: "BOT",
        queryId: queryId, // Link error message to the failed suggestion
        isError: true
      };

      setMessages((prev) => [
        ...prev,
        errorMessage
      ]);

      // Remove from pending queries since we got an error
      setPendingQueries(prev => {
        const updated = new Map(prev);
        updated.delete(queryId);
        return updated;
      });
    }
  };

  const getMaxSlides = () => {
    if (isMobile) return settingData?.suggestedTopics.length;
    return Math.ceil(settingData?.suggestedTopics.length / 2);
  };

  const nextSlide = () => {
    const totalCards = settingData?.suggestedTopics?.length || 0;
    if (totalCards === 0) return;

    // Circular scrolling - when reaching the end, go to beginning
    setCurrentSlide((prev) => (prev + 1) % totalCards);
  };

  const prevSlide = () => {
    const totalCards = settingData?.suggestedTopics?.length || 0;
    if (totalCards === 0) return;

    // Circular scrolling - when reaching the beginning, go to end
    setCurrentSlide((prev) => (prev - 1 + totalCards) % totalCards);
  };

  const handleNextSlide = () => {
    nextSlide();
  };

  const handlePrevSlide = () => {
    prevSlide();
  };

  const handleTouchStart = (e) => {
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;
    const swipeDistance = touchStart - touchEnd;
    const minSwipeDistance = 50;

    if (swipeDistance > minSwipeDistance) {
      nextSlide();
    }
    if (swipeDistance < -minSwipeDistance) {
      prevSlide();
    }

    setTouchStart(0);
    setTouchEnd(0);
  };

  useEffect(() => {
    if (messages.length === 0) {
      setShowInitialUI(false);
      const timer = setTimeout(() => setShowInitialUI(true), 60);
      return () => clearTimeout(timer);
    } else {
      setShowInitialUI(false);
    }
  }, [messages.length]);

  useEffect(() => {
    const checkScreenSize = () => {
      if (typeof window !== "undefined") {
        const width = window.innerWidth;
        setIsMobile(width < 768);
        setIsTablet(width >= 768 && width < 1024);
      }
    };

    checkScreenSize();

    const handleResize = () => {
      setCurrentSlide(0);
      checkScreenSize();
    };

    if (typeof window !== "undefined") {
      window.addEventListener("resize", handleResize);
      return () => window.removeEventListener("resize", handleResize);
    }
  }, []);

  useEffect(() => {
    if (inputRef.current && messages.length === 0) {
      const shouldAutoFocus = window.innerWidth >= 768;
      if (shouldAutoFocus) {
        const timer = setTimeout(() => {
          inputRef.current?.focus();
        }, 100);
        return () => clearTimeout(timer);
      }
    }
  }, [messages.length]);

  useEffect(() => {
    return () => {
      if (sseConnection) {
        sseConnection.close();
      }
    };
  }, [sseConnection]);

  // Check if settingData has been populated with actual data
  const hasSettingData =
    settingData &&
    Object.keys(settingData).some((key) => settingData[key] !== null);


  // Show loading state while waiting for data
  if (!hasSettingData) {
    return (
      <div className="bg-white flex flex-col min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading chat settings...</p>
          {contextError && (
            <p className="text-red-500 mt-2">Error: {contextError}</p>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white flex flex-col">
      {/* Desktop Layout (≥1024px) */}
      <div className="hidden lg:flex flex-1 flex-col px-4 ">
        {messages.length === 0 ? (
          <div className="flex flex-1 items-center justify-center min-h-[calc(100vh-64px)] mt-20">
            <div className="flex flex-col items-center w-[768px] justify-center mx-auto ">
              <h1
                className={`text-4xl text-gray-900 mb-6 text-center transition-all duration-700 ${
                  showInitialUI
                    ? "opacity-100 translate-y-0"
                    : "opacity-0 translate-y-4"
                }`}
                style={{ transitionDelay: showInitialUI ? "40ms" : "0ms" }}
              >
                How can I help you?
              </h1>

              <form
                onSubmit={handleSubmit}
                className={`relative w-full mb-6 flex flex-col items-center transition-all duration-500 ${
                  showInitialUI
                    ? "opacity-100 translate-y-0"
                    : "opacity-0 translate-y-4"
                }`}
                style={{ transitionDelay: showInitialUI ? "120ms" : "0ms" }}
              >
                <textarea
                  ref={messages.length === 0 ? inputRef : null}
                  value={message}
                  onChange={handleInputChange}
                  onKeyDown={handleKeyPress}
                  placeholder="Ask anything"
                  autoFocus={messages.length === 0}
                  rows={1}
                  className="pt-3 px-4 lg:p-4 pb-16 pr-12 w-full min-h-[104px] max-h-[250px] overflow-y-auto hide-scrollbar text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base shadow-md resize-none reset-height"
                  style={{
                    boxSizing: "border-box",
                    zIndex: 1001,
                    position: "relative",
                  }}
                />
                <button
                  type="submit"
                  disabled={message.trim().length === 0}
                  className={`absolute right-3 bottom-3 w-9 h-9 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation ${
                    message.trim().length === 0
                      ? "bg-[#D4D6CE] text-white cursor-not-allowed"
                      : "bg-black text-white hover:bg-gray-800"
                  }`}
                >
                  <FaArrowUp className="w-4 h-4 text-white" />
                </button>
              </form>
              <div
                className={`relative w-full max-w-2xl transition-all duration-500 ease-in-out ${
                  isTyping
                    ? "opacity-0 pointer-events-none"
                    : showInitialUI
                    ? "opacity-100 translate-y-0"
                    : "opacity-0 translate-y-4"
                }`}
                style={{
                  transitionDelay: showInitialUI ? "200ms" : "0ms",
                  zIndex: 10,
                }}
              >
                <div className="overflow-hidden px-12">
                  <div
                    ref={carouselRef}
                    className="flex gap-2 transition-transform duration-300 ease-in-out justify-start"
                    style={{
                      transform: `translateX(-${(currentSlide + (settingData?.suggestedTopics?.length || 0)) * 280}px)`,
                      paddingLeft: "0",
                    }}
                  >
                    {/* Create circular effect by duplicating cards */}
                    {(() => {
                      const cards = settingData?.suggestedTopics || [];
                      if (cards.length === 0) return null;

                      // Create circular array: [...cards, ...cards, ...cards] for smooth infinite scroll
                      const circularCards = [...cards, ...cards, ...cards];

                      return circularCards.map((card, cardIndex) => (
                        <button
                          key={`${cardIndex}-${card.question}`}
                          onClick={() =>
                            handleSuggestionClick(card?.question)
                          }
                          className="py-2 px-3 rounded-[8px] bg-[#f6f6f6] text-left group transition-all duration-200 touch-manipulation flex-shrink-0"
                          style={{
                            minWidth: "fit-content",
                            maxWidth: "300px",
                            width: "auto"
                          }}
                        >
                          <div className="text-[16px] font-[600] text-black mb-0.5 leading-tight">
                            {card.question}
                          </div>
                          <div className="text-[16px] text-gray-500 leading-tight">
                            {card.subtitle}
                          </div>
                        </button>
                      ));
                    })()}
                  </div>
                </div>
                <button
                  onClick={handlePrevSlide}
                  className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-10 w-8 h-8 bg-white border border-gray-200 rounded-full flex items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation z-10"
                >
                  <FaChevronLeft className="w-3 h-3 text-gray-600" />
                </button>
                <button
                  onClick={handleNextSlide}
                  className="absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-10 w-8 h-8 bg-white border border-gray-200 rounded-full flex items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation z-10"
                >
                  <FaChevronRight className="w-3 h-3 text-gray-600" />
                </button>
              </div>
            </div>
          </div>
        ) : (
          <>
            <div
              ref={desktopMessagesContainerRef}
              className="flex-1 overflow-y-auto max-h-[calc(100vh-200px)] mt-14"
              style={{ overscrollBehavior: "contain" }}
            >
              <div className="w-full lg:w-[768px] mx-auto px-4 h-fit ">
                {renderMessagePairs()}
                <div ref={messagesEndRef} />
              </div>
            </div>

            <div
              className="fixed bottom-0 left-0 right-0 p-4 mt-5"
              style={{ zIndex: 1000, marginTop: "20px" }}
            >
              <form
                onSubmit={handleSubmit}
                className="relative w-full lg:w-[768px] mx-auto"
              >
                <textarea
                  ref={messages.length > 0 ? inputRef : null}
                  value={message}
                  onChange={handleInputChange}
                  onKeyDown={handleKeyPress}
                  placeholder="Ask anything"
                  rows={1}
                  className="pt-3 px-4 md:px-5 pb-10 pr-12 w-full min-h-[104px] max-h-[250px] overflow-y-auto text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base shadow-lg resize-none reset-height hide-scrollbar"
                  style={{
                    boxSizing: "border-box",
                    zIndex: 1001,
                    position: "relative",
                  }}
                />
                <button
                  type="submit"
                  disabled={message.trim().length === 0}
                  className={`absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation ${
                    message.trim().length === 0
                      ? "bg-[#D4D6CE] text-white cursor-not-allowed"
                      : "bg-black text-white hover:bg-gray-800"
                  }`}
                >
                  <FaArrowUp className="w-4 h-4 text-white" />
                </button>
              </form>
              <div className="text-center mt-2">
                <p className="text-xs text-gray-500">
                  This chat is powered by{" "}
                  <strong className="text-black">Driply.me</strong>
                </p>
              </div>
            </div>
          </>
        )}
      </div>

      {/* Mobile/Tablet Layout (<1024px) */}
      <div className="lg:hidden overflow-hidden fixed inset-0 flex flex-col  mt-10 ">
        {messages.length === 0 ? (
          <>
            <div className="flex flex-col items-center pt-15 pb-4 px-4">
              <div className="hidden md:flex lg:hidden flex-col items-center"></div>
            </div>
            <div
              className="flex flex-col items-center justify-center flex-1 px-4"
              style={{ paddingBottom: "260px" }}
            >
              <h1
                className={`text-2xl md:text-4xl text-gray-900 text-center leading-relaxed transition-all duration-700 ${
                  showInitialUI
                    ? "opacity-100 translate-y-0"
                    : "opacity-0 translate-y-4"
                }`}
                style={{ transitionDelay: showInitialUI ? "40ms" : "0ms" }}
              >
                How can I help you?
              </h1>
            </div>
          </>
        ) : (
          <>
            {/* Mobile Messages Container (< 768px) */}
            <div
              ref={mobileMessagesContainerRef}
              className="md:hidden flex-1 overflow-y-auto max-h-[calc(100vh-190px)] hide-scrollbar"
              style={{ overscrollBehavior: "contain" }}
            >
              <div className="w-full max-w-[803px] mx-auto px-4  pt-12">
                {renderMessagePairs()}
                <div ref={messagesEndRef} />
              </div>
            </div>

            {/* Tablet Messages Container (768px - 1023px) */}
            <div
              ref={tabletMessagesContainerRef}
              className="hidden md:block lg:hidden flex-1 overflow-y-auto max-h-[calc(100vh-100px)]  pb-20 hide-scrollbar"
              style={{ overscrollBehavior: "contain" }}
            >
              <div className="w-full max-w-[803px] mx-auto px-4 pb-6 pt-12">
                {renderMessagePairs()}
                <div ref={messagesEndRef} />
              </div>
            </div>

            <div
              className="fixed bottom-0 left-0 right-0 bg-white"
              style={{
                minHeight: "160px",
                zIndex: 1000,
                paddingBottom: "env(safe-area-inset-bottom, 0)",
                transform: "translateZ(0)",
                backfaceVisibility: "hidden",
              }}
            >
              <div
                className="px-4 py-4 bg-white"
                style={{ transform: "translateZ(0)" }}
              >
                <form
                  onSubmit={handleSubmit}
                  className="relative bg-white"
                  style={{ zIndex: 1001 }}
                >
                  <div
                    className="relative w-full max-w-[890px] mx-auto bg-white"
                    style={{ transform: "translateZ(0)" }}
                  >
                    <textarea
                      ref={messages.length > 0 ? inputRef : null}
                      value={message}
                      onChange={handleInputChange}
                      onKeyDown={handleKeyPress}
                      placeholder="Ask anything"
                      rows={1}
                      className="w-full min-h-[104px] pt-3 px-4 pb-10 pr-12 text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base resize-none overflow-y-auto reset-height"
                      style={{
                        boxSizing: "border-box",
                        maxHeight: "250px",
                        zIndex: 1001,
                        overscrollBehavior: "none",
                        position: "relative", // Ensure textarea stays in place
                      }}
                    />
                    <button
                      type="submit"
                      disabled={message.trim().length === 0}
                      className={`absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation ${
                        message.trim().length === 0
                          ? "bg-[#E0E0E0] text-white cursor-not-allowed"
                          : "bg-black text-white hover:bg-gray-800"
                      }`}
                    >
                      <FaArrowUp className="w-4 h-4 text-white" />
                    </button>
                  </div>
                </form>
                <div className="text-center py-2">
                  <p className="text-xs text-gray-500">
                    This chat is powered by{" "}
                    <strong className="text-black">Driply.me</strong>
                  </p>
                </div>
              </div>
            </div>
          </>
        )}

        {/* Mobile/Tablet Bottom Section */}
        <div className="absolute bottom-0 left-0 right-0 bg-white">
          {messages.length === 0 && (
            <>
              {/* Suggestion Cards Section */}
              <div
                className={`${
                  isMobile ? "px-0" : "px-4"
                } pt-2 pb-2 transition-all duration-500 ease-in-out ${
                  isTyping
                    ? "opacity-0 pointer-events-none"
                    : showInitialUI
                    ? "opacity-100 translate-y-0"
                    : "opacity-0 translate-y-4"
                }`}
                style={{
                  transitionDelay: showInitialUI ? "120ms" : "0ms",
                  position: "relative",
                  zIndex: 10,
                  backgroundColor: "white",
                  paddingBottom: "10px",
                }}
              >
                <div className={`overflow-hidden ${isMobile ? 'px-4' : 'px-12'}`}>
                  <div
                    ref={carouselRef}
                    className="flex gap-2 transition-transform duration-300 ease-in-out justify-start"
                    style={{
                      transform: `translateX(-${(currentSlide + (settingData?.suggestedTopics?.length || 0)) * (isMobile ? 180 : 280)}px)`,
                      paddingLeft: "0",
                    }}
                    onTouchStart={handleTouchStart}
                    onTouchMove={handleTouchMove}
                    onTouchEnd={handleTouchEnd}
                  >
                    {/* Create circular effect by duplicating cards */}
                    {(() => {
                      const cards = settingData?.suggestedTopics || [];
                      if (cards.length === 0) return null;

                      // Create circular array: [...cards, ...cards, ...cards] for smooth infinite scroll
                      const circularCards = [...cards, ...cards, ...cards];

                      return circularCards.map((card, cardIndex) => (
                        <div
                          key={`${cardIndex}-${card.question}`}
                          className={`flex-shrink-0 ${isMobile ? 'mt-3 mr-2' : 'mr-2'}`}
                        >
                          <button
                            onClick={() =>
                              handleSuggestionClick(card.question)
                            }
                            className={`${isMobile ? 'py-3' : 'py-2'} px-2.5 rounded-[8px] bg-[#F6F6F6] text-left hover:bg-[#D6EDFF] transition-all duration-200 inline-block touch-manipulation`}
                            style={{
                              minWidth: "fit-content",
                              maxWidth: isMobile ? "280px" : "300px",
                              width: "auto"
                            }}
                          >
                            <div className={`${isMobile ? 'text-[16px]' : 'text-[14px]'} font-[600] text-black mb-0.5 leading-tight`}>
                              {card.question}
                            </div>
                            <div className="text-[16px] text-gray-500 leading-tight">
                              {card.subtitle}
                            </div>
                          </button>
                        </div>
                      ));
                    })()}
                  </div>
                </div>

                {/* Navigation Buttons */}
                <button
                  onClick={handlePrevSlide}
                  className="hidden md:flex absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-10 w-8 h-8 bg-white border border-gray-200 rounded-full items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation z-10"
                >
                  <FaChevronLeft className="w-3 h-3 text-gray-600" />
                </button>
                <button
                  onClick={handleNextSlide}
                  className="hidden md:flex absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-10 w-8 h-8 bg-white border border-gray-200 rounded-full items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation z-10"
                >
                  <FaChevronRight className="w-3 h-3 text-gray-600" />
                </button>
              </div>

              {/* Input Section */}
              <div
                className={`px-4 bg-white transition-all duration-500 ${
                  showInitialUI ? "opacity-100 translate-y-0" : "translate-y-4"
                }`}
                style={{
                  transitionDelay: showInitialUI ? "200ms" : "0ms",
                  position: "relative",
                  zIndex: 5,
                  paddingBottom: "env(safe-area-inset-bottom)",
                }}
              >
                <form onSubmit={handleSubmit} className="relative">
                  <div className="relative w-full max-w-[890px] mx-auto">
                    <textarea
                      ref={messages.length === 0 ? inputRef : null}
                      value={message}
                      onChange={handleInputChange}
                      onKeyDown={handleKeyPress}
                      placeholder="Ask anything"
                      rows={1}
                      className="w-full min-h-[104px] pt-3 px-4 pb-10 pr-12 text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base resize-none overflow-y-auto reset-height"
                      style={{
                        boxSizing: "border-box",
                        maxHeight: "250px",
                        fontSize: "16px",
                        zIndex: 1001,
                        overscrollBehaviorY: "contain", // Prevent vertical overscroll affecting layout
                        position: "relative", // Ensure textarea stays in its container
                      }}
                    />
                    <button
                      type="submit"
                      disabled={message.trim().length === 0}
                      className={`absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation ${
                        message.trim().length === 0
                          ? "bg-[#D4D6CE] text-white cursor-not-allowed"
                          : "bg-black text-white hover:bg-gray-800"
                      }`}
                    >
                      <FaArrowUp className="w-4 h-4 text-white" />
                    </button>
                  </div>
                </form>
                <div className="text-center py-2">
                  <p className="text-xs text-gray-500">
                    This chat is powered by{" "}
                    <strong className="text-black">Driply.me</strong>
                  </p>
                </div>
              </div>
            </>
          )}
        </div>
      </div>

      {/* CSS to enforce max-height and prevent overlap */}
      <style jsx>{`
        /* Loading dot animations */
        :global(.loading-dot) {
          animation: loading-dots 1.4s infinite ease-in-out both;
        }

        :global(.loading-dot:nth-child(1)) {
          animation-delay: -0.32s;
        }

        :global(.loading-dot:nth-child(2)) {
          animation-delay: -0.16s;
        }

        :global(.loading-dot:nth-child(3)) {
          animation-delay: 0s;
        }

        /* Unified zoom in/out animation for all screen sizes */
        @keyframes loading-dots {
          0%,
          80%,
          100% {
            transform: scale(1);
            opacity: 1;
          }
          40% {
            transform: scale(1.5);
            opacity: 0.7;
          }
        }

        /* Global scrollbar hiding styles */
        :global(.hide-scrollbar) {
          -ms-overflow-style: none !important; /* IE and Edge */
          scrollbar-width: none !important; /* Firefox */
        }
        :global(.hide-scrollbar::-webkit-scrollbar) {
          display: none !important; /* Chrome, Safari and Opera */
        }

        @media only screen and (max-width: 1023px) {
          /* Fix for mobile keyboard pushing textarea */
          .lg\\:hidden .absolute.bottom-0 {
            position: fixed !important;
            bottom: env(safe-area-inset-bottom, 0);
            left: 0;
            right: 0;
            width: 100%;
            box-sizing: border-box;
            z-index: 1000;
            background-color: white;
          }

          /* Enhanced textarea styles for all mobile devices */
          textarea {
            -webkit-user-select: auto !important;
            user-select: auto !important;
            -webkit-appearance: none;
            appearance: none;
            overscroll-behavior: none;
            line-height: 24px;
            max-height: 250px !important;
            position: relative !important;
            transform: translateZ(0); /* Force hardware acceleration */
            backface-visibility: hidden;
            perspective: 1000;
            z-index: 1001 !important;
            background-color: white;
          }

          /* Form container fixes */
          .lg\\:hidden form {
            position: relative !important;
            z-index: 1001;
            background-color: white;
          }

          /* Ensure submit button stays visible */
          button[type="submit"] {
            position: absolute !important;
            right: 12px;
            bottom: 12px;
            z-index: 1002 !important;
            -webkit-tap-highlight-color: transparent;
            touch-action: manipulation;
            transform: translateZ(0);
          }

          /* Prevent any background content from showing through */
          .lg\\:hidden .bg-white {
            background-color: white !important;
          }
        }

        /* Desktop-specific styles */
        @media only screen and (min-width: 1024px) {
          textarea {
            max-height: 250px !important;
            overflow-y: auto !important;
            overscroll-behavior: contain;
          }
          .reset-height {
            min-height: 104px !important;
            max-height: 250px !important;
            overflow-y: auto !important;
          }
        }

        /* Common styles for better mobile handling */
        .fixed-bottom {
          position: fixed !important;
          bottom: 0;
          left: 0;
          right: 0;
          background: white;
        }
      `}</style>
    </div>
  );
};

export default ChatInterface;
