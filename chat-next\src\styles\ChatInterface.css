/* ChatInterface Styles */
/* Contains all the complex CSS animations and mobile optimizations */

/* Loading dot animation */
@keyframes loading-dot {
  0%, 20% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
  80%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
}

.loading-dot {
  animation: loading-dot 1.4s infinite ease-in-out;
}

.loading-dot:nth-child(1) {
  animation-delay: 0s;
}

.loading-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.loading-dot:nth-child(3) {
  animation-delay: 0.4s;
}

/* Scrollbar hiding for webkit browsers */
.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

.hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* Mobile keyboard handling and viewport fixes */
@media screen and (max-width: 767px) {
  /* Prevent zoom on input focus */
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="number"],
  input[type="tel"],
  input[type="url"],
  input[type="search"],
  textarea,
  select {
    font-size: 16px !important;
    transform: translateZ(0);
    -webkit-appearance: none;
    border-radius: 0;
  }

  /* Mobile viewport height fix */
  .mobile-vh-fix {
    height: 100vh;
    height: -webkit-fill-available;
  }

  /* Mobile safe area handling */
  .mobile-safe-area {
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Touch action optimization */
  .touch-manipulation {
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  /* Prevent overscroll bounce */
  .prevent-overscroll {
    overscroll-behavior: contain;
    -webkit-overflow-scrolling: touch;
  }
}

/* Desktop and tablet specific styles */
@media screen and (min-width: 768px) {
  /* Custom scrollbar for desktop */
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
}

/* Carousel specific styles */
.carousel-container {
  position: relative;
  overflow: hidden;
}

.carousel-track {
  display: flex;
  transition: transform 300ms ease-in-out;
}

.carousel-card {
  flex-shrink: 0;
  transition: all 200ms ease-in-out;
}

.carousel-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Message pair animations */
.message-pair {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Button hover effects */
.nav-button {
  transition: all 200ms ease-in-out;
}

.nav-button:hover:not(:disabled) {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.nav-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Input focus effects */
.input-focus:focus {
  outline: none;
  ring: 2px;
  ring-color: #3b82f6;
  border-color: transparent;
}

/* Loading spinner animation */
.spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive text sizing */
@media screen and (max-width: 640px) {
  .responsive-text-sm {
    font-size: 14px;
  }
  
  .responsive-text-base {
    font-size: 16px;
  }
}

@media screen and (min-width: 641px) {
  .responsive-text-sm {
    font-size: 14px;
  }
  
  .responsive-text-base {
    font-size: 16px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .carousel-card {
    border: 2px solid #000;
  }
  
  .nav-button {
    border: 2px solid #000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .carousel-track,
  .message-pair,
  .nav-button,
  .loading-dot {
    animation: none;
    transition: none;
  }
}

/* Loading dot animations - moved from inline styles */
.loading-dot {
  animation: loading-dots 1.4s infinite ease-in-out both;
}

.loading-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dot:nth-child(2) {
  animation-delay: -0.16s;
}

.loading-dot:nth-child(3) {
  animation-delay: 0s;
}

/* Unified zoom in/out animation for all screen sizes */
@keyframes loading-dots {
  0%,
  80%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  40% {
    transform: scale(1.5);
    opacity: 0.7;
  }
}

/* Global scrollbar hiding styles */
.hide-scrollbar {
  -ms-overflow-style: none !important; /* IE and Edge */
  scrollbar-width: none !important; /* Firefox */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none !important; /* Chrome, Safari and Opera */
}

/* Mobile-specific layout fixes */
@media only screen and (max-width: 1023px) {
  /* Fix for mobile keyboard pushing textarea */
  .lg\:hidden .absolute.bottom-0 {
    position: fixed !important;
    bottom: env(safe-area-inset-bottom, 0);
    left: 0;
    right: 0;
    width: 100%;
    box-sizing: border-box;
    z-index: 1000;
    background-color: white;
  }

  /* Enhanced textarea styles for all mobile devices */
  textarea {
    -webkit-user-select: auto !important;
    user-select: auto !important;
    -webkit-appearance: none;
    appearance: none;
    overscroll-behavior: none;
    line-height: 24px;
    min-height: 104px !important;
    max-height: 104px !important;
    height: 104px !important;
    resize: none !important;
    overflow-y: auto !important;
    position: relative !important;
    transform: translateZ(0); /* Force hardware acceleration */
    backface-visibility: hidden;
    perspective: 1000;
    z-index: 1001 !important;
    background-color: white;
  }

  /* Form container fixes */
  .lg\:hidden form {
    position: relative !important;
    z-index: 1001;
    background-color: white;
  }

  /* Ensure submit button stays visible */
  button[type="submit"] {
    position: absolute !important;
    right: 12px;
    bottom: 12px;
    z-index: 1002 !important;
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
    transform: translateZ(0);
  }

  /* Prevent any background content from showing through */
  .lg\:hidden .bg-white {
    background-color: white !important;
  }
}

/* Desktop-specific styles */
@media only screen and (min-width: 1024px) {
  textarea {
    min-height: 104px !important;
    max-height: 104px !important;
    height: 104px !important;
    overflow-y: auto !important;
    resize: none !important;
    overscroll-behavior: contain;
  }
  
  .reset-height {
    min-height: 104px !important;
    max-height: 104px !important;
    height: 104px !important;
    overflow-y: auto !important;
    resize: none !important;
  }
}

/* Common styles for better mobile handling */
.fixed-bottom {
  position: fixed !important;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
}
