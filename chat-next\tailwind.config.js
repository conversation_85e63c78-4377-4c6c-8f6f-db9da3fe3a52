/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        background: "var(--background)",
        foreground: "var(--foreground)",
      },
    },
  },
  plugins: [],
  // Safelist classes that might be used dynamically
  safelist: [
    // Message styling classes
    'message-item',
    'space-y-6',
    'flex',
    'justify-start',
    'justify-end',
    'bg-gray-100',
    'bg-white',
    'rounded-3xl',
    'rounded-[15px]',
    'px-3',
    'py-2',
    'px-4',
    'py-3',
    'text-[16px]',
    'font-[400]',
    'max-w-xs',
    'max-w-lg',
    'break-words',
    'hyphens-auto',
    'loading-dot',
    'w-2',
    'h-2',
    'bg-black',
    'rounded-full',
    'space-x-1',
    // Container classes
    'overflow-y-auto',
    'hide-scrollbar',
    'h-screen',
    'flex-col',
    'flex-1',
    'flex-shrink-0',
    'border-t',
    'border-gray-100',
    // Layout classes
    'fixed',
    'bottom-0',
    'left-0',
    'right-0',
    'relative',
    'z-1000',
    // Responsive classes
    'md:hidden',
    'lg:hidden',
    'lg:max-w-lg',
    'lg:w-[768px]',
    // Dynamic height classes
    'h-15',
    'mt-14',
    'mt-7',
    'pb-20',
    'pt-12',
    'pb-6',
    // Animation classes
    'transition',
    'opacity',
    'ease-in-out',
  ]
};
